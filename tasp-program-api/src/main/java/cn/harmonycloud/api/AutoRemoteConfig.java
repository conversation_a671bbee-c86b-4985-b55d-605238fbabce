package cn.harmonycloud.api;

import org.springframework.context.annotation.Configuration;

/**
 * @apiNote  配置远程客户端
 */
@Configuration
public class AutoRemoteConfig {

//    /**
//     * 手动构造feign的bean
//     * ConditionalOnMissingBean控制可以直接调用controller
//     *
//     * @return
//     */
//    @Bean
//    @ConditionalOnMissingBean(UserRemoteClient.class)
//    public UserRemoteClient userRemoteClient(RemoteClientProperties remoteClientProperties) {
//        return Feign.builder()
//                .client(new ApacheHttpClient())
//                .encoder(new JacksonEncoder())
//                .decoder(new JacksonDecoder())
//                .target(UserRemoteClient.class, remoteClientProperties.getClient().get(UserRemoteClient.getRemoteClientKey()));
//    }

}
