package cn.harmonycloud.pmp.util;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;

import java.util.*;
import java.util.regex.Pattern;

@UtilityClass
public class IPUtil {
    // IP的正则
    private static final Pattern pattern = Pattern
            .compile("(1\\d{1,2}|2[0-4]\\d|25[0-5]|\\d{1,2})\\." + "(1\\d{1,2}|2[0-4]\\d|25[0-5]|\\d{1,2})\\."
                    + "(1\\d{1,2}|2[0-4]\\d|25[0-5]|\\d{1,2})\\." + "(1\\d{1,2}|2[0-4]\\d|25[0-5]|\\d{1,2})");
    public static final String DEFAULT_ALLOW_ALL_FLAG = "*";// 允许所有ip标志位
    public static final String DEFAULT_DENY_ALL_FLAG = "0"; // 禁止所有ip标志位


    /**
     * 将ip网段划分成最大值和最小值
     * @param ip
     * @return
     */
    public static Map<String,Long> getIpGap(String ip){
        Map<String,Long> result = new HashMap<>();
        if(!ip.contains("/")){
            result.put("minIp",ipToLong(ip));
            result.put("maxIp",ipToLong(ip));
            return result;
        }
        Integer replace = 0;
        try{
            String segment = ip.split("/")[1];
            replace = Integer.parseInt(segment)/8;
        }catch (Exception e){
            throw new BusinessException("类型转换错误");
        }
        //将127.0.0.1/24 -> 127.0.0.*
        ip = ip.split("/")[0];
        String[] ips = ip.split("\\.");
        for (int i = ips.length-1; i >= replace; i--) {
            ips[i] = "*";
        }

        String[] from = new String[] { "0", "0", "0", "0" };
        String[] end = new String[] { "255", "255", "255", "255" };
        List<String> tem = new ArrayList<String>();
        for (int i = 0; i < ips.length; i++)
            if (ips[i].indexOf("*") > -1) {
                tem = complete(ips[i]);
                from[i] = null;
                end[i] = null;
            } else {
                from[i] = ips[i];
                end[i] = ips[i];
            }

        StringBuilder fromIP = new StringBuilder();
        StringBuilder endIP = new StringBuilder();
        for (int i = 0; i < 4; i++)
            if (from[i] != null) {
                fromIP.append(from[i]).append(".");
                endIP.append(end[i]).append(".");
            } else {
                fromIP.append("[*].");
                endIP.append("[*].");
            }
        fromIP.deleteCharAt(fromIP.length() - 1);
        endIP.deleteCharAt(endIP.length() - 1);

        for (String s : tem) {
             result.put("minIp",ipToLong(fromIP.toString().replace("[*]", s.split(";")[0])));
             result.put("maxIp",ipToLong(endIP.toString().replace("[*]", s.split(";")[1])));
        }

        return result;
    }

    /**
     * @date 2017-4-17 下午02:50:20
     * @param allowIp
     * @return
     */

    public static Set<String> getIpList(String allowIp) {
        String[] splitRex = allowIp.split(";");// 拆分出白名单正则
        Set<String> ipList = new HashSet<String>(splitRex.length);
        for (String allow : splitRex) {
            if (allow.contains("*")) {// 处理通配符 *
                String[] ips = allow.split("\\.");
                String[] from = new String[] { "0", "0", "0", "0" };
                String[] end = new String[] { "255", "255", "255", "255" };
                List<String> tem = new ArrayList<String>();
                for (int i = 0; i < ips.length; i++)
                    if (ips[i].indexOf("*") > -1) {
                        tem = complete(ips[i]);
                        from[i] = null;
                        end[i] = null;
                    } else {
                        from[i] = ips[i];
                        end[i] = ips[i];
                    }

                StringBuilder fromIP = new StringBuilder();
                StringBuilder endIP = new StringBuilder();
                for (int i = 0; i < 4; i++)
                    if (from[i] != null) {
                        fromIP.append(from[i]).append(".");
                        endIP.append(end[i]).append(".");
                    } else {
                        fromIP.append("[*].");
                        endIP.append("[*].");
                    }
                fromIP.deleteCharAt(fromIP.length() - 1);
                endIP.deleteCharAt(endIP.length() - 1);

                for (String s : tem) {
                    String ip = fromIP.toString().replace("[*]", s.split(";")[0]) + "-"
                            + endIP.toString().replace("[*]", s.split(";")[1]);
                    if (validate(ip)) {
                        ipList.add(ip);
                    }
                }
            } else if (allow.contains("/")) {// 处理 网段 xxx.xxx.xxx./24
                ipList.add(allow);
            } else {// 处理单个 ip 或者 范围
                if (validate(allow)) {
                    ipList.add(allow);
                }
            }

        }

        return ipList;
    }

    /**
     * 对单个IP节点进行范围限定
     *
     * @param arg
     * @return 返回限定后的IP范围，格式为List[10;19, 100;199]
     */
    private static List<String> complete(String arg) {
        List<String> com = new ArrayList<String>();
        int len = arg.length();
        if (len == 1) {
            com.add("0;255");
        } else if (len == 2) {
            String s1 = complete(arg, 1);
            if (s1 != null)
                com.add(s1);
            String s2 = complete(arg, 2);
            if (s2 != null)
                com.add(s2);
        } else {
            String s1 = complete(arg, 1);
            if (s1 != null)
                com.add(s1);
        }
        return com;
    }

    private static String complete(String arg, int length) {
        String from = "";
        String end = "";
        if (length == 1) {
            from = arg.replace("*", "0");
            end = arg.replace("*", "9");
        } else {
            from = arg.replace("*", "00");
            end = arg.replace("*", "99");
        }
        if (Integer.valueOf(from) > 255)
            return null;
        if (Integer.valueOf(end) > 255)
            end = "255";
        return from + ";" + end;
    }

    /**
     * 在添加至白名单时进行格式校验
     *
     * @param ip
     * @return
     */
    public static boolean validate(String ip) {
        if(StrUtil.isBlank(ip)){
            return false;
        }
        return pattern.matcher(ip).matches();
    }

    /**
     *
     * isPermited:(根据IP,及可用Ip列表来判断ip是否包含在白名单之中).
     *
     * @date 2017-4-17 下午03:01:03
     * @param ip
     * @param ipList
     * @return
     */
    public static boolean isIPInSubnet(String ip, Set<String> ipList) {
        if (ipList.isEmpty())
            return false;
        if(ipList.contains(ip)){
            return true;
        }
        if(!validate(ip)){
            //针对 ip 是网段 ***********/24 ，黑名单是ip的情况
            for(String blackIp:ipList){
                if(validate(blackIp) && checkIpInSegment(blackIp,ip)){
                    return true;
                }
            }
            return false;
        }else{
            for (String allow : ipList) {
                return checkIpInSegment(ip,allow);
            }
        }
        return false;
    }

    private static boolean checkIpInSegment(String ip, String allow) {
        if (allow.contains("/")) {// 处理 网段 xxx.xxx.xxx.xxx/24
            int splitIndex = allow.indexOf("/");
            // 子网数
            String netmask = allow.substring(splitIndex + 1);// 24
            // ip 转二进制
            long ipLong = ipToLong(ip);
            //子网二进制
            long maskLong=(2L<<32 -1) -(2L << Integer.valueOf(32-Integer.valueOf(netmask))-1);
            // 取出子网段
            String ipSegment = longToIP(ipToLong(allow.substring(0,splitIndex)) & maskLong);
            // ip与和子网相与 得到 网络地址
            String calcSegment = longToIP(ipLong & maskLong);
            // 如果计算得出网络地址和库中网络地址相同 则合法
            return ipSegment.equals(calcSegment);
        }
        return false;
    }

    public static long ipToLong(String strIP) {
        long[] ip = new long[4];
        // 先找到IP地址字符串中.的位置
        int position1 = strIP.indexOf(".");
        int position2 = strIP.indexOf(".", position1 + 1);
        int position3 = strIP.indexOf(".", position2 + 1);
        // 将每个.之间的字符串转换成整型
        ip[0] = Long.parseLong(strIP.substring(0, position1));
        ip[1] = Long.parseLong(strIP.substring(position1 + 1, position2));
        ip[2] = Long.parseLong(strIP.substring(position2 + 1, position3));
        ip[3] = Long.parseLong(strIP.substring(position3 + 1));
        return (ip[0] << 24) + (ip[1] << 16) + (ip[2] << 8) + ip[3];
    }

    // 将10进制整数形式转换成127.0.0.1形式的IP地址
    private static String longToIP(long longIP) {
        StringBuilder sb = new StringBuilder();
        // 直接右移24位
        sb.append(longIP >>> 24);
        sb.append(".");
        // 将高8位置0，然后右移16位
        sb.append((longIP & 0x00FFFFFF) >>> 16);
        sb.append(".");
        sb.append((longIP & 0x0000FFFF) >>> 8);
        sb.append(".");
        sb.append(longIP & 0x000000FF);
        return sb.toString();
    }

    public static void main(String[] args) {
        Set set = new HashSet();
        set.add("*********/24");
        System.out.println(isIPInSubnet("*********",set));
    }
}
