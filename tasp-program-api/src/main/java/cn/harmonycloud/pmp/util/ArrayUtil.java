package cn.harmonycloud.pmp.util;

import cn.hutool.core.util.ObjectUtil;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@UtilityClass
public class ArrayUtil {


    public <T> List<T> asList(T... obj){
        List<T> result = Arrays.asList(obj);
        return result.stream().filter(index-> ObjectUtil.isNotNull(index)).collect(Collectors.toList());
    }
}
