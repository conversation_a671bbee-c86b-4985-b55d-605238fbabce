package cn.harmonycloud.pmp.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

public class SpringContextUtils implements ApplicationContextAware {
    private static ApplicationContext context;
    @Override
    public void setApplicationContext(ApplicationContext context)
            throws BeansException {

        SpringContextUtils.context = context;
    }
    public static ApplicationContext getContext(){
        return context;
    }
}