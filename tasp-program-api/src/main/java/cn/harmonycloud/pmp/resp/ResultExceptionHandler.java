package cn.harmonycloud.pmp.resp;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * <AUTHOR>
 * @date 2021-04-27 14:41
 */
@Slf4j
@RestControllerAdvice
public class ResultExceptionHandler {

    @ExceptionHandler(ResultException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<String> handleResultException(ResultException e) {
        log.error("Result exception: [{}]", e.getMessage(), e);
        return R.failure(e.resultInterface.getMessage());
    }


}
