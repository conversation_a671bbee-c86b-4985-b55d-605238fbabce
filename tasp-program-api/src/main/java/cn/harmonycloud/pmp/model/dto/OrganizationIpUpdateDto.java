package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.OrganizationIp;
import cn.harmonycloud.pmp.resp.ResultEnum;
import cn.harmonycloud.pmp.util.IPUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value="租户白名单修改对象", description="租户ip白名单修改")
public class OrganizationIpUpdateDto implements Serializable {

    private static final long seriaVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "ip白名单")
    @NotEmpty(message = "ip白名单不能为为空")
    private String ipParam;

    public OrganizationIp getOrganIp(){
        if(ipParam.contains("/")){
            String[] ipArray = ipParam.split("/");
            ResultEnum.IP_REGEX_ERROR.isTrue(IPUtil.validate(ipArray[0]),ipParam);
        }else{
            ResultEnum.IP_REGEX_ERROR.isTrue(IPUtil.validate(ipParam),ipParam);
        }
        OrganizationIp organizationIp = new OrganizationIp();
        organizationIp.setId(this.id);
        organizationIp.setOrganId(this.organId);
        organizationIp.setIp(ipParam.trim());
        return organizationIp;
    }
}
