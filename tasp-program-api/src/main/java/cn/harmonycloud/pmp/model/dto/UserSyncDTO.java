package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.constant.DictCons;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:44 上午
 **/
@Data
@ApiModel(value = "外部用户同步传输")
public class UserSyncDTO {

    @ApiModelProperty(value = "账号", required = true)
    private String username;

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "员工编号")
    @NotBlank(message = "员工编号不能为空")
    private String employNum;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "行政组织id列表")
    private List<String> adminCodes;

    @ApiModelProperty(value = "roleIds")
    private List<Long> roleIds = Lists.newArrayList(DictCons.Role.BaseRole.getId());

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "租户id")
    private Long organId = 1L;

    @ApiModelProperty(value = "用户类型 1-公司内部员工 2-公司外部试用",required = true)
    private Integer userType =1 ;

}
