package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class OrganByUserVo implements Serializable {


    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "父类id")
    private Long parentId;

    @ApiModelProperty(value = "租户名称")
    private String organName;

    @ApiModelProperty(value = "租户编码")
    private String code;

}
