package cn.harmonycloud.pmp.model.dto.record;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: AddRecordDTO
 * @projectName src-message-svc
 * @date 2020/11/10 16:15
 */
@Data
public class DingTalkDTO implements Serializable {
    /**
     * @see cn.harmonycloud.pmp.Enum.DingTalkMessageTypeEnum
     */
    @ApiModelProperty(value = "钉钉消息类型 text,image,file,link,markdown,oa,action_card",hidden = true)
    private String dingMsgType;

    @ApiModelProperty(value = "钉钉消息 是否发送给全部，默认false",hidden = true)
    private Boolean dingSendAll=false;

    @ApiModelProperty(value = "钉钉消息 上传文件的mediaId",hidden = true)
    private String dingMsgMediaId;

    @ApiModelProperty(value = "钉钉消息 跳转地址",hidden = true)
    private String dingMsgActionUrl;

    @ApiModelProperty(value = "钉钉消息 跳转按钮文字",hidden = true)
    private String dingMsgActionTitle;

    @ApiModelProperty(value = "钉钉消息 OA头部文字",hidden = true)
    private String dingMsgOaHeader;

}
