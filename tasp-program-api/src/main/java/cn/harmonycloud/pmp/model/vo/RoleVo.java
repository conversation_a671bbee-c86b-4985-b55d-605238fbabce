package cn.harmonycloud.pmp.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@ApiModel(value="角色返回对象")
public class RoleVo implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "1-平台角色 2-平台模板角色 3-租户角色 4-租户管理员角色 5-资源集角色")
    private Integer type;

    @ApiModelProperty(value = "是否是管理员角色 0-否 1-是")
    private Integer bolAdmin;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "最底层的权限id")
    private List<Long> deepestPermissionIds;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty("是否可编辑")
    private Boolean editable;


}
