package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class UserResourcePermissionVo implements Serializable {

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "权限集合")
    Map<String,Object> permissionMap;

    public UserResourcePermissionVo(String resourceTypeCode, Long resourceInstanceId,
                                    Map<String, Object> permissionMap) {
        this.resourceTypeCode = resourceTypeCode;
        this.resourceInstanceId = resourceInstanceId;
        this.permissionMap = permissionMap;
    }
}
