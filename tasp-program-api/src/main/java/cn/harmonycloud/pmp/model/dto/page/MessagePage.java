package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.MessageText;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-26 11:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("消息分页对象")
public class MessagePage extends Pagination<MessageText> {

    private static final long serialVersionUID = 1357423035255559223L;
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "发送者名称")
    private String sender;

    @ApiModelProperty(value = "是否发布")
    private Boolean published;

    @ApiModelProperty(value = "消息来源")
    private Integer messageFrom;

    @ApiModelProperty(value = "状态")
    private List<Integer> status;
}
