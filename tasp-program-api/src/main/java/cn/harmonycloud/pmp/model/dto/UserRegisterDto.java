package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value = "注册用户")
public class UserRegisterDto implements Serializable {

    private static final long serialVersionUID = -7743086722283617513L;

    @NotBlank(message = "用户账号不能为空")
    @ApiModelProperty(value = "账号", required = true)
    private String username;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "员工编号")
    @NotBlank(message = "员工编号不能为空")
    private String employNum;

    @ApiModelProperty(value = "员工编号")
    private String userCode;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "是否首次登录 0-否 1-是")
    private Integer firstLogin = 1;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "租户id", hidden = true)
    private Long organId;

    @ApiModelProperty(value = "行政组织id列表")
    private List<Long> adminIds;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "用户类型 1-公司内部员工 2-公司外部试用",required = true)
    private Integer userType =1 ;

    @ApiModelProperty(value = "角色id集合")
    private List<Long> roleIds;
}
