package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-27 11:33 上午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("消息展示")
public class MessageVO  implements Serializable {
    private static final long serialVersionUID = -5396547869131098979L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "接受者ID，0表示接受者为所有人")
    private List<Long> receiveIds;

    @ApiModelProperty(value = "接受者名称")
    private List<String> receiveNames;

    @ApiModelProperty(value = "是否发布")
    private Boolean published;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "消息类型Id")
    private Integer messageFrom;

    @ApiModelProperty(value = "状态 见MessageStatus")
    private Integer status;

    @ApiModelProperty(value = "是否发送全部")
    private Boolean sendAll = false;

    @ApiModelProperty(value = "收件租户id")
    private List<Long> organIds;

    @ApiModelProperty(value = "收件项目id")
    private List<Long> projectIds;

    @ApiModelProperty(value = "收件用户id")
    private List<Long> userIds;
}
