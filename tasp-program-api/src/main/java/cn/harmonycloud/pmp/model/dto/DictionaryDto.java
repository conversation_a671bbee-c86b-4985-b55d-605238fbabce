package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class DictionaryDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;

    @ApiModelProperty(value = "字典码")
    private String code;

    @ApiModelProperty(value = "显示名")
    private String name;

    @ApiModelProperty(value = "应用id")
    private Long appId;

    @ApiModelProperty(value = "存储值")
    private String value;

    @ApiModelProperty(value = "备注")
    private String description;

}
