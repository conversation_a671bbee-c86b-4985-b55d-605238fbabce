package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class OwnerResourceRoleDTO implements Serializable {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "资源类型集合")
    private List<String> resourceTypeCodeList;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源拥有者类型 1-人员 2-角色 支持子应用扩展")
    private String typeDictValue;

    @ApiModelProperty(value = "资源拥有者id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "资源实例名称")
    private String resourceInstanceName;

    @ApiModelProperty(value = "父类资源id",hidden = true)
    private Long parentResourceId;

    @ApiModelProperty(value = "父类资源类型")
    private String parentResourceTypeCode;

    @ApiModelProperty(value = "父类应用资源id")
    private Long parentResourceInstanceId;

    public  ResourceRole toResourceRole() {
        ResourceRole resourceRole =new ResourceRole();
        BeanUtils.copyProperties(this,resourceRole);
        return resourceRole;
    }

}
