package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Web;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-24
 */
@Data
@EqualsAndHashCode(callSuper = true)

public class WebPage extends Pagination<Web> {
    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "名称", required = true)
//    private String name;
//
//    @ApiModelProperty(value = "编号", required = true)
//    private String code;

    @ApiModelProperty(value = "微前端类型：1.vue 2.react")
    private Integer type;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "复合查询参数")
    private String queryParam;

    @ApiModelProperty(value = "复合查询参数集合")
    private List<String> queryParams;

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }

}
