package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021-07-27 2:58 下午
 */
@Data
@ApiModel("收件箱详情")
public class MessageInboxVO{

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "状态0 未读 1已读")
    private Integer status;

    @ApiModelProperty(value = "发送人id")
    private Long senderId;

    @ApiModelProperty(value = "发送人")
    private String sender;

    @ApiModelProperty(value = "发送时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "消息来源")
    private Integer messageFrom;

    @ApiModelProperty(value = "云服务产品")
    private String cloudserviceName;

    @ApiModelProperty(value = "消息来源")
    private String messageType;

    @ApiModelProperty(value = "总数")
    private Integer unReadCount;
}
