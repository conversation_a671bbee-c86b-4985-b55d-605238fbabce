package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.PermissionTreeNode;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色表
 * </p>
 *ø
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Role复合对象")
public class RoleDto extends ResourceRole implements Serializable {


    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "主键ID集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "1.平台角色 2.租户共享角色 3.全局角色 4.资源类型角色 5.租户角色")
    private Integer type;

    @ApiModelProperty(value = "是否可改 0不可 1可以")
    private Boolean editable;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "权限类型")
    private Integer permissionKind;

    @ApiModelProperty(value = "权限树节点集合")
    private List<PermissionTreeNode> permissionTreeNodes;

    @ApiModelProperty(value = "角色描述")
    private String description;

    @ApiModelProperty("扩展字段")
    private String annotations;

}
