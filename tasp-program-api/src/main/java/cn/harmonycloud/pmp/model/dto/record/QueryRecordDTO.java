package cn.harmonycloud.pmp.model.dto.record;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: AddRecordDTO
 * @projectName src-message-svc
 * @date 2020/11/10 16:15
 */
@Data
public class QueryRecordDTO implements Serializable {
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "筛选参数")
    private String queryParam;

    @ApiModelProperty(value = "发送起始时间")
    private String sendStartTime;

    @ApiModelProperty(value = "发送结束时间")
    private String sendEndTime;


}
