package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @create 2021-09-23
 */

@Data
@ApiModel(value = "修改用户")
public class UserUpdateDto implements Serializable {

    private static final long serialVersionUID = -7743086722283617513L;
    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别 0女 1男")
    private Integer gender;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "员工编号")
    private String employNum;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "是否可编辑")
    private Boolean editable;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "部门id")
    private Long departId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIds;

    @ApiModelProperty(value = "行政组织列表ids")
    private List<Long> administrativeIds;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "用户类型（1：内部，2：外部）")
    private Integer userType;

}