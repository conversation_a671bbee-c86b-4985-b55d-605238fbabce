package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Trigger;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("功能模块分页对象")
public class TriggerPage extends Pagination<Trigger> {

    @ApiModelProperty(value = "复合查询参数")
    private String queryParam;

    @ApiModelProperty(value = "功能模块id")
    private Long categoryId;

    @ApiModelProperty(value = "租户id")
    private Long organId;


}
