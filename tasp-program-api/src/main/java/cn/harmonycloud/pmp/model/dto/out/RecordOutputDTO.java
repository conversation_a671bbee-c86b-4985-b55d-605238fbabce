package cn.harmonycloud.pmp.model.dto.out;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: RecordOutputDTO
 * @projectName src-message-svc
 * @date 2020/11/4 14:16
 */
@Data
public class RecordOutputDTO {
    /**
     * 标题
     */
    private String title;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 收件人租户id
     */
    private String tenantId;
    /**
     * 收件人角色id
     */
    private String roleIds;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 消息类型（SMS/MAIL）
     */
    private String sendType;
    /**
     * 消息状态（NOT SEND/SUCCESS/FAILED）
     */
    private String status;
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 发送者名称
     */
    private String senderName;
    /**
     * 发送机制（DELAYED/REALTIME）
     */

    private String sendMode;
}
