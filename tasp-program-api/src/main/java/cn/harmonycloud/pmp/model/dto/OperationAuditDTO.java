package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationAuditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("操作审计类型 platform(0) ，business(1)")
    private Integer type;

    @ApiModelProperty("ipAddress")
    private String ipAddress;

    @ApiModelProperty("responseCode")
    private String responseCode;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("应用编号")
    private String appCode;

    @ApiModelProperty("操作开始时间，系统自动获取")
    private LocalDateTime operationStartTime;

    @ApiModelProperty("操作结束时间，系统自动获取")
    private LocalDateTime operationEndTime;

    @ApiModelProperty("操作用户ID")
    private String operationUserId;

    @ApiModelProperty("操作方法参数")
    private String param;

    @ApiModelProperty("操作请求类型GET/POST/DELETE")
    private String requestType;

    @ApiModelProperty("操作请求URL")
    private String requestUrl;

    @ApiModelProperty("操作方法")
    private String method;

    @ApiModelProperty("操作业务名称")
    private String operationName;

    @ApiModelProperty("操作返回值")
    private String response;

    @ApiModelProperty("操作结果")
    private String result;

    @ApiModelProperty("操作扩展记录结果")
    private String extendParam;

    @ApiModelProperty("操作类名称")
    private String className;

    private String traceId;

}
