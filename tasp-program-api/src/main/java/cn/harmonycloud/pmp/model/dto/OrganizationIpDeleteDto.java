package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value="租户白名单删除对象", description="租户ip白名单删除")
public class OrganizationIpDeleteDto implements Serializable {

    private static final long seriaVersionUID = 1L;

    @ApiModelProperty(value = "租户白名单ids")
    private List<Long> ids;

    @ApiModelProperty(value = "租户id")
    private Long organId;

}
