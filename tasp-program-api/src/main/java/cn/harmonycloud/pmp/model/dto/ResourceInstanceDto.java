package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.model.entity.ResourceScreening;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@ApiModel(value="ResourceInstance复合对象", description="ResourceInstance复合对象")
public class ResourceInstanceDto implements Serializable {


    @ApiModelProperty(value = "主键ID",hidden = true)
    private Long id;

    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    @NotBlank(message = "资源类型编码不能为空")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    @NotBlank(message = "应用资源id不能为空")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "应用资源名称")
    @NotBlank(message = "应用资源名称不能为空")
    private String resourceInstanceName;

    @ApiModelProperty(value = "父级资源类型编号")
    private String parentResourceTypeCode;

    @ApiModelProperty(value = "父级应用资源id")
    private Long parentResourceInstanceId;

    @ApiModelProperty(value = "应用ID",hidden = true)
    private Long appId;

    @ApiModelProperty(value = "应用ID",hidden = true)
    private String appCode;

    @ApiModelProperty(value = "扩展字段")
    private String extendField;

    @ApiModelProperty(value = "筛选表参数")
    List<ResourceScreening> resourceScreenings;

    @ApiModelProperty(value = "备注")
    private String description;


    public ResourceRole createResourceRole(){
        ResourceRole resourceRole = new ResourceRole();
        resourceRole.setAppId(this.appId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        return resourceRole;
    }

}
