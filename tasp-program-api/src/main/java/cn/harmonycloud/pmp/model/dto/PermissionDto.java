package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "权限复合参数对象")
public class PermissionDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "编码")
    @NotBlank(message = "编码不能为空")
    private String code;

    @ApiModelProperty(value = "类型 1.菜单 2.页面元素 3.tab页")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "资源类型编码")
    private String resourceTypeCode;

    @ApiModelProperty(value = "类型 1.菜单 2.页面元素 3.tab页")
    private List<Integer> types;

    @ApiModelProperty(value = "种类 1.平台菜单 2.租户菜单 3.资源菜单")
    private Integer kind;

    @ApiModelProperty(value = "1-可见 2-不可见")
    private Boolean visible;

    @ApiModelProperty(value = "应用ID")
    private Long appId;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "请求方式 1.GET 2.POST 3.PUT 4.DELETE")
    @NotNull(message = "请求方式不能为空")
    private Integer method;

    @ApiModelProperty(value = "接口列表")
    private String url;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "应用code")
    private String appCode;

    @ApiModelProperty(value = "是否是iframe")
    private Boolean isIframe;

    @ApiModelProperty(value = "iframe url地址")
    private String iframeUrl;

}
