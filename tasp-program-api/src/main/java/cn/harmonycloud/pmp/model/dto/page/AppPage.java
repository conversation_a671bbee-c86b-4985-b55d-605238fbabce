package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.App;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.minidev.json.annotate.JsonIgnore;

import java.util.Arrays;
import java.util.List;

@Data
@ApiModel("用户分页对象")
public class AppPage extends Pagination<App> {

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "复合查询参数")
    private String queryParam;

    @ApiModelProperty(value = "复合查询参数集合")
    @JsonIgnore
    private List<String> queryParams;

    public void setQueryParam(String queryParam) {
        if(StrUtil.isNotBlank(queryParam)){
            this.queryParams= Arrays.asList(queryParam.split(","));
        }
        this.queryParam=queryParam;
    }
}
