package cn.harmonycloud.pmp.model.dto.page;


import cn.harmonycloud.pmp.model.entity.OperationAuditPO;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationalAuditPage extends Pagination<OperationAuditPO> {

    @ApiModelProperty(value = "操作用户姓名")
    private String queryParam;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "跟踪编号",hidden = true)
    private String traceId;

    @ApiModelProperty(value = "应用编码")
    private String appCode;

    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    //@ApiModelProperty(value = "操作名称")
    //private String operationName;

    @ApiModelProperty(value = "审计实体主键值")
    private String operationId;

    @ApiModelProperty(value = "ip")
    private String ipAddress;

    @ApiModelProperty(value = "操作开始时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private String operationStartTime;

    @ApiModelProperty(value = "操作结束时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private String operationEndTime;

    //@ApiModelProperty(value = "操作用户名")
    //private String operationUserName;
    //
    //@ApiModelProperty(value = "URL")
    //private String requestUrl;

    @ApiModelProperty(value = "操作结果")
    private String result;



}
