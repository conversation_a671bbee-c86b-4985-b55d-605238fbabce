package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.entity.OrganizationBase;
import cn.harmonycloud.pmp.model.entity.RoleBase;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@ApiModel(value = "用户")
public class CopResourceUserVo implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "租户基础对象")
    private List<OrganizationBase> organizationBases;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机")
    @TableField("mobile")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty(value = "用户加入资源的时间")
    private String joinTime;

    @ApiModelProperty(value = "角色集合")
    private List<RoleBase> roles;

    @ApiModelProperty(value = "角色ID集合")
    private List<Long> roleIds;

}
