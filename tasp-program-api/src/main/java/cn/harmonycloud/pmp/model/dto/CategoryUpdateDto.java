package cn.harmonycloud.pmp.model.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: CategoryAddDto
 * @projectName amp
 * @date 2020/11/4 14:58
 */

@Data
public class CategoryUpdateDto {

    @ApiModelProperty(value = "功能模块ID",required = true)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private Long organId;

    @ApiModelProperty(value = "模块名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;
}
