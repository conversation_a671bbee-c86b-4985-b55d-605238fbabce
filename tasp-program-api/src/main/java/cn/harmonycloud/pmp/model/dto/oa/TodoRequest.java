package cn.harmonycloud.pmp.model.dto.oa;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @describe OA待办请求消息体
 * @author: wang<PERSON><PERSON>
 * @create: 2022-01-18 16:43:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TodoRequest {
    //异构系统标识
    @NotBlank(message = "缺少异构系统标识")
    private String syscode;
    //流程实例id
    @NotNull(message = "缺少流程实例ID")
    private String flowid;
    //标题
    private String requestname;
    //流程类型名称
    private String workflowname;
    //步骤名称（节点名称）
    private String nodename;
    //PC地址（非必传）
    private String pcurl;
    //APP地址（非必传）
    private String appurl;
    //创建人（原值）
    @NotBlank(message = "创建人不能为空")
    private String creator;
    //创建日期时间
    @NotEmpty(message = "创建时间不能为空 yyyy-MM-dd HH:mm:ss")
    private String createdatetime;
    //接收人，多接收人以英文逗号分隔
    @NotNull(message = "接收人不能为空")
    private String receiver;
    //接收日期时间
    //@NotEmpty(message = "接收时间不能为空")
    private String receivedatetime;
    //时间戳字段（非必传）
    private String receivets;
    //紧急程度（非必传）0：正常、1：重要、2：紧急
    private String requestlevel;

}
