package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021-05-26 11:55
 */
@Data
@ApiModel(value = "用户修改密码")
public class UserUpdatePasswordDto implements Serializable {

    private static final long serialVersionUID = -7743086722283617513L;

    @NotBlank(message = "旧密码不能为空")
    @ApiModelProperty(value = "旧密码")
    private String password;

    @NotBlank(message = "新密码不能为空")
    @ApiModelProperty(value = "新密码")
    private String newPassword;

    @NotBlank(message = "二次新密码不能为空")
    @ApiModelProperty(value = "二次新密码")
    private String secondNewPassword;
}
