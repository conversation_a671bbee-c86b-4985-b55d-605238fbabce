package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@ApiModel("审记前端页面赛选条件生成")
@Data
@Accessors(chain = true)
public class OperationAuditSelectorAuditVo {
    private List<OrganSelectorVo> organSelector;

    @Data
    @Accessors(chain = true)
    public static class OrganSelectorVo{
        @ApiModelProperty("租户ID")
        private String organId;

        @ApiModelProperty("租户名称")
        private String organName;
    }
}