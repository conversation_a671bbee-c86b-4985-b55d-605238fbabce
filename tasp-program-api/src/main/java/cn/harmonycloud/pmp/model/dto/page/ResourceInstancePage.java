package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.constant.DictCons;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;


@Data
@ApiModel("资源分页对象")
public class ResourceInstancePage extends Pagination<ResourceInstance> {

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化id",hidden = true)
    private Long resourceInstanceId;

    @ApiModelProperty(value = "资源拥有者类型 1-人员 2-角色 支持子应用扩展")
    private String typeDictValue = DictCons.ResourceOwnerType.User.getValue();

    @ApiModelProperty(value = "资源拥有者id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "父类资源id",hidden = true)
    private Long parentResourceId;

    @ApiModelProperty(value = "父类资源类型")
    private String parentResourceTypeCode;

    @ApiModelProperty(value = "父类应用资源id")
    private Long parentResourceInstanceId;

    @ApiModelProperty(value = "AMP资源id,子应用不要传这个参数",hidden = true)
    private Long resourceId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "应用id",hidden = true)
    private Long appId;

    @ApiModelProperty(value = "查询参数")
    private String queryParam;

    @ApiModelProperty(value = "拆分后的查询参数",hidden = true)
    private List<String> queryParams;

    @ApiModelProperty(value = "倒序排序")
    private Boolean desc=true;

    @ApiModelProperty(value = "资源id集合",hidden = true)
    private List<Long> resourceIds;

    @ApiModelProperty(value = "资源id实例集合")
    private List<Long> resourceInstanceIds;

    @ApiModelProperty(value = "根据埋点排序",hidden = true)
    private List<String> orderByIds;

    public String getOrderByIds() {
        return StrUtil.join(",",orderByIds);
    }

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }

    public ResourceRole createResourceRole(){
        ResourceRole resourceRole= new ResourceRole();
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setAppId(this.appId);
        resourceRole.setOrganId(this.organId);
        return resourceRole;
    }

    public ResourceRole getParentResourceRole(){
        ResourceRole parentResourceRole = new ResourceRole();
        parentResourceRole.setResourceInstanceId(this.parentResourceInstanceId);
        parentResourceRole.setResourceTypeCode(this.parentResourceTypeCode);
        parentResourceRole.setOrganId(this.organId);
        parentResourceRole.setAppId(this.appId);
        return parentResourceRole;
    }

}
