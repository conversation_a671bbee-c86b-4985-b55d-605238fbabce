package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@ApiModel("登录页面配置")
@Data
public class CustomSettingDto {
    @ApiModelProperty("平台名称")
    @Length(max = 30, message = "Platform name's size must less than 30")
    private String platformName;

    @ApiModelProperty("Slogan")
    @Length(max = 50, message = "Slogan's size must less than 50")
    private String slogan;

    @ApiModelProperty("版权声明")
    @Length(max = 100, message = "Copyright's size must less than 100")
    private String copyright;

    @ApiModelProperty("tab页标题")
    @Length(max = 30, message = "TabTitle's size must less than 30")
    private String tabTitle;

    private String backgroudImage;
}
