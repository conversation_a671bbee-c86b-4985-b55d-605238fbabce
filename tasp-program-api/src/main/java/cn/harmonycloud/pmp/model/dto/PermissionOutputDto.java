package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "菜单导入导出对象")
public class PermissionOutputDto implements Serializable {

    @ApiModelProperty(value = "权限id")
    private List<Long> permissionIds;

    @ApiModelProperty(value = "上传文件")
    private MultipartFile multipartFile;

    @ApiModelProperty(value = "应用ID")
    private Long appId;

}
