package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/20 2:24 下午
 **/
@ApiModel("通过外部信息保存行政组织")
@Data
public class AdministrativeSaveDTO {

    @ApiModelProperty("行政组织名称")
    private String adminName;

    @ApiModelProperty("行政组织编码")
    @NotEmpty(message = "行政组织编码不能为空")
    private String code;

    @ApiModelProperty("行政组织父级编码")
    private String parentCode;

}
