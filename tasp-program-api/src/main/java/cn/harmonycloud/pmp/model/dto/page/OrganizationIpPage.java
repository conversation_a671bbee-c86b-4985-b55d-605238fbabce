package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.OrganizationIp;
import cn.harmonycloud.pmp.page.Pagination;
import cn.harmonycloud.pmp.util.IPUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("租户ip分页对象")
public class OrganizationIpPage extends Pagination<OrganizationIp> {


    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "检查是否是完整的ip",hidden = true )
    private Boolean checkIpFlag = false;

    public void setIp(String ip) {
        this.ip = ip;
        this.checkIpFlag= IPUtil.validate(ip);
    }
}
