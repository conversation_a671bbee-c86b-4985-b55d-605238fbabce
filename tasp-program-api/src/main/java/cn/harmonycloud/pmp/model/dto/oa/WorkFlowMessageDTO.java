package cn.harmonycloud.pmp.model.dto.oa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @describe 工作流消息拓展实体类
 * @author: wang<PERSON><PERSON>
 * @create: 2022-01-14 14:26:47
 **/
@ApiModel("工作流消息拓展实体类")
@Data
public class WorkFlowMessageDTO implements Serializable {

    @ApiModelProperty("流程实例ID")
    private String instanceId;

    @ApiModelProperty("步骤名称（节点名称")
    private String nodeName;

    @ApiModelProperty("重定向回调地址")
    private String redirectUrl;

    @ApiModelProperty("是否待办任务")
    private Boolean todoTask;
}
