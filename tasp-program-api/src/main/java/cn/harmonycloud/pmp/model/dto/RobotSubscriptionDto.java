package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="机器人订阅")
public class RobotSubscriptionDto {

    private Integer messageTypeId;

    private List<String> robotNames;

    private List<Long> robotIds;
}
