package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.entity.ResourceUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class OrganAdminByUserVo implements Serializable {


    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "租户名称")
    private String organName;

    @ApiModelProperty(value = "租户管理员用户id")
    private List<ResourceUser> users;


}
