package cn.harmonycloud.pmp.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OperationAuditVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户ID")
    private String organId;

    @ApiModelProperty("租户名称")
    private String organName;

    @ApiModelProperty(value = "跟踪编号")
    private String traceId;

    @ApiModelProperty(value = "应用编码")
    private String appCode;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "操作名称")
    private String operationName;

    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    @ApiModelProperty(value = "审计实体主键值")
    private String operationId;

    @ApiModelProperty(value = "ip")
    private String ipAddress;

    @ApiModelProperty(value = "操作开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationStartTime;

    @ApiModelProperty(value = "操作结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationEndTime;

    @ApiModelProperty(value = "操作用户")
    private String operationUserName;

    @ApiModelProperty(value = "参数")
    private String param;

    @ApiModelProperty(value = "请求类型POST/GET")
    private String requestType;

    @ApiModelProperty(value = "URL")
    private String requestUrl;

    @ApiModelProperty(value = "类名")
    private String className;

    @ApiModelProperty(value = "方法名")
    private String method;

    @ApiModelProperty(value = "返回json")
    private String response;

    @ApiModelProperty(value = "返回HTTP code")
    private String responseCode;

    @ApiModelProperty(value = "操作结果")
    private String result;

    @ApiModelProperty(value = "自定义扩展字段")
    private String extendParam;

}
