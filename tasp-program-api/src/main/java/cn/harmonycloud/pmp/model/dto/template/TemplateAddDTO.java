package cn.harmonycloud.pmp.model.dto.template;

import cn.harmonycloud.pmp.model.entity.ReceiveGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: TemplateAddDTO
 * @projectName src-message-svc
 * @date 2020/11/4 15:49
 */
@Data
public class TemplateAddDTO implements Serializable {

    @ApiModelProperty(value = "模版ID")
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "消息类型")
    private String sendType;

    @ApiModelProperty(value = "发送给所有人 0-否 1-是")
    private Integer sendAll;

    @ApiModelProperty(value = "归属模块ID")
    private Long categoryId;

    @ApiModelProperty(value = "触发事件ID")
    private Long triggerId;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "备注信息")
    private String remarks;

    @ApiModelProperty(value = "模板内容keys值")
    private List<Map<String,String>> contentKeyList;

    @ApiModelProperty(value = "接收组集合")
    private List<ReceiveGroup> receiveGroupList;

}
