package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="UserResource关联关系对象")
public class UserResourceDto implements Serializable {

    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIds;

    @ApiModelProperty(value = "来源应用编码",hidden = true)
    private String sourceAppCode;

    @ApiModelProperty(value = "来源应用id",hidden = true)
    private Long sourceAppId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "目标应用编码")
    private String targetAppCode;

    @ApiModelProperty(value = "目标资源编码")
    private String targetReourceCode;

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例id")
    private Long resourceInstanceId;


    public ResourceRole createResourceRole(){
        ResourceRole resourceRole = new ResourceRole();
        resourceRole.setAppId(sourceAppId);
        resourceRole.setOrganId(organId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        return resourceRole;
    }

}
