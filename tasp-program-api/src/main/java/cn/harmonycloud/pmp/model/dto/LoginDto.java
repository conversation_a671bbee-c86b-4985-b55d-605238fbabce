package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: tong<PERSON>yue
 * @date: 2021-10-08
 **/
@Data
@ApiModel(value = "用户登录")
public class LoginDto implements Serializable {
    /**
     * 用户名
     */
    @NotNull(message = "用户名不为空")
    private String username;
    /**
     * 密码
     */
    @NotNull(message = "密码不能为空")
    private String password;

    /**
     * 验证码
     */
    @NotNull(message = "验证码不为空")
    private String code;

    /**
     * 随机码
     */
    @NotNull(message = "随机码不能为空")
    private String randomStr;
}
