package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class AddResourceRoleDto {


    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源ID",hidden = true)
    private Long resourceInstanceId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIds;
}
