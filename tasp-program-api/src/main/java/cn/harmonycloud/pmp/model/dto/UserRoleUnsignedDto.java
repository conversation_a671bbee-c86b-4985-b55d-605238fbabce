package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class UserRoleUnsignedDto implements Serializable {


    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "角色类型")
    private List<Integer> types;

}
