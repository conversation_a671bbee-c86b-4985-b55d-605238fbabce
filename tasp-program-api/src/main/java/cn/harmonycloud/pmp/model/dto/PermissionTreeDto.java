package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "获取树状权限参数对象")
public class PermissionTreeDto {

    @ApiModelProperty(value = "机构id")
    private Long organId;

    @ApiModelProperty(value = "应用id")
    private Long appId;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;
}
