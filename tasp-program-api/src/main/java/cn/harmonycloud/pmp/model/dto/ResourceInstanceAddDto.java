package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@ApiModel(value="ResourceInstance新增复合对象")
public class ResourceInstanceAddDto implements Serializable {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "父类id",hidden = true)
    private Long parentId;

    @ApiModelProperty(value = "应用ID",hidden = true)
    private String appCode;

    @ApiModelProperty(value = "资源类型编号",required = true)
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id",required = true)
    private Long resourceInstanceId;

    @ApiModelProperty(value = "应用资源名称",required = true)
    private String resourceInstanceName;

    @ApiModelProperty(value = "父级资源类型编号")
    private String parentResourceTypeCode;

    @ApiModelProperty(value = "父级应用资源id")
    private Long parentResourceInstanceId;

    @ApiModelProperty(value = "备注")
    private String description;

    //给所有人分配多个角色使用userIds和roleIds
    @ApiModelProperty(value = "负责人id")
    private List<Long> userIds;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIds;
}
