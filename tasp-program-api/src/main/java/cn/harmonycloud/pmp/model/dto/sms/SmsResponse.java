package cn.harmonycloud.pmp.model.dto.sms;

import cn.harmonycloud.pmp.Enum.SmsResponseStatusEnum;
import lombok.Data;

@Data
public class SmsResponse {
    private Integer status;
    private String message;
    private SmsResultData data;

    public boolean isSuccess(SmsResponseStatusEnum statusType) {
        return status.equals(statusType.getCode())
                && data != null;
    }

    @Data
    public static class SmsResultData {
        private Long msg_id;
        private String deliver_status;
        private Long template_id;
    }
}
