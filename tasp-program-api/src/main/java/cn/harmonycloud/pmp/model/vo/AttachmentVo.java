package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2021-10-13
 */
@Data
@ApiModel(value = "附件")
public class AttachmentVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * minio的存储空间名称
     */
    @ApiModelProperty(name = "minio的存储空间名称")
    private String bucketName;

    /**
     * 附件名称
     */
    @ApiModelProperty(name = "附件名称")
    private String name;

    /** 大小,单位:kb */
    @ApiModelProperty(name = "大小,单位:kb")
    private String size;

    /**
     * 附件地址
     */
    @ApiModelProperty(name = "附件地址")
    private String url;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
