package cn.harmonycloud.pmp.model.dto.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-27 2:58 下午
 */
@Data
@ApiModel("收件箱")
public class MessageInboxPage{
    private static final long serialVersionUID = 5558615225617471112L;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "状态0 未读 1已读")
    private Integer status;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "云服务")
    private String cloudServiceName;

    @ApiModelProperty(value = "消息类型")
    private List<Integer> messageTypeIds;
}
