package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.entity.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.minidev.json.annotate.JsonIgnore;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@ApiModel(value="ResourceInstance复合对象", description="资源实例复合表")
public class ResourceInstanceVo implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "应用资源名称")
    private String resourceInstanceName;

    @ApiModelProperty(value = "项目下成员数")
    private Integer userCount;

    @ApiModelProperty(value = "项目管理员")
    private List<User> resourceManagers;

    @ApiModelProperty(value = "项目管理员名称")
    @JsonIgnore
    private List<String> managerNames;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
