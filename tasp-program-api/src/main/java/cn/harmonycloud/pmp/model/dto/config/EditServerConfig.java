package cn.harmonycloud.pmp.model.dto.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @title: EditServerConfig
 * @projectName src-message-svc
 * @date 2020/11/10 16:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EditServerConfig {

    @ApiModelProperty(value = "服务器配置id")
    private Long id;

    @ApiModelProperty(value = "服务器地址")
    private  String server;

    @ApiModelProperty(value = "端口")
    private int port;

    @ApiModelProperty(value = "类别 字典服务 code = sendType")
    private int type;

    @ApiModelProperty(value = "应用标识")
    private String appId;

    @ApiModelProperty(value = "应用密钥")
    private String appSecret;

    @ApiModelProperty(value = "用户")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "令牌")
    private String token;

    @ApiModelProperty(value = "发送人")
    private String sender;

    @ApiModelProperty(value = "调用外部api")
    private String url;

    @ApiModelProperty(value = "是否匿名 1-是 2-否 字典服务 code = isAnonymous")
    private Integer isAnonymous;
}
