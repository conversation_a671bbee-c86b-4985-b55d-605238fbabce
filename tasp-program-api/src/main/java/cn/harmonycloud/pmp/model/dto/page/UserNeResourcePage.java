package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户分页对象")
public class UserNeResourcePage extends Pagination<User> {
    private static final long serialVersionUID = 8796628952643669932L;

    @ApiModelProperty(value = "复合查询参数,逗号隔开的查询参数")
    private String queryParam;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "去除资源下的用户")
    private List<Long> neUserIds;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long curOrganId;

    @ApiModelProperty(value = "资源类型编码")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化id")
    private Long resourceInstanceId;
}
