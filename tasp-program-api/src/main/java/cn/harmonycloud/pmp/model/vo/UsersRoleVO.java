package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 资源下不同用户的角色信息
 */
@Data
@ApiModel("资源下不同用户的角色信息")
public class UsersRoleVO {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "角色信息List")
    private List<RoleVo> roleList;

    @ApiModelProperty(value = "租户ID")
    private Long organId;

    @ApiModelProperty(value = "资源ID")
    private Long resourceId=0L;
}
