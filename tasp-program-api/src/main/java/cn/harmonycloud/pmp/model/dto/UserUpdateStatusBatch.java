package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.User;
import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class UserUpdateStatusBatch {

    @ApiModelProperty(value = "用户ids")
    @NotNull(message = "用户id不能为空")
    private List<Long> userIds;

    @ApiModelProperty(value = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status;


    public List<User> getUpdateUsers(){
        List<User> users = CollUtil.newArrayList();
        if(CollUtil.isEmpty(userIds)){
            return users;
        }
        for(Long userId : userIds){
            User user = new User();
            user.setId(userId);
            user.setFirstLogin(null);
            user.setStatus(status);
            users.add(user);
        }
        return users;
    }
}
