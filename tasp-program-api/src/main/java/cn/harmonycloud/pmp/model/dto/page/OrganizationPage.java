package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Organization;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("机构分页对象")
public class OrganizationPage extends Pagination<Organization> {


    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "资源类型码")
    private String resourceTypeCode;

    @ApiModelProperty(value = "标签id")
    private Long tagId;

    @ApiModelProperty(value = "查询参数")
    private String queryParam;

    @ApiModelProperty(value = "排序，默认倒序")
    private Boolean desc=true;

    @ApiModelProperty(value = "状态 0启用 1停用")
    private Integer status;

    public ResourceRole createResourceRole(){
        ResourceRole resourceRole =  new ResourceRole();
        resourceRole.setResourceOwnerInstanceId(userId);
        return resourceRole;
    }


}
