package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 查询不同用户在同一资源下各自拥有的角色
 *
 * @Date 2023-02-15 16:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "不同用户在同一资源下各自拥有的角色")
public class UserResourceRoleDTO {
    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIdList;

    @ApiModelProperty(value = "资源主键ID")
    private Long resourceId = 0L;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "租户id")
    private Long organId;
}
