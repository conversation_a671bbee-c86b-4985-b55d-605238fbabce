package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "租户复合参数对象")
public class OrganizationAddDto implements Serializable {

    @ApiModelProperty(value = "名称",required = true)
    @NotBlank(message = "租户名称不能为空")
    private String name;

    @ApiModelProperty(value = "编码",required = true)
    @NotBlank(message = "租户编码不能为空")
    private String code;

    @ApiModelProperty(value = "租户管理员",required = true)
    private List<Long> ownerIds;

    @ApiModelProperty(value = "状态 0正常 1锁定",required = true)
    private Integer status;

    @ApiModelProperty(value = "标签id集合")
    private List<Long> tagIds;

    @ApiModelProperty(value = "备注")
    private String description;

}
