package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.dto.AdministrativeDTO;
import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@ApiModel(value = "用户")
public class UserVo implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @ApiModelProperty(value = "员工编号")
    private String employNum;

    @ApiModelProperty(value = "类型 1公司内部用户 2公司外部用户")
    private Integer userType;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "是否可编辑")
    private Boolean editable;

    @ApiModelProperty(value = "状态 0.正常 1.锁定")
    private Integer status;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "角色集合")
    private List<RoleVo> roles;

    @ApiModelProperty(value = "角色ID集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "权限名称集合")
    private List<String> permissionName;

    @ApiModelProperty(value = "用户租户信息集合")
    private List<OrganizationVo> organizations;

    @ApiModelProperty(value = "行政组织列表ids")
    private List<Long> administrativeIds;

    @ApiModelProperty(value = "行政组织列表")
    private List<AdministrativeDTO> administrativeList;

    @ApiModelProperty(value = "租户名称")
    private String organName;

    @ApiModelProperty(value = "新增时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "用户登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;


    public void setRoles(List<RoleVo> roles) {
        this.roles = roles;
        if(CollUtil.isNotEmpty(roles)){
            this.roleIds = roles.stream().map(RoleVo::getId).distinct().collect(Collectors.toList());
        }
    }
}
