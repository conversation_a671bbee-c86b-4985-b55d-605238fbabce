package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @describe
 * @author: wang<PERSON><PERSON>
 * @create: 2022/11/01
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PushMessageDTO implements Serializable {
    private static final long serialVersionUID = -7057107445674895160L;

    @ApiModelProperty(value = "推送的消息")
    private MessageDTO message;

    @ApiModelProperty(value = "未读消息数量")
    private Integer unreadCount;
}
