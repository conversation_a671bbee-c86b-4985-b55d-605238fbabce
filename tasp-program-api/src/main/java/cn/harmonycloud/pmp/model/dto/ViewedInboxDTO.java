package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-27 3:10 下午
 */
@Data
@ApiModel("批量标记已读")
public class ViewedInboxDTO implements Serializable {
    private static final long serialVersionUID = 8575007235212709640L;

    @ApiModelProperty("是否一键全标记已读，1是 0否")
    private Integer all;

    @ApiModelProperty("批量标记ID集合")
    private List<Long> ids;

    @ApiModelProperty("批量标记ID集合")
    private Integer status;
}
