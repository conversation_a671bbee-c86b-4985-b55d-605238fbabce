package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.ResourceType;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("资源类型分页查询对象")
public class ResourceTypePage extends Pagination<ResourceType> {

    @ApiModelProperty(value = "应用id")
    @NotNull(message = "应用id，不能为空")
    private Long appId;

    @ApiModelProperty(value = "查询参数")
    private String queryParam;

    @ApiModelProperty(value = "拆分后的查询参数")
    private List<String> queryParams;

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }
}
