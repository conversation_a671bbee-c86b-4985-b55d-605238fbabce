package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.UserMapRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 资源实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@ApiModel(value="OwnerResource新增复合对象")
public class OwnerResourceAddDto implements Serializable {

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源Id")
    private List<Long> resourceIds;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIds;

    @ApiModelProperty(value = "资源拥有者id")
    private List<Long> resourceOwnerIds;

    @ApiModelProperty(value = "用户角色一一对应对象集合")
    private List<UserMapRole> userMapRoles;

    @ApiModelProperty(value = "资源拥有者实例ids",hidden = true)
    private List<Long> resourceOwnerInstanceIds;

    @ApiModelProperty(value = "资源拥有者类型",hidden = true)
    private String typeDictValue;

}
