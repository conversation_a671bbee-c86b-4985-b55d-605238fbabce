package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.ResourceOwnerInstance;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;


@Data
@ApiModel("资源拥有者分页对象")
public class ResourceOwnerInstancePage extends Pagination<ResourceOwnerInstance> {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "字典表的resource_owner的下属value")
    @TableField("type_dict_value")
    private String typeDictValue;

    @ApiModelProperty(value = "应用资源拥有者id")
    @TableField("resource_owner_instance_id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "子应用资源拥有者名称")
    @TableField("resource_owner_instance_name")
    private String resourceOwnerInstanceName;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例化id",hidden = true)
    private Long resourceInstanceId;


    @ApiModelProperty(value = "AMP资源id,子应用不要传这个参数",hidden = true)
    private Long resourceId;

    @ApiModelProperty(value = "应用id",hidden = true)
    private Long appId;
    @ApiModelProperty(value = "查询参数")
    private String queryParam;

    @ApiModelProperty(value = "拆分后的查询参数",hidden = true)
    private List<String> queryParams;

    @ApiModelProperty(value = "倒序排序")
    private Boolean desc=true;

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }

}
