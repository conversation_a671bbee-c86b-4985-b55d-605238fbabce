package cn.harmonycloud.pmp.model.dto.record;

import cn.harmonycloud.pmp.Enum.SendModeEnum;
import cn.harmonycloud.pmp.Enum.SendSourceEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: AddRecordDTO
 * @projectName src-message-svc
 * @date 2020/11/10 16:15
 */
@Data
public class AddRecordDTO implements Serializable {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "类别 字典服务 code= sendType 1代表邮件 2 代表短信 3 站内信  4 钉钉 5 飞书机器人")
    @NotNull(message = "发送类别不能为空")
    private Integer sendType;

    @ApiModelProperty(value = "状态",hidden = true)
    private String status;

    @ApiModelProperty(value = "收件人用户id集合")
    private List<Long> userIds;

    @ApiModelProperty(value = "目标地址",hidden = true)
    private String target;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty("发送人用户ID")
    private Long senderId;

    @ApiModelProperty("功能模块code")
    private String categoryCode;

    @ApiModelProperty("触发事件code")
    private String triggerCode;

    @ApiModelProperty(value = "发送人用户名称",hidden = true)
    private String sender;

    @ApiModelProperty(value = "发送机制  字典服务 code= sendMode  1-定时 2-实时,默认实时",hidden = true)
    private Integer sendMode = SendModeEnum.REALTIME.getCode();

    @ApiModelProperty(value = "消息来源 字典服务 code= messageSource 1-系统新增 2-后台服务发送",hidden = true)
    private String source = SendSourceEnum.SERVICE_SEND.getCode();

    @ApiModelProperty(value = "模板替换的key/value",hidden = true)
    private List<Map<String,String>> contentKeyList = CollUtil.newArrayList();

    @ApiModelProperty(value = "模板替换的key/value")
    private Map<String,String> contentKeyMap;

    @ApiModelProperty(value = "接口参数的key/value")
    private Map<String,Object> paramMap;

    @ApiModelProperty(value = "邮件，附带文件id集合")
    private List<Long> fileIds;

    /**
     * @see cn.harmonycloud.pmp.Enum.DingTalkMessageTypeEnum
     */
    @ApiModelProperty(value = "钉钉消息类型 text,image,file,link,markdown,oa,action_card")
    private String dingMsgType;

    @ApiModelProperty(value = "钉钉消息 是否发送给全部，默认false")
    private Boolean dingSendAll=false;

    @ApiModelProperty(value = "钉钉消息 上传文件的mediaId")
    private String dingMsgMediaId;

    @ApiModelProperty(value = "钉钉消息 跳转地址")
    private String dingMsgActionUrl;

    @ApiModelProperty(value = "钉钉消息 跳转按钮文字")
    private String dingMsgActionTitle;

    @ApiModelProperty(value = "钉钉消息 OA头部文字")
    private String dingMsgOaHeader;

    @ApiModelProperty(value = "消息类型Id")
    private Integer messageFrom;

    @ApiModelProperty(value = "收件租户id")
    private List<Long> organIds;

    @ApiModelProperty(value = "收件项目id")
    private List<Long> projectIds;

    @ApiModelProperty(value = "是否发送全部")
    private Boolean sendAll = false;

    public void setContentKeyMap(Map<String, String> contentMap) {
        this.contentKeyMap = contentMap;
        if(CollUtil.isNotEmpty(contentMap)){
            for(Map.Entry<String,String> entry : contentMap.entrySet()){
                Map<String,String> map = new HashMap<>();
                map.put("key",entry.getKey());
                map.put("value",entry.getValue());
                contentKeyList.add(map);
            }
        }
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
        if(CollUtil.isNotEmpty(userIds) && ObjectUtil.isNotNull(senderId)){
            userIds.remove(senderId);
        }
    }


    public void setSenderId(Long senderId) {
        this.senderId = senderId;
        if(CollUtil.isNotEmpty(userIds) && ObjectUtil.isNotNull(senderId)){
            userIds.remove(senderId);
        }
    }
}
