package cn.harmonycloud.pmp.model.dto.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QueryBatchDTO {
    @ApiModelProperty(value = "id列表")
    private List<Long> ids;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "归属模块（业务分类）编号")
    private String categoryCode;

    @ApiModelProperty(value = "触发事件编号")
    private String triggerCode;
}
