package cn.harmonycloud.pmp.model.dto;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class DingDeptUserDto {

    private Long id;

    private String deptId;

    private String deptName;

    private List<DeptUser> deptUsers;

    private List<DingDeptUserDto> childDepts;

    @Data
    public static class DeptUser{

        private Long id;

        private String userId;

        private String name;

        private String phone;

        private String email;
    }
}
