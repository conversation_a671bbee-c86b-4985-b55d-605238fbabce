package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value="资源之间的角色的关联关系表", description="资源之间的角色的关联关系表")
public class AppRoleDto implements Serializable {


    @ApiModelProperty(value = "来源应用code",hidden = true)
    private String sourceAppCode;

    @ApiModelProperty(value = "来源资源编号")
    private String sourceResourceCode;

    @ApiModelProperty(value = "来源角色id")
    private Long sourceRoleId;

    @ApiModelProperty(value = "来源角色编号")
    private String sourceRoleCode;

    @ApiModelProperty(value = "目标应用code")
    private String targetAppCode;

    @ApiModelProperty(value = "目标资源编号")
    private String targetResourceCode;
}
