package cn.harmonycloud.pmp.model.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="UserResource关联关系对象")
public class UserResource implements Serializable {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "目标资源角色id")
    private Long roleId;

    @ApiModelProperty(value = "目标资源角色编码")
    private String roleCode;

    /**
     * @describe
     * @author: zhengchenchen
     * @create: 2022-01-11 17:20:14
     **/
    @Data
    public static class ResourceCheckFlagDto implements Serializable {
        private static final long serialVersionUID = 907871241658423700L;

        @ApiModelProperty(value = "资源ID",hidden = true)
        private Long resourceId=0L;

        @ApiModelProperty(value = "资源类型")
        private String resourceTypeCode;

        @ApiModelProperty(value = "应用资源id")
        private Long resourceInstanceId;

        @ApiModelProperty(value = "租户id")
        private Long organId;

        @ApiModelProperty(value = "角色ID")
        private Long objectId;

        @ApiModelProperty(value = "类型 1.角色 2.用户 3.用户组")
        private Integer type;

        @ApiModelProperty(value = "种类 1.平台菜单 2.租户菜单 3.资源菜单")
        private Integer kind;

        @ApiModelProperty(value = "权限类型 1-菜单 2-tab页 3-功能点")
        private String permissionTypes;

        @ApiModelProperty(value = "权限类型 1-菜单 2-tab页 3-功能点",hidden = true)
        private List<Integer> permissionTypeList;

        public void setPermissionTypes(String permissionTypes) {
            this.permissionTypes = permissionTypes;
            List<Integer> permissionTypeList= CollUtil.newArrayList();
            if(StrUtil.isNotBlank(permissionTypes)){
                String[] array= permissionTypes.split(",");
                for (int i = 0; i < array.length; i++) {
                    permissionTypeList.add(Integer.parseInt(array[i]));
                }
            }
            this.setPermissionTypeList(permissionTypeList);
        }

    }
}
