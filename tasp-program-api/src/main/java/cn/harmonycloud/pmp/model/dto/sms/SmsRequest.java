package cn.harmonycloud.pmp.model.dto.sms;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SmsRequest {
    /**
     * 短信内容   发送短信和创建模板的接口都需要
     */
    private String content;

    private String dest_id;

    private Boolean market;

    private Integer signature_id;

    private Integer template_id;

    private List<String> parameters;

    private Integer type = 3;
}

