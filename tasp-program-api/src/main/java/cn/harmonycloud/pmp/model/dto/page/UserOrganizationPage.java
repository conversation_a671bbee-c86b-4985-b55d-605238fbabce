package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.UserOrganization;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户机构关联关系分页对象")
public class UserOrganizationPage extends Pagination<UserOrganization> {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    @ApiModelProperty(value = "行政组织id")
    private List<Long> adminIds;

    @ApiModelProperty(value = "是否包韩子行政组织id")
    private Boolean adminChildren;

    @ApiModelProperty(value = "机构id")
    private Long organId;

    @ApiModelProperty(value = "机构id")
    private Long resourceId=0L;

    @ApiModelProperty(value = "查询参数")
    private String queryParam;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "用户状态")
    private Integer status = 0;

    @ApiModelProperty(value = "拆分后的查询参数")
    private List<String> queryParams;

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }
}
