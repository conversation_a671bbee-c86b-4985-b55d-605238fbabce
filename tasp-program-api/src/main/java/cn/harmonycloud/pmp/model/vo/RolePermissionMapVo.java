package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 用户表
 * </p>
 * Map<Role,Map<permissionCode,boolean>>
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
@ApiModel(value="角色返回对象")
public class RolePermissionMapVo implements Serializable {

    @ApiModelProperty(value = "角色名称")
    String roleName;

    @ApiModelProperty(value = "权限")
    Map<String,Boolean> permissionCodeMap;
}
