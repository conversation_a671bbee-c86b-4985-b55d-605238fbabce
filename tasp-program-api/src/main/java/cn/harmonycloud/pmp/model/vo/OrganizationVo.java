package cn.harmonycloud.pmp.model.vo;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 租户表
 * </p>
 * 用于返回
 * <AUTHOR>
 * @since 2021-09-15
 */
@Data
public class OrganizationVo  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
   private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "管理员")
    private List<String> ownerNames;

    @ApiModelProperty(value = "管理员id")
    private List<Long> ownerIds;

    @ApiModelProperty(value = "权限树")
    List<Tree<Long>> permissionIds;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "权限树对应的最底层id集合")
    List<Long> depestPermissionIds;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "标签对象")
    private List<TagVo> tagVos;

   @ApiModelProperty(value = "标签id集合")
   private List<Long> tagIds;

}
