package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value="租户黑名单列表对象", description="租户黑名单")
public class OrganizationIpVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ip白名单")
    private Long id;

    @ApiModelProperty(value = "租户名称")
    private String organName;

    @ApiModelProperty(value = "ip白名单")
    private String ip;


}
