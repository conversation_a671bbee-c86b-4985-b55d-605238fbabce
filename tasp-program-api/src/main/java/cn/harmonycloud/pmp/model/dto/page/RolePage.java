package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Role;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("角色分页对象")
public class RolePage extends Pagination<Role> {
    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "角色类型集合 1.平台角色 2.租户共享角色 3.全局角色 4.资源类型角色 5.租户角色")
    private List<Integer> types;

    @ApiModelProperty(value = "复合查询参数")
    private String queryParam;
}
