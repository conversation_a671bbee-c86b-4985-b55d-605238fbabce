package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "租户树状参数查询对象")
public class OrganizationTreeDto implements Serializable {

    @ApiModelProperty(value = "名称/编号")
    private String queryParam;

    @ApiModelProperty(value = "父节点id")
    private Long parentId;

    @ApiModelProperty(value = "标签id")
    private Long tagId;

    @ApiModelProperty(value = "用户id",hidden = true)
    private Long userId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "租户ids",hidden = true)
    private List<Long> organIds;
}
