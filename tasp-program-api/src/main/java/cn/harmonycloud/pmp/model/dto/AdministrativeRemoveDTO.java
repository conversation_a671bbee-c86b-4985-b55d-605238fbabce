package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/20 2:35 下午
 **/
@Data
@ApiModel("通过外部信息删除行政组织")
public class AdministrativeRemoveDTO {

    @ApiModelProperty("行政组织编码")
    @NotEmpty(message = "行政组织编码不能为空")
    private String code;

}
