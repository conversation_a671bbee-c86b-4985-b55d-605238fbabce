package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.entity.Tag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="TagClassification返回对象", description="标签分类复合表")
public class TagClassificationVo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "标签名")
    private String name;

    @ApiModelProperty(value = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "tag" )
    private List<Tag> tags;


    public TagClassificationVo(String name) {
        this.name = name;
    }

    public TagClassificationVo() {
    }

}
