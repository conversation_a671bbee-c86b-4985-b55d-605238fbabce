package cn.harmonycloud.pmp.model.dto.sms;

import com.alibaba.fastjson.support.spring.PropertyPreFilters;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * @author: wp
 * @create: 2021/4/26
 * @Description:
 * @FileName: SmsDTO
 */
@Data
public class SmsDTO {

    @NotEmpty(message = "邮件标题不能为空")
    private String title;

    private String content="";

    /**
     * 消息接收人  手机号或者邮箱
     */
    @NotNull(message = "消息接收人不能为空")
    private List<String> receiverList;

    private List<MultipartFile> files = Collections.emptyList();


    /**
     * 排除需要打印的属性
     * @return
     */
    public PropertyPreFilters.MySimplePropertyPreFilter excludePropertiesToJSONString() {
        String[] excludeProperties = {"files"};
        PropertyPreFilters filters = new PropertyPreFilters();
        PropertyPreFilters.MySimplePropertyPreFilter excludefilter = filters.addFilter();
        excludefilter.addExcludes(excludeProperties);
        return excludefilter;
    }

}
