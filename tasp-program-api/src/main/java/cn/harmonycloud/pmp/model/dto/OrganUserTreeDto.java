package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="资源下查询租户用户对象", description="资源下查询租户用户对象参数")
public class OrganUserTreeDto implements Serializable {

    @ApiModelProperty(value = "资源类型",required = true)
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例ID",required = true)
    private Long resourceInstanceId;

    @ApiModelProperty(value = "租户id")
    private Long organId;
}
