package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class TagByNameDto implements Serializable {

    @ApiModelProperty(value = "标签编号",hidden = true)
    private String code;

    @ApiModelProperty(value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下",required = true)
    private Integer scope;

    @ApiModelProperty(value = "标签名",required = true)
    private String name;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "标签实体类型,资源下必填")
    private String typeCode;

    @ApiModelProperty(value = "标签实体实例id,资源下必填")
    private String instanceId;

    @ApiModelProperty(value = "应用code",hidden = true)
    private String appCode;

    @ApiModelProperty(value = "标签分类id",hidden = true)
    private Long classificationId;

    @ApiModelProperty(value = "标签分类编号",required = true)
    private String classificationCode;

    @ApiModelProperty(value = "颜色")
    private String colour;

    public void setName(String name) {
        this.name = name;
        this.code = name;
    }
}
