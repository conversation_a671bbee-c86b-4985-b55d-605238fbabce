package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @title: CategoryAddDto
 * @projectName amp
 * @date 2020/11/4 14:58
 */

@Data
public class TagRelateAddDto {

    @ApiModelProperty(value = "标签ids",required = true)
    List<Long> tagIds;

    @ApiModelProperty(value = "标签类型",required = true)
    @NotNull(message = "实例类型不能为空")
    String relateTypeCode;

    @ApiModelProperty(value = "实例id",required = true)
    @NotNull(message = "实例不能为空")
    Long relateInstanceId;
}
