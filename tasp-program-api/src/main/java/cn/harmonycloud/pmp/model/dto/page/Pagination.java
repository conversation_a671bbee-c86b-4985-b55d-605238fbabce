package cn.harmonycloud.pmp.model.dto.page;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/2
 */
@Data
@ApiModel("分页参数")
public class Pagination<T> implements Serializable {
    private static final long serialVersionUID = -1466437400829446382L;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页，默认 1")
    private long current = 1;

    /**
     * 每页的数量
     */
    @ApiModelProperty(value = "每页显示条数，默认 10")
    private long size = 10;

    /**
     * 排序属性
     */
    @ApiModelProperty(value = "排序字段")
    private String field;

    /**
     * 排序方式：asc,desc
     */
    @ApiModelProperty(value = "排序方式，asc,desc")
    private String order;

    /**
     * 转换为Page
     */
    public Page<T> toPage() {
        Page<T> page = new Page<>(current, size);

        return page;
    }
}
