package cn.harmonycloud.pmp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("消息展示")
public class OperationAuditConfigVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;


    /**
     * 操作事项实体编号
     */
    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    /**
     * 记录清除周期 单位:天
     */
    @ApiModelProperty(value = "记录清除周期")
    private Integer clearanceCycle;

    /**
     * 是否归档 0-是 1-否
     */
    @ApiModelProperty(value = "是否归档")
    private Integer archive;

    /**
     * 设置类型 0-通用类型 1-操作事项
     */
    @ApiModelProperty(value = "设置类型")
    private Integer configType;
}
