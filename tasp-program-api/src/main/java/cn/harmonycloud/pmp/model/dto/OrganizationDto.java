package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.PermissionTreeNode;
import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "租户复合参数对象")
public class OrganizationDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "父类ID")
    private Long parentId;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "租户名称不能为空")
    private String name;

    @ApiModelProperty(value = "编码")
    @NotBlank(message = "租户编码不能为空")
    private String code;

    @ApiModelProperty(value = "租户管理员")
    private List<Long> ownerIds;

    @ApiModelProperty(value = "行政组织id")
    private List<Long> administrativeIds;

    @ApiModelProperty(value = "负责行政组织id列表")
    private List<Long> chargeAdministrativeIds;

    @ApiModelProperty(value = "IP白名单开关 0-关 1-开")
    private Boolean whiteIpFlag;

    @ApiModelProperty(value = "ip强制管控 0-否 1-是")
    private Boolean ipForcedControl;

    @ApiModelProperty(value = "gitlab强制管控 0-否 1-是")
    private Boolean gitlabForcedControl;

    @ApiModelProperty(value = "jenkins强制管控 0-否 1-是")
    private Boolean jenkinsForcedControl;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "联系人")
    private Long contactPerson;

    @ApiModelProperty(value = "标签id集合")
    private List<Long> tagIds;

    @ApiModelProperty(value = "权限树节点集合")
    private List<PermissionTreeNode> permissionTreeNodes;

    @ApiModelProperty(value = "备注")
    private String description;

    public void setContactPerson(Long contactPerson) {
        this.contactPerson = contactPerson;
        if(CollUtil.isEmpty(ownerIds)){
            ownerIds = CollUtil.newArrayList();
            ownerIds.add(contactPerson);
        }
    }
}
