package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.Tag;
import cn.harmonycloud.pmp.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户分页对象")
public class TagPage extends Pagination<Tag> {
    private static final long serialVersionUID = 8796628952643669932L;

    @ApiModelProperty(value = "复合查询参数")
    private String queryParam;

    @ApiModelProperty(value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下")
    private Integer scope;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "标签实体类型,资源下必填")
    private String typeCode;

    @ApiModelProperty(value = "标签实体实例id,资源下必填")
    private String instanceId;

    @ApiModelProperty(value = "标签分类id")
    private Long classificationId;
}
