package cn.harmonycloud.pmp.model.vo;

import cn.harmonycloud.pmp.model.entity.ReceiveGroup;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: TemplateVO
 * @projectName src-message-svc
 * @date 2020/11/4 15:01
 */
@Data
public class TemplateVO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "发送给所有人 0-否 1-是")
    private Integer sendAll;

    @ApiModelProperty(value = "功能模块id")
    private Long categoryId;

    @ApiModelProperty(value = "触发事件ID")
    private Long triggerId;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "接收组集合")
    private List<ReceiveGroup> receiveGroupList;

    @ApiModelProperty(value = "模板内容键值")
    private List<Map<String,String>> contentKeyList;

    @ApiModelProperty(value = "备注信息")
    private String remarks;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime updateTime;
}
