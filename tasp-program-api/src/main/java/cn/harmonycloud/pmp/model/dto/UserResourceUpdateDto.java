package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

@Data
public class UserResourceUpdateDto {
    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "资源类型编号")
    @NotBlank(message = "资源类型编码不能为空")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    @NotBlank(message = "应用资源id不能为空")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "负责人id")
    private List<Long> userIds;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIds;


    public ResourceRole createResourceRole(){
        ResourceRole resourceRole = new ResourceRole();
        resourceRole.setOrganId(this.organId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        return resourceRole;
    }
}
