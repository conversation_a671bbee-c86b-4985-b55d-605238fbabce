package cn.harmonycloud.pmp.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Description 安全登录信息对象
 * <AUTHOR>
 * @Date 2024/6/3 3:00 下午
 **/
@Data
public class SafetyLoginDTO implements Serializable {

    private String passwordErrTime;

    private Integer passwordLoginLock = 0;

    public SafetyLoginDTO(String passwordErrTime, int passwordLoginLock){
        this.passwordErrTime = passwordErrTime;
        this.passwordLoginLock = passwordLoginLock;
    }

    public boolean afterPasswordTime(LocalDateTime time) {
        return passwordErrTime != null && passwordErrTime.length() > 0 && LocalDateTime.parse(passwordErrTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).isAfter(time);
    }
}
