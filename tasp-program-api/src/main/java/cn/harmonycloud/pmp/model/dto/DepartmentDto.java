package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;


/**
 * 部门复合表
 *
 * <AUTHOR>
 * @date 2021-12-01 11:01:35
 */
@Data
@ApiModel(value = "Department复合对象", description = "部门表")
public class DepartmentDto{

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;

    @ApiModelProperty(name = "租户ID")
    @NotNull(message = "租户不能为空")
    private Long organizationId;

    @ApiModelProperty(name = "名称")
    @NotBlank(message = "部门名称不能为空")
    private String name;

    @ApiModelProperty(name = "唯一编码")
    private String uniCode;

    @ApiModelProperty(name = "是否是根部门 0-否 1-是")
    private Integer isRoot;

    @ApiModelProperty(name = "备注")
    private String description;

    @ApiModelProperty(name = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "用户id")
    private Long userId;



}
