package cn.harmonycloud.pmp.model.vo;


import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2021-10-08
 */
@Data
@Builder
public class LoginVo {
    private String token;

    private Long expireTime;

    private UserInfoVo loginUser;
    /**
     * 密码过期：4-首次登录 3 过期 2 即将过期 1 没有 默认 1
     */
    private Integer passwordExpired=1;
    /**
     * 离密码过期剩下时间：天数
     */
    private Integer durationDaysInt=0;
    /**
     * 已使用的时间：天数
     */
    private Integer usedDaysInt;
}
