package cn.harmonycloud.pmp.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@ApiModel(value = "租户用户Vo")
public class OrganUserVo implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "账户名")
    private String username;

    @ApiModelProperty(value = "类型 1平台用户 2租户用户")
    private Integer type;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "状态 0.正常 1.锁定")
    private Integer status;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "角色集合")
    private List<RoleVo> roles;

    @ApiModelProperty(value = "角色ID集合")
    private List<Long> roleIds;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty(value = "权限名称集合")
    private List<String> permissionName;

    @ApiModelProperty(value = "用户租户信息集合")
    private List<OrganizationVo> organizations;

    @ApiModelProperty(value = "租户名称")
    private String organName;

    @ApiModelProperty(value = "新增时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "扩展字段")
    private String annotations;

    @ApiModelProperty(value = "用户登录时间")
    private LocalDateTime loginTime;

}
