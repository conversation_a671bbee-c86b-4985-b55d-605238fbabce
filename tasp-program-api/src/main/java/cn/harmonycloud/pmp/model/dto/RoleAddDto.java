package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 角色表
 * </p>
 *ø
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class RoleAddDto implements Serializable {

    @ApiModelProperty(value = "名称",required = true)
    private String name;

    @ApiModelProperty(value = "编码",required = true)
    private String code;

    @ApiModelProperty(value = "机构id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "1.平台角色 2.租户共享角色 3.全局角色 4.资源类型角色 5.租户角色",required = true)
    private Integer type;

    @ApiModelProperty(value = "角色类型编码,4-资源类型角色时必填")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "角色描述")
    private String description;

    @ApiModelProperty("扩展字段")
    private String annotations;

}
