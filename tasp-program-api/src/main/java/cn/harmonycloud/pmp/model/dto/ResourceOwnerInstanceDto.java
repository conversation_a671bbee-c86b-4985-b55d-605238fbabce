package cn.harmonycloud.pmp.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 资源拥有者实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Data
@ApiModel(value="ResourceOwnerInstance复合对象", description="ResourceOwnerInstance复合对象")
public class ResourceOwnerInstanceDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "字典表的resource_owner的下属value")
    @TableField("type_dict_value")
    private String typeDictValue;

    @ApiModelProperty(value = "应用资源拥有者id")
    @TableField("resource_owner_instance_id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "子应用资源拥有者名称")
    @TableField("resource_owner_instance_name")
    private String resourceOwnerInstanceName;

    @ApiModelProperty(value = "租户id")
    @TableField("organ_id")
    private Long organId;

    @ApiModelProperty(value = "扩展字段")
    @TableField("extend_field")
    private String extendField;

}
