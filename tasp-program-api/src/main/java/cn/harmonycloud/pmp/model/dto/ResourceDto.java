package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="资源集合对象")
public class ResourceDto extends ResourceRole implements Serializable {


    @ApiModelProperty(value = "资源拥有者类型 1-人员 2-角色 支持子应用扩展")
    private String typeDictValue;

    @ApiModelProperty(value = "资源拥有者id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "排序")
    private Boolean desc;

}
