package cn.harmonycloud.pmp.model.dto.oa;

import lombok.Data;

import java.io.Serializable;

/**
 * @describe OA待办请求返回实体类
 * @author: wang<PERSON><PERSON>
 * @create: 2022-01-18 15:39:32
 **/
@Data
public class OaMessageResult implements Serializable {
    private static final long serialVersionUID = -3921117483219136289L;

    /**
     * 异构系统标识
     */
    private String syscode;

    /**
     * 数据类型
     * IsUse：统一待办中心
     * OtherSys：异构系统
     * WfType：流程类型
     * WfData：流程数据
     * SetParam：参数设置
     */
    private String dateType;

    /**
     * 操作类型
     * AutoNew	：自动创建
     * New：新建
     * AutoEdit：自动更新
     * Edit：编辑
     * Del：删除
     * Check：检测
     * Set：设置
     */
    private String operType;

    /**
     * 操作结果
     * 1：成功
     * 0：失败
     */
    private String operResult;

    /**
     * 错误信息
     */
    private String message;

}
