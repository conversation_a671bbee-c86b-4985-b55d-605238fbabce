package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationAuditConfigDto {



    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "操作事项实体编号")
    private String operationCode;

    @ApiModelProperty(value = "记录清除周期")
    private Integer clearanceCycle;

    @ApiModelProperty(value = "是否归档")
    private Integer archive;

    @ApiModelProperty(value = "设置类型")
    private Integer configType;
}
