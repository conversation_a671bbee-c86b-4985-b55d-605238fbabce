package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户分页对象")
public class UserBiResourcePage extends Pagination<User> {
    private static final long serialVersionUID = 8796628952643669932L;

    @ApiModelProperty(value = "复合查询参数,逗号隔开的查询参数")
    private String queryParam;

    @ApiModelProperty(value = "复合查询参数集合",hidden = true)
    private List<String> queryParams;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "第一个资源类型编号，用户在此资源中")
    private String resourceTypeCode;

    @ApiModelProperty(value = "第一个应用资源id，用户在此资源中")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "第一个资源id，用户在此资源中",hidden = true)
    private Long resourceId;

    @ApiModelProperty(value = "第二个资源类型编号，用户不在此资源中")
    private String resource2TypeCode;

    @ApiModelProperty(value = "第二个应用资源id，用户不在此资源中")
    private Long resource2InstanceId;

    @ApiModelProperty(value = "第二个资源id，用户不在此资源中",hidden = true)
    private Long resource2Id;

    @ApiModelProperty(value = "排序，默认倒序")
    private Boolean desc=true;

    public void setQueryParam(String queryParam) {
        this.queryParam=queryParam;
        if(StrUtil.isNotBlank(queryParam)){
            List<String> queryParams= Arrays.asList(this.getQueryParam().split(","));
            this.setQueryParams(queryParams);
        }
    }

    public ResourceRole createResourceRole(){
        ResourceRole resourceRole= new ResourceRole();
        resourceRole.setResourceId(this.resourceId);
        resourceRole.setResourceInstanceId(this.resourceInstanceId);
        resourceRole.setResourceTypeCode(this.resourceTypeCode);
        resourceRole.setOrganId(this.organId);
        return resourceRole;
    }
}
