package cn.harmonycloud.pmp.model.vo;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppVo implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "应用首页地址")
    private String url;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "左侧栏图标")
    private String leftIcon;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "子应用菜单是否由主框架控制 0否 1是")
    private Integer frameControl;

    @ApiModelProperty(value = "应用绑定资源类型 绑定后直接跳转")
    @TableField("bind_resource_type_code")
    private String bindResourceTypeCode;

    @ApiModelProperty(value = "排序号")
    private Integer sortId;

    @ApiModelProperty(value = "权限树")
    private List<Tree<Long>> permissionTree;

}
