package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
public class UserPermissionDto extends ResourceRole implements Serializable {

    @ApiModelProperty(value = "权限拥有者ID")
    private List<Long> objectIds;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "权限拥有者类型 1-角色 2-用户 3-用户组",hidden = true)
    private Integer type;

    @ApiModelProperty(value = "权限类型 1-菜单 2-功能点 3-tab页",hidden = true)
    private List<Integer> permissionTypeList;

    @ApiModelProperty(value = "权限类型 1-菜单 2-功能点 3-tab页")
    private String permissionTypes;

    @ApiModelProperty(value = "应用id",hidden = true)
    private Long appId;

    @ApiModelProperty(value = "父类code")
    private String parentCode;

    @ApiModelProperty(value = "排除查询出的应用code")
    private String neAppCode;

    @ApiModelProperty(value = "权限编号",hidden = true)
    private String permissionCode;

    @ApiModelProperty(value = "父类菜单id")
    private Long parentId;

    @ApiModelProperty(value = "是否递归获取,默认false")
    private Boolean reGet = false;

    @ApiModelProperty(value = "菜单code")
    private List<String> permissionCodes;

    public void setPermissionTypes(String permissionTypes) {
        this.permissionTypes = permissionTypes;
        List<Integer> permissionTypeList= CollUtil.newArrayList();
        if(StrUtil.isNotBlank(permissionTypes)){
            String[] array= permissionTypes.split(",");
            for (int i = 0; i < array.length; i++) {
                permissionTypeList.add(Integer.parseInt(array[i]));
            }
        }
        this.setPermissionTypeList(permissionTypeList);
    }
}
