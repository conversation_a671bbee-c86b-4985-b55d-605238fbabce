package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.CopOrganUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@ApiModel(value="当前租户+协作租户拉人入参")
public class CopUserResourceAddDto {

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "资源实例id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "当前租户id",hidden = true)
    private Long curOrganId;

    @ApiModelProperty(value = "用户对象")
    List<CopOrganUser> copUserOrgans;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIds;
}
