package cn.harmonycloud.pmp.model.dto.page;

import cn.harmonycloud.pmp.constant.AmpConstant;
import cn.harmonycloud.pmp.model.entity.App;
import cn.harmonycloud.pmp.page.Pagination;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户分页对象")
public class UserResourcePage extends Pagination<App> {

    @ApiModelProperty(value = "租户id",hidden = true)
    private Long organId;

    @ApiModelProperty(value = "主键ID",hidden = true)
    private Long resourceId=0L;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;

    @ApiModelProperty(value = "应用id",hidden = true)
    private Long appId;

    @ApiModelProperty(value = "资源拥有者类型 1-人员 2-角色 支持子应用扩展")
    private String typeDictValue;

    @ApiModelProperty(value = "资源拥有者id")
    private Long resourceOwnerInstanceId;

    @ApiModelProperty(value = "资源实例名称")
    private String resourceInstanceName;

    public boolean isResourceRole(){
        return ObjectUtil.isNotNull(resourceInstanceId) || !AmpConstant.ORGAN_RESOURCE_ID.equals(resourceId);
    }
}
