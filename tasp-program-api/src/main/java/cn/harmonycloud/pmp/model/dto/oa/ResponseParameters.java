package cn.harmonycloud.pmp.model.dto.oa;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2021-12-16
 */
@Data
public class ResponseParameters {
    //异构系统标识
    private Integer sysCode;
    //数据类型 IsUse：统一待办中心 OtherSys：异构系统 WfType：流程类型 WfData：流程数据  SetParam：参数设置
    private String dateType;
    //操作类型 AutoNew	：自动创建 New：新建 AutoEdit：自动更新 Edit：编辑 Del：删除 Check：检测 Set：设置
    private String operType;
    //操作结果 0：成功  操作结果  1:失败
    private Boolean operResult;
}
