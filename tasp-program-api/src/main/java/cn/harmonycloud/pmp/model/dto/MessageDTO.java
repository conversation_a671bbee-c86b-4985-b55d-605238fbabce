package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.Enum.MessageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-27 11:23 上午
 */
@Data
@ApiModel("消息传输对象")
public class MessageDTO implements Serializable {
    private static final long serialVersionUID = 1882769989813302982L;

    @ApiModelProperty("消息ID")
    private Long id;

    @NotBlank(message = "消息标题不能为空")
    @ApiModelProperty(value = "标题")
    private String title;

    @NotBlank(message = "消息标题不能为空")
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * @see MessageTypeEnum
     */
    @ApiModelProperty(value = "消息类型")
    private Integer messageType;

    @ApiModelProperty(value = "接受者ID，0表示接受者为所有人")
    private List<Long> receiveIds;

    @ApiModelProperty(value = "收件租户id")
    private List<Long> organIds;

    @ApiModelProperty(value = "收件项目id")
    private List<Long> projectIds;

    @ApiModelProperty(value = "收件用户id")
    private List<Long> userIds;

    @ApiModelProperty(value = "发送人ID")
    private Long sendId;

    @ApiModelProperty(value = "消息类型ID")
    private Integer messageFrom;

    @ApiModelProperty(value = "是否发送全部")
    private Boolean sendAll = false;

}
