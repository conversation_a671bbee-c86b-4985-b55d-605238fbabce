package cn.harmonycloud.pmp.model.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "用户组复合参数对象")
public class UserGroupDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "租户id")
    private Long organId;

    @ApiModelProperty(value = "资源ID",hidden = true)
    private Long resourceId;

    @ApiModelProperty(value = "资源类型")
    private String resourceTypeCode;

    @ApiModelProperty(value = "应用资源id")
    private Long resourceInstanceId;
    public void setName(String name) {
        this.name=name;
        if(StrUtil.isNotBlank(name)){
            this.setCode(name);
        }
    }
}