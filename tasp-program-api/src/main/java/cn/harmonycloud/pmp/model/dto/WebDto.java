package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 微前端输入表
 * </p>
 *
 * <AUTHOR> @since 2021-09-24
 */
@Data
@ApiModel(value="新增微前端")
public class WebDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "编号", required = true)
    @NotBlank(message = "编号不能为空")
    private String code;

    @ApiModelProperty(value = "前端路由", required = true)
    private String route;

    @ApiModelProperty(value = "运行地址", required = true)
    private String url;

    @ApiModelProperty(value = "微前端类型：1.vue 2.react")
    private Integer type;

    @ApiModelProperty(value = "状态 0正常 1锁定")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String description;

}
