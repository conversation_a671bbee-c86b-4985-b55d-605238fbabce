package cn.harmonycloud.pmp.model.dto.sms;

import cn.harmonycloud.pmp.validator.TemplateKey;
import com.alibaba.fastjson.support.spring.PropertyPreFilters;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-11-24
 */
@Data
public class SmsTemplateDTO {
    /**
     * 模板表的id
     */
    private String temId;

    /**
     * 模板键值 - 兼容模板 id，用于获取模板
     */
    @TemplateKey
    private String templateKey;

    /**
     * 参数列表
     */
    private Map<String, String> paramMap;

    /**
     * 模板中表示姓名占位符的key
     */
    private String userNameKey;

    /**
     * key: 邮箱或手机号
     * value: 姓名
     */
    private Map<String, String> nameAndMail;

    /**
     * 消息接收人  手机号或者邮箱
     */
    private List<String> receiverList;

    private List<MultipartFile> files = Collections.emptyList();


    /**
     * 排除需要打印的属性
     *
     * @return
     */
    public PropertyPreFilters.MySimplePropertyPreFilter excludePropertiesToJSONString() {
        String[] excludeProperties = {"files"};
        PropertyPreFilters filters = new PropertyPreFilters();
        PropertyPreFilters.MySimplePropertyPreFilter excludefilter = filters.addFilter();
        excludefilter.addExcludes(excludeProperties);
        return excludefilter;
    }
}
