package cn.harmonycloud.pmp.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("根据当前上下文获取角色")
public class RoleByTypeDto{
    @ApiModelProperty(value = "租户id,不填默认从header拿")
    private Long organId;

    @ApiModelProperty(value = "资源类型,不传时返回全部type=4的角色")
    private String resourceTypeCode;

    @ApiModelProperty(value = "角色类型集合 1.平台角色 2.租户共享角色 3.全局角色 4.资源类型角色 5.租户角色",required = true)
    private List<Integer> types;

    public RoleByTypeDto(Long organId, String resourceTypeCode, List<Integer> types) {
        this.organId = organId;
        this.resourceTypeCode = resourceTypeCode;
        this.types = types;
    }

    public RoleByTypeDto() {
    }
}
