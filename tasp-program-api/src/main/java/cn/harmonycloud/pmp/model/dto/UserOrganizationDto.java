package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.ResourceRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 租户用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserOrganization复合对象")
public class UserOrganizationDto extends ResourceRole implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIds;

    @ApiModelProperty(value = "角色id集合")
    private List<Long> roleIds;



}
