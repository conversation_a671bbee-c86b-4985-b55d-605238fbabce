package cn.harmonycloud.pmp.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * 云服务消息类型
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CloudServiceMessageType {

    private String cloudserviceName;

    private List<MessageTypeDto> messageType;


    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    public static class MessageTypeDto {

        private int id;

        private int parentId;

        private String messageType;

        private String messageTypeAlias;

        private Boolean isRoot;

        private List<String> subscribeTypes;

        private String tips;

        private List<MessageTypeDto> childMessageType;
    }
}
