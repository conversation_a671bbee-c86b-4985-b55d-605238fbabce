package cn.harmonycloud.pmp.model.dto;

import cn.harmonycloud.pmp.model.entity.OrganizationIp;
import cn.harmonycloud.pmp.resp.ResultEnum;
import cn.harmonycloud.pmp.util.IPUtil;
import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@ApiModel(value="租户白名单新增对象", description="租户ip白名单新增")
public class OrganizationIpAddDto implements Serializable {

    private static final long seriaVersionUID = 1L;

    @ApiModelProperty(value = "租户id",hidden = true)
    @NotNull(message = "租户id不能为空")
    private Long organId;

    @ApiModelProperty(value = "ip白名单集合")
    @NotEmpty(message = "ip白名单集合不能为为空")
    private String ipParam;

    public List<OrganizationIp> getOrganIplist(){
        List<String> ipList = Arrays.asList(ipParam.split("\n"));
        List<OrganizationIp> result = CollUtil.newArrayList();
        if(CollUtil.isEmpty(ipList)){
            return result;
        }
        List<String> ipSet = CollUtil.newArrayList();
        for(String ip : ipList){
            if(ipSet.contains(ip)){
                continue;
            }
            if(ip.contains("/")){
                String[] ipArray = ip.split("/");
                ResultEnum.IP_REGEX_ERROR.isTrue(IPUtil.validate(ipArray[0]),ip);
            }else{
                ResultEnum.IP_REGEX_ERROR.isTrue(IPUtil.validate(ip),ip);
            }
            OrganizationIp organizationIp = new OrganizationIp();
            organizationIp.setOrganId(this.organId);
            organizationIp.setIp(ip.trim());
            result.add(organizationIp);
            ipSet.add(ip);
        }
        return result;
    }
}
