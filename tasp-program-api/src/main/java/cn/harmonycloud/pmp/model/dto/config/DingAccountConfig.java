package cn.harmonycloud.pmp.model.dto.config;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */

@SuperBuilder
@Data
public class DingAccountConfig {

    public DingAccountConfig() {
    }

    private Long id;

    private String companyName;

    private String domain;

    private String appKey;

    private String appSecret;

    private int type;

    private String annotation;

    private Boolean allowLogin;

    private Boolean allowMessageNotice;

    private LocalDateTime createTime;

    private Boolean status;
}
