package cn.harmonycloud.pmp.model.dto.sms;

import cn.harmonycloud.pmp.wechatbot.NewArticle;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: wangpeng
 * @Description: 微信机器人请求参数
 * @Date:Create：in 2021/8/10
 * @Modified By：
 */
@Data
public class WxCpGroupRobotReqDTO {

    @NotEmpty(message = "机器人地址不能为空")
    private String chatBotWebHook;

    /**
     * 文本内容，最长不超过2048个字节，markdown内容，最长不超过4096个字节，必须是utf8编码
     */
    private String content;

    /**
     * userid的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人，如果开发者获取不到userid，可以使用mentioned_mobile_list
     */
    private List<String> mentionedList;

    /**
     * 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
     */
    private List<String> mentionedMobileList;

    /**
     * 图片内容的base64编码
     */
    private String base64;

    /**
     * 图片内容（base64编码前）的md5值
     */
    private String md5;

    /**
     * 图文消息，一个图文消息支持1到8条图文
     */
    private List<NewArticle> articles;

}
