package cn.harmonycloud.pmp.Enum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <PERSON><PERSON>
 * @date 2020/11/17 15:49
 */
@Getter
@AllArgsConstructor
public enum WebHookCodeEnum {

    ORGAN_RM_USER("organRmUser","租户删除用户"),
    PLAT_RM_USER("platRmUser","平台删除用户"),
    PLAT_CREATE_USER("platCreateUser","平台创建用户"),
    PLAT_CREATE_ORGAN("platCreateOrgan","平台创建租户"),
    ORGAN_ADD_USER("organAddUser","租户新增用户"),
    ORGAN_USER_ROLE_CHANGE("organUserRoleChange","租户修改用户角色"),
    ;

    private String code;
    private String name;

}
