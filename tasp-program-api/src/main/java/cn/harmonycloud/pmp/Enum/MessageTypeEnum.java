package cn.harmonycloud.pmp.Enum;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @describe
 * @author: wangkuan
 * @create: 2022/11/01
 **/
@ApiModel("消息类型枚举类")
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {

    NOTICE(1,"通知"),
    PROCLAMATION(2,"公告"),
    PRIVATE_LETTER(3,"私信")
    ;

    private int code;

    private String desc;

}
