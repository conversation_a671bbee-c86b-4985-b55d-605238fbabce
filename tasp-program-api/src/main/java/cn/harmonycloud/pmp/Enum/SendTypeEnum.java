package cn.harmonycloud.pmp.Enum;

/**
 * <AUTHOR> <PERSON><PERSON>
 * @Description: 发送消息的发送类型枚举类
 * @date 2020/11/16 17:51
 */
public enum SendTypeEnum {
    //邮件通知
    MAIL(1, "MAIL","邮件通知"),
    //短信通知
    SMS(2, "SMS","短信通知"),
    //站内信
    IM(3, "IM","站内信通知"),
    //钉钉
    DINGTALK(4, "DINGTALK","钉钉"),

    ITSM(11,"ITSM","齐商ITSM"),

    FLYINGBOOKROBOT(5, "FLYINGBOOKROBOT", "飞书机器人"),
    FLYINGBOOKUSER(6, "FLYINGBOOKUSER", "飞书个人");


    private final int code;
    private final String name;
    private final String description;

    private SendTypeEnum(int code, String name,String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getType() {
        return code;
    }

    public String getName(){
        return name;
    }

    public String getDescription() {
        return description;
    }


    public static SendTypeEnum getType(Integer code) {
        for (SendTypeEnum sendTypeEnum : SendTypeEnum.values()) {
            if (sendTypeEnum.getType() == code) {
                return sendTypeEnum;
            }
        }
        return null;
    }
}
