package cn.harmonycloud.pmp.page;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class PageWrapper<T> implements IPage<T> {

    @ApiModelProperty(value = "记录")
    private List<T> records;
    @ApiModelProperty(value = "分页对象")
    Page<T> page;

    public PageWrapper(long current, long size,List<T> records) {
        this.page=new Page<>(current,size);
        this.setRecords(records);
    }

    public PageWrapper(long current, long size) {
        this.page=new Page<>(current,size);
    }

    public Page<T> getPage() {
        return page;
    }

    @Override
    public List<OrderItem> orders() {
        return page.orders();
    }

    @Override
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    public IPage<T> setRecords(List<T> records) {
        if(CollUtil.isNotEmpty(records)){
            Integer start = Math.toIntExact((page.getCurrent() - 1) * page.getSize());
            Integer end= Math.toIntExact((records.size() - start) > page.getSize() ?page.getCurrent()* page.getSize() : records.size());
            page.setTotal(records.size());
            page.setRecords(records.subList(start,end));
        }
        return page;
    }

    @Override
    public long getTotal() {
        return page.getTotal();
    }

    @Override
    public IPage<T> setTotal(long total) {
        return page.setTotal(total);
    }

    @Override
    public long getSize() {
        return page.getSize();
    }

    @Override
    public IPage<T> setSize(long size) {
        return page.setSize(size);
    }

    @Override
    public long getCurrent() {
        return page.getCurrent();
    }

    @Override
    public IPage<T> setCurrent(long current) {
        return page.setCurrent(current);
    }
}
