package cn.harmonycloud.pmp.mq;

import cn.harmonycloud.pmp.fegin.IOperationAuditProvider;
import cn.harmonycloud.pmp.model.param.BaseOperationAuditParam;
import cn.harmonycloud.pmp.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ArrayBlockingQueue;

@Slf4j
public class MessageQueueUtil {

    private static ArrayBlockingQueue<BaseOperationAuditParam> messageQueue = new ArrayBlockingQueue<>(1000);

    /**
     * 生产消息
     */
    public static void produce(BaseOperationAuditParam baseOperationAuditParam){
        if(!messageQueue.offer(baseOperationAuditParam)){
            log.info("审计消息队列暂存消息达到最大值，消息被丢弃");
        }
    }

    /**
     * 消费消息
     */
    public static void consume(){
        try {
            BaseOperationAuditParam baseOperationAuditParam = messageQueue.take();
            log.info("消费了消息："+baseOperationAuditParam.getOperationName());
            IOperationAuditProvider operationAuditProvider = SpringContextUtils.getContext().getBean(IOperationAuditProvider.class);
            operationAuditProvider.save(baseOperationAuditParam);
        } catch (Exception e) {
            log.error("消费消息发生异常:{0}",e);
        }
    }

}

