package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.entity.AmpFile;
import cn.harmonycloud.pmp.model.entity.FileResult;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @describe
 * @author: zhengchenchen
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "fileProvider",url = "${amp.url:http://localhost:8080/}",path = FeignConstant.PROVIDER+"/files")
public interface IFileProvider {


    @GetMapping("/{id}")
    @ApiOperation("根据附件id查询文件")
    R<AmpFile> getById(@ApiParam(value = "附件id") @PathVariable Long id);

    @PostMapping(value = "/upload/one",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("上传附件")
    R<FileResult> addFile(@RequestParam String bucketName, @RequestPart("file") MultipartFile file);

    @PostMapping("/upload/muti")
    @ApiOperation(value = "创建多个附件",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<List<FileResult>> addFiles(@RequestParam String bucketName, @ApiParam(value = "上传的文件数组") @RequestPart("file") MultipartFile[] files);
}
