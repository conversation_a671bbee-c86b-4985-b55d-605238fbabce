package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.OrganizationAddDto;
import cn.harmonycloud.pmp.model.dto.OrganizationUpdateDto;
import cn.harmonycloud.pmp.model.dto.page.OrganizationPage;
import cn.harmonycloud.pmp.model.entity.Organization;
import cn.harmonycloud.pmp.model.vo.OrganAdminByUserVo;
import cn.harmonycloud.pmp.model.vo.OrganizationVo;
import cn.harmonycloud.pmp.resp.R;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(value = "organizationProvider",path = FeignConstant.PROVIDER+"/organizations",url = "${amp.url:http://localhost:8080/}")
public interface IOrganizationProvider {

    /**
     * 获取所有租户信息
     * @param
     * @return
     */
    @GetMapping
    @ApiOperation(value = "根据id获取租户信息", notes = "根据id获取租户信息")
    R<List<Organization>> getList();

    @GetMapping("/tree")
    @ApiOperation(value = "获取租户树", notes = "获取租户树")
    R<List<Tree<Long>>> getTree();


    @GetMapping("/users")
    @ApiOperation(value = "获取登录用户的租户列表", notes = "根据用户id获取租户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "token", paramType = "header", dataType = "String",dataTypeClass = String.class,required = true),
    })
    R<List<Organization>> getByUser();

    @GetMapping("{/id}")
    @ApiOperation(value = "根据id获取租户信息", notes = "根据id获取租户信息")
    R<Organization> getOrganById(@PathVariable("id")Long id);

    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户id获取租户列表", notes = "根据用户id获取租户列表")
    R<List<Organization>> getByUserId(@PathVariable("userId") Long userId);


    @PostMapping
    @ApiOperation(value = "创建租户表", notes = "创建租户表")
    R<Long> create(@Valid @RequestBody OrganizationAddDto organizationAddDto);

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除租户表", notes = "根据ID删除")
    R<Boolean> delete(@PathVariable("id") Long id);

    @PostMapping("/put/{id}")
    @ApiOperation(value = "修改租户表", notes = "修改租户表")
    R<Boolean> update(@Valid @RequestBody OrganizationUpdateDto organizationUpdateDto, @PathVariable("id") Long id);

    @GetMapping("/ids/map")
    @ApiOperation(value = "根据id集合查询租户", notes = "根据id集合查询租户")
    R<Map<Long,String>> getMapByIds(@RequestParam List<Long> organIds);

    @GetMapping("/id/name")
    @ApiOperation(value = "根据id集合查询租户名称，支持传organId", notes = "根据id集合查询租户名称")
    R<String> getNameById(@RequestParam Long organId);

    @GetMapping("/organ/admin")
    @ApiOperation(value = "查询某个用户所属租户，信息中包含租户所属管理员", notes = "查询某个用户所属租户，信息中包含租户所属管理员")
    R<List<OrganAdminByUserVo>> getOrganAdminByUser(@RequestParam Long userId);

    @GetMapping("/user/nrRole")
    @ApiOperation(value = "获取当前用户所在的租户 nr = not relation", notes = "获取当前用户所在的租户，无关角色体系")
    R<List<Organization>> getByUserNrRole(@RequestParam("userId") Long userId);
}
