package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.annotation.OperationAudit;
import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.*;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;

@FeignClient(name = "userProviderProvider",path = FeignConstant.PROVIDER+"/user-organizations",url = "${amp.url:http://localhost:8080/}")
public interface IUserOrganizationProvider {


    @PostMapping("/resource")
    @ApiOperation(value = "创建资源用户关联表", notes = "创建资源用户关联表")
    @OperationAudit(operationName = "新增资源下用户",operationCode = "用户-租户")
    R<Boolean> createResource(@Valid @RequestBody UserOrganizationDto userOrganizationDto);

    @PostMapping("/organ")
    @ApiOperation(value = "创建租户用户关联表", notes = "创建租户用户关联表")
    R<Boolean> create(@Valid @RequestBody UserOrganizationDto userOrganizationDto);


    @PostMapping("/resource/delete")
    @ApiOperation(value = "删除资源用户关联表", notes = "删除租户用户关联表")
    @ApiImplicitParams({
        @ApiImplicitParam(name ="userId",value = "用户id" ,required = true,paramType = "body"),
        @ApiImplicitParam(name ="organId",value = "机构id" ,required = true,paramType = "body"),
    })
    R<Boolean> resourceDelete(@Valid @RequestBody UserResourceDeleteDto deleteDto);

    @PostMapping("/delete")
    @ApiOperation(value = "删除租户用户关联表", notes = "删除租户用户关联表")
    @ApiImplicitParams({
        @ApiImplicitParam(name ="userId",value = "用户id" ,required = true,paramType = "body"),
        @ApiImplicitParam(name ="organId",value = "机构id" ,required = true,paramType = "body"),
    })
    @OperationAudit(operationName = "移除资源下用户",operationCode = "用户-租户")
    R<Boolean> delete(@Valid @RequestBody UserOrganizationDeleteDto deleteDto);


    @PostMapping("/resource/delete/batch")
    @ApiOperation(value = "批量 删除资源用户关联表", notes = "批量 删除租户用户关联表")
    @OperationAudit(operationName = "移除资源下用户",operationCode = "用户-租户")
    R<Boolean> deleteBatch(@Valid @RequestBody UserResourceDeleteBatchDto deleteDto);

    @PostMapping("/v2/batch")
    @ApiOperation(value = "创建租户用户关联表", notes = "创建租户用户关联表")
    @OperationAudit(operationName = "新增资源下用户",operationCode = "用户-租户")
    R<Boolean> createBatchV2(@Valid @RequestBody UserOrganizationAddBatchDto userOrganizationAddBatchDto);

    @PostMapping("/cooperate/resource")
    @ApiOperation(value = "创建资源协作用户关联表", notes = "创建资源协作用户关联表")
    R<Boolean> saveCopUserResource(@Valid @RequestBody CopUserResourceAddDto copUserResourceAddDto);

    @PostMapping("/resource/updateRole")
    @ApiOperation(value = "更新资源下用户的角色", notes = "更新资源下用户的角色")
    R<Boolean> updateRole(@Valid @RequestBody UserResourceUpdateRoleDto updateRoleDto);

    @GetMapping("/count/organ")
    @ApiOperation(value = "查询租户下的人数", notes = "查询租户下的人数")
    R<Integer> countByOrgan(@RequestParam("organId") Long organId);
}
