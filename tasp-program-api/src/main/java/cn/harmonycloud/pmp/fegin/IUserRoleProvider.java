package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.AddResourceRoleDto;
import cn.harmonycloud.pmp.model.dto.UserRoleDto;
import cn.harmonycloud.pmp.model.entity.ResourceOwnerInstance.UserRolesByResourceUser;
import cn.harmonycloud.pmp.model.entity.UserRole;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * @describe
 * @author: wangkuan
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "userRoleProvider",path = FeignConstant.PROVIDER+"/user-roles",url = "${amp.url:http://localhost:8080/}")
public interface IUserRoleProvider {

    @ApiOperation(value = "根据角色ID获取用户")
    @GetMapping("/role/{roleId}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "roleId", value = "角色id", paramType = "path", dataTypeClass = Long.class, required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataTypeClass = Long.class, required = true),
    })
    R<List<UserRole>> getByRoleId(@PathVariable("roleId") Long roleId);


    @ApiOperation(value = "根据角色ID集合获取用户")
    @GetMapping("/roles")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "roleIds", value = "角色id", paramType = "path", dataTypeClass = Long.class, required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataTypeClass = Long.class, required = true),
    })
    R<List<UserRole>> getByRoleIds(@RequestParam("roleIds") List<Long> roleIds);

    @PostMapping("/role/assign/update")
    @ApiOperation(value = "修改资源下，拥有此角色的用户列表")
    R<Boolean> updateRoleOfUsers(@RequestBody AddResourceRoleDto addResourceRoleDto);


    @GetMapping("/users")
    @ApiOperation(value = "根据用户获取角色", notes = "根据用户获取角色")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户id", paramType = "query", dataTypeClass = Long.class, required = true),
        @ApiImplicitParam(name = "organId", value = "租户id,不传从header拿", paramType = "query", dataTypeClass = Long.class, required = false),
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型编码,获取资源下角色必填", paramType = "query", dataTypeClass = String.class, required = false),
        @ApiImplicitParam(name = "resourceInstanceId", value = "资源实例id，获取资源下角色必填", paramType = "query", dataTypeClass = Long.class, required = false)
    })
    R<List<UserRoleVo>> getRoleByUserId(@SpringQueryMap UserRolesByResourceUser userRolesByResourceUser);


    @PostMapping("/delete")
    @ApiOperation(value = "删除用户角色关联关系表", notes = "删除用户角色关联关系表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户", paramType = "body", dataTypeClass = Long.class, required = true),
        @ApiImplicitParam(name = "organId", value = "租户id,没传从header拿", paramType = "body", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "roleIds", value = "角色列表", paramType = "body", dataTypeClass = List.class),
        @ApiImplicitParam(name = "resourceInstanceId", value = "资源实例id", paramType = "body", dataTypeClass = Long.class, required = false),
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型码", paramType = "body", dataTypeClass = String.class, required = false),
    })
    R<Boolean> delete(@RequestBody UserRoleDto userRoleDto);

    @PostMapping("/role")
    @ApiOperation(value = "把角色分配给用户", notes = "新增用户角色关联关系")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户", paramType = "body", dataTypeClass = Long.class, required = true),
        @ApiImplicitParam(name = "roleIds", value = "角色列表", paramType = "body", dataTypeClass = List.class),
    })
    R<Boolean> create(@Valid @RequestBody UserRoleDto userRoleDto);
}
