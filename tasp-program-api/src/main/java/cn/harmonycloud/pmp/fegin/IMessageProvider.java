package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.record.AddRecordDTO;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;

@FeignClient(value = "messageProvider",path = FeignConstant.PROVIDER+"/messages",url = "${amp.url:http://localhost:8080/}")
public interface IMessageProvider {

    @ApiOperation("发送消息")
    @PostMapping("/sendMessage")
    R<Boolean> sendMessage(@Valid @RequestBody AddRecordDTO addRecordDTO);

    @ApiOperation("根据模版id查询内容")
    @GetMapping("/template")
    R<String> getContentByTemplateId(@RequestParam Long templateId,String contentKeyMapJson);

}
