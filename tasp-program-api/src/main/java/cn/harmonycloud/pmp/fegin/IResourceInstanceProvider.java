package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.*;
import cn.harmonycloud.pmp.model.dto.page.ResourceInstancePage;
import cn.harmonycloud.pmp.model.entity.CheckResourceInstance;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.model.vo.ResourceInstanceVo;
import cn.harmonycloud.pmp.resp.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import java.util.List;

@FeignClient(name = "resourceInstanceProvider", url = "${amp.url:http://localhost:8080/}", path = FeignConstant.PROVIDER + "/resourceInstances")
public interface IResourceInstanceProvider {
    @PostMapping
    @ApiOperation(value = "新建资源实例", notes = "新建资源实例")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "body", dataType = "String",dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "resourceInstanceId", value = "资源实例id", paramType = "body", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "resourceInstanceName", value = "资源实例名称", paramType = "body", dataType = "String",dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
    })
    R<Boolean> create(@Valid @RequestBody ResourceInstanceAddDto resourceInstanceAddDto);


    @PostMapping("/delete/instanceIds")
    @ApiOperation(value = "根据实例id(s)批量删除", notes = "根据实例id(s)批量删除")
    R<Boolean> deleteByInstanceIds(@RequestBody ResourceInstancesDeleteDto resourceInstancesDeleteDto);

    @PostMapping("/update")
    @ApiOperation(value = "修改资源实例", notes = "修改资源实例")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "body", dataType = "String",dataTypeClass = String.class),
        @ApiImplicitParam(name = "resourceInstanceId", value = "资源实例id", paramType = "body", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
    })
    R<Boolean> update(@Valid @RequestBody ResourceInstanceUpdateDto resourceInstanceDto);

    @PostMapping("/delete")
    @ApiOperation(value = "删除资源实例表", notes = "根据资源实例ID删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "body", dataType = "String",dataTypeClass = String.class),
        @ApiImplicitParam(name = "resourceInstanceId", value = "资源实例id", paramType = "body", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
    })
    R<Boolean> delete(@RequestBody ResourceInstanceDeleteDto resourceInstanceDeleteDto);

    @GetMapping("/page")
    @ApiOperation(value = "资源分页-获取资源", notes = "超级管理员，租户管理员，获取所有指定类型的资源实例，普通员工获取拥有的资源合集")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String",dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
    })
    R<Page<Long>> getOwnerResource(@SpringQueryMap ResourceInstancePage resourceInstancePage);

    @GetMapping("/owner")
    @ApiOperation(value = "资源列表-获取资源", notes = "超级管理员，租户管理员，资源管理员获取所有的资源，普通员工获取拥有的资源")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String",dataTypeClass = String.class),
        @ApiImplicitParam(name = "typeDictValue", value = "资源拥有者类型", paramType = "query",dataTypeClass = Integer.class),
        @ApiImplicitParam(name = "resourceOwnerInstanceId", value = "资源拥有者id", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
    })
    R<List<ResourceInstance>> getListOwnerResource(@SpringQueryMap ResourceRole resourceRole);

    @GetMapping("/owner/ids")
    @ApiOperation(value = "获取资源实例ids", notes = "超级管理员，租户管理员，资源管理员获取所有的资源，普通员工获取拥有的资源，返回实例id")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String",dataTypeClass = String.class),
        @ApiImplicitParam(name = "resourceOwnerInstanceId", value = "资源拥有者id", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class,required = true),
        @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class,required = true),
    })
    R<List<String>> getByOwnerResource(@SpringQueryMap OwnerResourceRoleDTO ownerResourceRoleDTO);


    @GetMapping("/entity/page")
    @ApiOperation(value = "获取用户拥有的资源实例", notes = "超级管理员，租户管理员，资源管理员获取所有的资源，普通员工获取拥有的资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "resourceOwnerInstanceId", value = "资源拥有者id", paramType = "query", dataTypeClass = String.class),
            @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class, required = true),
            @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class, required = true),
    })
    R<Page<ResourceInstance>> getPageByExample(@SpringQueryMap ResourceInstancePage resourceInstancePage);


    @GetMapping("/cooperate/queryUser/resourceTypeCode")
    @ApiOperation(value = "根据资源类型查询所有该用户拥有的资源实例", notes = "根据资源类型查询所有该用户拥有的资源实例,不传默认从header拿")
    R<List<Long>> getCopByExample(@RequestParam("userId") Long userId,@RequestParam("resourceTypeCode") String resourceTypeCode);

    @GetMapping("/organ")
    @ApiOperation(value = "获取当前租户下的资源", notes = "获取指定租户下的所有指定类型的资源,不传默认从header拿")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "organId", value = "租户id,不传默认从header拿", paramType = "query", dataType = "Long", dataTypeClass = Long.class, required = true),
    })
    R<List<ResourceInstance>> getByOrganId(@SpringQueryMap ResourceInstance resourceInstance);

    @GetMapping("/instance")
    @ApiOperation(value = "根据实例id获取资源实例", notes = "根据实例id获取资源实例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "resourceInstanceId", value = "资源实例id", paramType = "query", dataType = "String", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "organId", value = "租户id,不传默认从header拿", paramType = "query", dataType = "Long", dataTypeClass = Long.class, required = true),
    })
    R<ResourceInstance> getByInstance(@SpringQueryMap CheckResourceInstance checkResourceInstance);

    @GetMapping("/organ/user/resourceTypeCode")
    @ApiOperation(value = "根据资源类型查询所有该用户拥有的资源实例", notes = "根据资源类型查询所有该用户拥有的资源实例,不传默认从header拿")
    R<List<ResourceInstance>> getByOrganUserCode(@RequestParam("resourceTypeCode") String resourceTypeCode);

    @GetMapping("/owner")
    @ApiOperation(value = "资源列表-获取资源,默认根据当前用户查询", notes = "超级管理员，租户管理员，资源管理员获取所有的资源，普通员工获取拥有的资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "typeDictValue", value = "资源拥有者类型", paramType = "query", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "resourceOwnerInstanceId", value = "资源拥有者id", paramType = "query", dataTypeClass = String.class),
            @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataType = "Long", dataTypeClass = Long.class, required = true),
            @ApiImplicitParam(name = "Amp-App-Code", value = "应用编号", paramType = "header", dataType = "String", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", dataType = "String", dataTypeClass = String.class, required = true),
    })
    R<List<ResourceInstance>> getByOwner(@SpringQueryMap ResourceRole resourceRole);

    @GetMapping("/cooperate/user/resourceTypeCode")
    @ApiOperation(value = "根据资源类型查询所有该用户拥有的资源实例", notes = "根据资源类型查询所有该用户拥有的资源实例,不传默认从header拿")
    R<List<Long>> getCopByResourceTypeCode(@RequestParam("resourceTypeCode") String resourceTypeCode);

    @GetMapping("/cooperate/user/resourceTypeCodes")
    @ApiOperation(value = "根据资源类型查询所有该用户拥有的资源实例", notes = "根据资源类型查询所有该用户拥有的资源实例,不传默认从header拿")
    R<List<Long>> getCopByResourceTypeCode(@RequestParam("resourceTypeCodes") List<String> resourceTypeCodes);


    @GetMapping("/organ/owner/projects")
    @ApiOperation(value = "获取用户的项目", notes = "获取用户的项目")
    R<Page<ResourceInstanceVo>> getOrganUserProjects(@SpringQueryMap ResourceInstancePage resourceInstancePage);

    @GetMapping("/name")
    @ApiOperation(value = "获取资源列表", notes = "根据resourceInstanceName获取租户下资源")
    R<ResourceInstance> getByNameAndOrganId(@RequestParam("name") String name);

    @GetMapping("/user/niOrgan")
    @ApiOperation(value = "获取全量的资源（没有租户隔离）,ni=not isolation", notes = "获取全量的资源（没有租户隔离）")
    R<List<ResourceInstance>> getByUserId(@RequestParam("resourceTypeCode") String resourceTypeCode, @RequestParam("userId") Long userId);
}

