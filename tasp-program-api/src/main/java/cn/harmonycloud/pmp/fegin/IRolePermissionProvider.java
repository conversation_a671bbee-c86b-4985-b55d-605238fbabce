package cn.harmonycloud.pmp.fegin;


import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.RoleDto;
import cn.harmonycloud.pmp.model.dto.RolePermissionAddDto;
import cn.harmonycloud.pmp.model.vo.RolePermissionMapVo;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(value = "objectPermissionProvider", path = FeignConstant.PROVIDER + "/objectPermissions", url = "${amp.url:http://localhost:8080/}")
public interface IRolePermissionProvider {


    @PostMapping("/organ/allChecked")
    @ApiOperation(value = "赋予租户全选权限", notes = "赋予租户全选权限")
    R<Boolean> createOrganAllPermssion(@RequestBody RolePermissionAddDto objectPermissionAddDto);

    @PostMapping("/skyview")
    @ApiOperation(value = "观云台赋权", notes = "观云台赋权点确定批量赋权,devops是点一个赋予一次权限")
    R<Boolean> grantPermissions(@RequestBody RoleDto roleDto);

    @PostMapping("/role")
    @ApiOperation(value = "赋予租户角色权限", notes = "赋予租户角色权限")
    R<Boolean> create(@RequestBody RolePermissionAddDto objectPermissionAddDto);
    /**
     * 查看用户是否用用某个权限
     * @return
     */
    @GetMapping("/role/resourceTypeCode/map")
    @ApiOperation(value = "获取资源类型下的角色权限", notes = "获取资源类型下的角色权限")
    R<List<RolePermissionMapVo>> getMapByResourceTypeCode(@RequestParam String resourceTypeCode);
}
