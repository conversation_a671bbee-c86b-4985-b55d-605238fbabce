package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.annotation.OperationAudit;
import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.UsersByResource;
import cn.harmonycloud.pmp.model.dto.*;
import cn.harmonycloud.pmp.model.dto.page.UserPage;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.model.entity.ResourceUser;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.entity.UserNeResource;
import cn.harmonycloud.pmp.model.vo.*;
import cn.harmonycloud.pmp.resp.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(name = "userProvider",path = FeignConstant.PROVIDER+"/users/v2",url = "${amp.url:http://localhost:8080/}")
public interface IUserProvider {

    @GetMapping("/organ/{organId}")
    @ApiOperation(value = "根据机构id获取用户(包含角色)", notes = "根据机构id获取用户(包含角色)")
    R<List<UserVo>> getVoByOrganId(@PathVariable("organId") Long organId,@RequestParam("queryParam") String queryParam);


    @GetMapping("/base/organId/{organId}")
    @ApiOperation(value = "根据机构id获取用户基本信息", notes = "根据机构id获取用户基本信息")
    R<List<UserBaseVO>> getUserBaseByOrganId(@PathVariable("organId") Long organId, @RequestParam(value ="queryParam", required = false) String queryParam);


    @GetMapping("/organ")
    @ApiOperation(value = "根据机构id获取用户,从header获取organId", notes = "根据机构id获取用户,从header获取organId")
    R<List<User>> getByOrgan();

    @PostMapping
    @ApiOperation(value = "新增用户", notes = "新增系统用户")
    R<UserInfoVo> create(@Valid @RequestBody UserRegisterDto userDto);

    @GetMapping("/organ/role")
    @ApiOperation(value = "根据机构角色集合获取用户", notes = "根据机构角色集合获取用户")
    R<List<User>> getByOrganRoles(@RequestParam("organId") Long organId,@RequestParam("roleIds")List<Long> roleIds);

    @GetMapping("/page")
    @ApiOperation(value = "用户表分页列表", notes = "分页查询")
    R<Page<UserVo>> page(@SpringQueryMap UserPage page);

    @GetMapping("/resource")
    @ApiOperation(value = "获取资源下的用户", notes = "获取资源下的用户")
    R<List<UserVo>> getVosByResource(@SpringQueryMap ResourceRole resourceRole);

    @GetMapping("/{id}")
    @ApiOperation(value = "用户表详情", notes = "根据ID查询用户")
    R<User> getById(@PathVariable("id") Long id);

    @GetMapping("/batch")
    @ApiOperation(value = "根据id获取用户", notes = "根据id获取用户")
    R<List<User>> getByIds(@RequestParam("ids")List<Long> ids);

    @GetMapping("/usernames")
    @ApiOperation(value = "根据username集合批量获取用户", notes = "根据username集合批量获取用户")
    R<List<User>> page(@RequestParam("usernames")List<String> usernames);

    @GetMapping("/token")
    @ApiOperation(value = "根据token获取当前登录用户全量信息", notes = "根据token获取当前登录用户全量信息")
    R<User> getByToken();

    @GetMapping("/username")
    @ApiOperation(value = "根据用户名获取用户信息", notes = "根据用户名获取用户信息")
    R<UserVo> getbyUsername(@RequestParam("username") String username);

    @GetMapping()
    @ApiOperation(value = "查询全量用户", notes = "查询全量用户")
    R<List<User>> list();

    @PostMapping("/noCode/login")
    @ApiOperation(value = "无验证码登录", notes = "无验证码登录")
    R<LoginVo> login(@RequestBody LoginDto loginDto);

    @PostMapping("/logout")
    @ApiOperation(value = "退出登录", notes = "退出登录")
    R<User> logout();

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除用户表", notes = "根据ID删除")
    R<Boolean> delete(@PathVariable("id") Long id);

    @PostMapping("/put")
    @ApiOperation(value = "修改用户表", notes = "修改用户表")
    R<Boolean> updateById(@Valid @RequestBody UserUpdateDto userDTO);

    @GetMapping("/name/{name}")
    R<List<UserVo>> getbyName(@PathVariable("name") String name);

    @PostMapping("/updatePassword")
    @ApiOperation(value = "修改密码", notes = "修改密码")
    R<Boolean> updatePsw(@Valid @RequestBody UserUpdatePasswordDto userUpdatePasswordDTO);

    @PostMapping("/organ/neResource/page")
    @ApiOperation(value = "在租户内不在此资源下用户分页", notes = "分页查询")
    R<Page<UserVo>> pageOrganNoCurrentResource(@RequestBody UserPage page);

    @GetMapping("/ids/map")
    @ApiOperation(value = "根据id获取用户名称,Map<id,name>", notes = "根据id获取用户名称")
    R<Map<Long,String>> getMapByIds(@RequestParam("ids")List<Long> ids);

    @GetMapping("/ids")
    @ApiOperation(value = "根据ids获取用户，包括角色名和部门名", notes = "查询不同用户在同一资源下各自拥有的角色")
    R<List<OrganUserVo>> getOrganUserVoById(@SpringQueryMap UserResourceRoleDTO userResourceRoleDTO);

    @PostMapping("/delete/usernames")
    @ApiOperation(value = "根据username集合批量删除用户", notes = "根据username集合批量删除用户")
    @OperationAudit(operationName="根据username集合批量删除用户",operationCode = "user")
    R<Boolean> deleteByUsernames(@RequestBody Map<String,Object> paramMap);

    @PostMapping("/reset/{id}")
    @ApiOperation(value = "重置密码", notes = "重置密码")
    @OperationAudit(operationName="重置密码",operationCode = "user")
    R<Boolean> reset(@PathVariable("id") Long id);

    @GetMapping("/cooperate/resource")
    @ApiOperation(value = "获取资源下的用户+协作用户", notes = "获取资源下的用户+协作用户")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataTypeClass = String.class, required = true),
        @ApiImplicitParam(name = "resourceInstanceId", value = "资源id", paramType = "query", dataTypeClass = Long.class, required = true),
        @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataTypeClass = Long.class, required = true),
    })
    R<List<CopResourceUserVo>> getCopVoByResourceId(@SpringQueryMap UsersByResource usersByResource);

    @GetMapping("/cooperate/resource/base")
    @ApiOperation(value = "获取资源下的用户+协作用户，只返回用户", notes = "获取资源下的用户+协作用户，只返回用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "resourceInstanceId", value = "资源id", paramType = "query", dataTypeClass = Long.class, required = true),
            @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataTypeClass = Long.class, required = true),
    })
    R<List<UserBaseVO>> getCopByResourceId(@SpringQueryMap UsersByResource usersByResource);

    @GetMapping("/cooperate/resource/base/resourceInstanceInstanceIds")
    @ApiOperation(value = "获取资源集合下的用户+协作用户，只返回用户", notes = "获取资源下的用户+协作用户，只返回用户")
    R<List<UserBaseVO>> getCopByResourceIds(Long organId,
                                            @RequestParam("resourceTypeCode") String resourceTypeCode,
                                            @RequestParam("resourceInstanceIds") List<Long> resourceInstanceIds);

    @GetMapping("/cooperate/resources/count")
    @ApiOperation(value = "获取资源下的用户+协作用户数量，返回key: instanceId,value: 用户数量", notes = "获取资源下的用户+协作用户数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceTypeCode", value = "资源类型", paramType = "query", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "resourceInstanceId", value = "资源id", paramType = "query", dataTypeClass = Long.class, required = true),
            @ApiImplicitParam(name = "Amp-Organ-Id", value = "租户id", paramType = "header", dataTypeClass = Long.class, required = true),
    })
    R<Map<Long,Integer>> getCopCountByResourceIds(@SpringQueryMap UserResourcesDto userResourcesDto);



    @GetMapping("/resource/role")
    @ApiOperation(value = "根据资源下的角色查询用户", notes = "根据资源下的角色查询用户")
    R<List<User>> getByResourceRole(@RequestParam("organId") Long organId,
                                           @RequestParam("resourceTypeCode") List<String> resourceTypeCode,
                                           @RequestParam("resourceInstanceId") Long resourceInstanceId,
                                           @RequestParam("roleIds") List<Long> roleIds);

    @GetMapping("/resource/role/map")
    @ApiOperation(value = "根据资源下的角色查询用户,返回<resourceId,List<userId>>", notes = "根据资源下的角色查询用户,返回<resourceId,List<userId>>")
    R<Map<Long,List<Long>>> getMapByResourceRole(@RequestParam("resourceTypeCode") String resourceTypeCode,
                                                     @RequestParam("resourceInstanceIds") List<Long> resourceInstanceIds,
                                                     @RequestParam("roleId") Long roleId);

    @GetMapping ("/organ/neResource/list")
    @ApiOperation(value = "在租户内不在此资源下用户列表，有角色", notes = "全量查询")
    R<List<ResourceUser>>  getByOrganNoCurrentResource(@SpringQueryMap UserNeResource userNeResource);
}
