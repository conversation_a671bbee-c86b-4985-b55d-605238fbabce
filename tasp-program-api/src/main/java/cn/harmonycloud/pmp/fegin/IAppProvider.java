package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.vo.AppVo;
import cn.harmonycloud.pmp.model.vo.GatewayAppVo;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @describe
 * @author: wangkuan
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "appProvider",url = "${amp.url:http://localhost:8080/}",path = FeignConstant.PROVIDER+"/apps")
public interface IAppProvider {

    @GetMapping("/organ")
    @ApiOperation(value = "根据用户和租户获取应用信息", notes = "根据用户和租户获取应用信息")
    R<List<AppVo>> getByOrganId(@RequestHeader("Authorization")String token,@RequestHeader("Amp-Organ-Id")String organId);


    @GetMapping("/code")
    @ApiOperation(value = "根据code查询app", notes = "根据code查询app")
    R<GatewayAppVo> getByCode(@RequestParam("code")String code);
}
