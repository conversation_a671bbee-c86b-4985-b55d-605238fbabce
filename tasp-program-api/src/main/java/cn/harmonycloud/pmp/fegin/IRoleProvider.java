package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.*;
import cn.harmonycloud.pmp.model.dto.page.RolePage;
import cn.harmonycloud.pmp.model.entity.Role;
import cn.harmonycloud.pmp.model.entity.RoleBase;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import cn.harmonycloud.pmp.resp.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @describe
 * @author: wangkuan
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "roleProvider",path = FeignConstant.PROVIDER+"/roles",url = "${amp.url:http://localhost:8080/}")
public interface IRoleProvider {

    @GetMapping("/{id}")
    @ApiOperation(value = "角色表详情", notes = "根据ID查询详情")
    R<RoleVo> info(@PathVariable("id") Long id);

    @GetMapping("/batch")
    @ApiOperation(value = "根据id集合获取角色集合", notes = "根据id集合获取角色集合")
    R<List<Role>> getByBatchIds(@RequestParam("roleIds") List<Long> roleIds);

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除角色表", notes = "根据ID删除")
    R<Boolean> delete(@PathVariable("id") Long id);

    @PostMapping
    @ApiOperation(value = "创建角色表", notes = "创建角色表")
    R<Boolean> create(@Valid @RequestBody RoleDto roleDto);

    @GetMapping("/resources/user")
    @ApiOperation(value = "获取资源下用户的角色", notes = "获取资源下用户的角色")
    R<Map<Long,List<RoleBase>>> getByResourceUser(@SpringQueryMap RolesByResourcesUser rolesByResourcesUser);

    @PostMapping("/update/resource/user")
    @ApiOperation(value = "修改资源下用户的角色", notes = "修改资源下用户的角色")
    R<Boolean> resourceUpdateUserRole(@Valid @RequestBody UpdateResourceUserRoleDto updateResourceUserRoleDto);

    @PostMapping("/update/{id}")
    @ApiOperation(value = "修改角色表", notes = "修改角色表")
    R<Boolean> update(@Valid @RequestBody RoleDto roleDto, @PathVariable("id") Long id);

    @GetMapping("/page")
    @ApiOperation(value = "获取机构下得所有角色", notes = "根据id集合获取角色集合")
    R<Page<RoleVo>> getRoles(@SpringQueryMap RolePage rolePage);

    @GetMapping("/organ")
    @ApiOperation(value = "获取机构下所有的角色", notes = "角色不分页列表")
    R<List<RoleVo>> getByOrgan(@RequestParam("organId") Long organId);

    @GetMapping("/users/{userId}")
    @ApiOperation(value = "根据人员id获取角色集合", notes = "根据人员id获取角色集合")
    R<List<Role>> getRolesByUserId(@PathVariable("userId") Long userId);

    @PostMapping("/update")
    @ApiOperation(value = "修改角色表以及权限", notes = "修改角色表以及权限")
    R<Boolean> update(@Valid @RequestBody RoleUpdateDto roleUpdateDto);

    @GetMapping("/resource/user")
    @ApiOperation(value = "获取租户、资源下用户的角色", notes = "获取租户、资源下用户的角色")
    R<List<Role>> getByResourceUser(@SpringQueryMap RolesByResourceUser rolesByResourceUser);

    @PostMapping("/valid")
    @ApiOperation(value = "角色重名校验", notes = "角色重名校验")
    R<Boolean> validRoleRepeat(@Valid @RequestBody RoleDto roleDto);

    @PostMapping("/update/simple/{id}")
    @ApiOperation(value = "修改角色表", notes = "修改角色表")
    R<Boolean> simpleUpdateRole(@Valid @RequestBody RoleDto roleDto, @PathVariable("id") Long id);

    @GetMapping("/roleTypeCode")
    @ApiOperation(value = "根据角色类型编码获取角色集合", notes = "根据角色类型编码获取角色集合")
    R<List<Role>> getByResourceTypeCode(@RequestParam("reosurceTypeCode") String reosurceTypeCode);

    @PostMapping("/list")
    @ApiOperation(value = "获取平台、机构、资源下的所有角色-列表", notes = "获取平台、机构、资源下的所有角色-列表")
    R<List<Role>> list(@RequestBody RoleByTypeDto roleByTypeDto) ;

    @GetMapping("/all")
    @ApiOperation(value = "查询所有角色", notes = "查询所有角色")
    R<List<Role>> getList();

    @GetMapping("/checkAdmin")
    @ApiOperation(value = "查看是否是超级管理员", notes = "查看是否是超级管理员")
    R<Boolean> checkAdminRole();

}
