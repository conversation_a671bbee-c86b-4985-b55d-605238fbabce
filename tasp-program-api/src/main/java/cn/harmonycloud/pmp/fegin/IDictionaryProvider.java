package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.entity.Dictionary;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @describe
 * @author: zhen<PERSON><PERSON><PERSON>
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "dictionaryProvider",path = FeignConstant.PROVIDER+"/dictionarys",url = "${amp.url:http://localhost:8080/}")
public interface IDictionaryProvider {

    @GetMapping("/code")
    @ApiOperation(value = "字典表列表", notes = "字典表列表")
    R<List<Dictionary>> getByCode(@RequestParam String code);

    @GetMapping("/code/map")
    @ApiOperation(value = "字典表列表", notes = "字典表列表")
    R<Map<String,String>> getMapByCode(@RequestParam String code);

    @GetMapping("/code/list")
    @ApiOperation(value = "获取平台parentId为0的code列表")
    R<List<Dictionary>> listCode();

}
