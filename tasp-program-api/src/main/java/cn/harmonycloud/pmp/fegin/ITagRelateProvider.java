package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.TagRelateAddDto;
import cn.harmonycloud.pmp.model.dto.TagsByInstancesDto;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "tagRelateProvider",path = FeignConstant.PROVIDER+"/tagRelate",url = "${amp.url:http://localhost:8080/}")
public interface ITagRelateProvider {

    @PostMapping
    @ApiOperation(value = "新增标签关联关系")
    R<Boolean> removeThenAdd(@Validated @RequestBody TagRelateAddDto tagRelateAddDto);

    @GetMapping("/instances")
    @ApiOperation(value = "根据实例id集合查询标签")
    R<Map<Long, List<Long>>> getByInstanceIds(@SpringQueryMap TagsByInstancesDto tagsByInstancesDto);

    @PostMapping("/delete/instance")
    @ApiOperation(value = "根据实例删除标签关联关系")
    R<Boolean> removeByInstanceId(@RequestParam String relateTypeCode, @RequestParam Long relateInstanceId);

    @GetMapping("/tag")
    @ApiOperation(value = "根据标签id和查询实例")
    R<List<Long>> getInstanceIdsByTagId(@RequestParam Long tagId,@RequestParam String relateTypeCode);

    @PostMapping("/batch")
    @ApiOperation(value = "根据标签id和查询实例")
    R<Boolean> saveBatch(@RequestBody List<TagRelateAddDto> tagRelateAddDtos);
}
