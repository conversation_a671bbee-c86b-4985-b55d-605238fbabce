package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.entity.UserDetail;
import cn.harmonycloud.pmp.resp.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @describe
 * @author: wang<PERSON><PERSON>
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "userDetailProvider",url = "${amp.url:http://localhost:8080/}",path = FeignConstant.PROVIDER+"/userDetails")
public interface IUserDetailProvider {

    @GetMapping("/getByUserId")
    @ApiOperation(value = "根据用户id和类型查询外部主键", notes = "根据用户id和类型查询外部主键")
    R<UserDetail> getByUserId(@RequestParam("userId") Long userId);


    @GetMapping("/getMapByUserIds")
    @ApiOperation(value = "根据用户ids查询用户信息", notes = "返回值key: userId，value：UserDetail")
    R<Map<Long,UserDetail>> getMapByUserIds(@RequestParam("userIds") List<Long> userIds);


}
