package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.PermissionDto;
import cn.harmonycloud.pmp.model.dto.ResourceDto;
import cn.harmonycloud.pmp.model.entity.Permission;
import cn.harmonycloud.pmp.model.entity.PermissionTreeNode;
import cn.harmonycloud.pmp.resp.R;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(value = "permissionProvider", path = FeignConstant.PROVIDER + "/permissions", url = "${amp.url:http://localhost:8080/}")
public interface IPermissionProvider {

    /**
     * 获取用户某一层权限下拥有权限
     *
     * @return
     */
    @GetMapping("/organ/{organId}/parent/{parentId}")
    R<Map<String, Object>> getUserPermissionByParentId(@RequestHeader("Authorization") String token, @PathVariable("organId") Long organId, @PathVariable("parentId") Long parentId);

    /**
     * 获取用户权限
     *
     * @return
     */
    @GetMapping("/organ/resource/user/entity")
    @ApiOperation(value = "获取用户权限", notes = "获取用户权限")
    R<List<Permission>> getByUser(ResourceDto resourceDto);

    /**
     * 根据角色id获取权限
     *
     * @return
     */
    @GetMapping("/roleId/{roleId}")
    @ApiOperation(value = "根据角色id获取权限", notes = "根据角色id获取权限")
    R<List<Tree<Long>>> getByRoleId(@PathVariable("roleId") Long roleId, @RequestParam("resourceTypeCode") String resourceTypeCode, @RequestParam("resourceInstanceId") Long resourceInstanceId);


    /**
     * 根据应用id获取权限
     *
     * @return
     */
    @GetMapping("/appId")
    @ApiOperation(value = "根据应用id获取权限", notes = "根据应用id获取权限")
    R<List<Tree<Long>>> getByAppId();

    @PostMapping("/put/{id}")
    @ApiOperation(value = "修改权限表", notes = "修改权限表")
    R<Boolean> update(@Valid @RequestBody Permission permission, @PathVariable("id") Long id);

    @PostMapping
    @ApiOperation(value = "创建权限表", notes = "创建权限表")
    R<Boolean> create(@Valid @RequestBody PermissionDto permissionDto);


    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除权限表", notes = "根据ID删除")
    R<Boolean> delete(@PathVariable("id") Long id);

    @GetMapping
    @ApiOperation(value = "获取权限树(包含应用层)", notes = "获取权限树(包含应用层)")
    R<List<Tree<Long>>> getTreeList(@SpringQueryMap PermissionDto permissionDto);


    @GetMapping("/organ/resource")
    @ApiOperation(value = "获取平台、租户、资源集下的所有权限", notes = "获取平台、租户、资源集下的所有权限")
    R<List<Tree<Long>>> getByOrganOrResource(@SpringQueryMap ResourceDto resourceDto);

    @GetMapping("/organ/resource/node")
    @ApiOperation(value = "获取平台、租户、资源集下的所有权限", notes = "获取平台、租户、资源集下的所有权限")
    R<List<PermissionTreeNode>> getByOrganOrResourceList(@SpringQueryMap ResourceDto resourceDto);

    @GetMapping("/organ/resource/user")
    @ApiOperation(value = "获取用户权限", notes = "获取用户权限")
    R<List<Tree<Long>>> getByOrganAndResource(@SpringQueryMap ResourceDto resourceDto);

    @GetMapping("/all")
    @ApiOperation(value = "查询全局角色/租户共享角色和在租户下的权限", notes = "查询全局角色/租户共享角色和在租户下的权限")
    R<List<Permission>> getAllPermission();

}
