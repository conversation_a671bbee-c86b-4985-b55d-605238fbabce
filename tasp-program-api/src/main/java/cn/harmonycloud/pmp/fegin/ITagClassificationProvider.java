package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.DeleteTagClassificationDto;
import cn.harmonycloud.pmp.model.dto.page.TagClassificationPage;
import cn.harmonycloud.pmp.model.entity.TagClassification;
import cn.harmonycloud.pmp.resp.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "tagClassificationProvider",path = FeignConstant.PROVIDER+"/tagClassification",url = "${amp.url:http://localhost:8080/}")
public interface ITagClassificationProvider {

    @GetMapping()
    @ApiOperation(value = "列表获取分类")
    R<List<TagClassification>> list();

    @GetMapping("/{id}")
    @ApiOperation(value = "根据id获取分类对象")
    R<TagClassification> getById(@PathVariable Long id);


    @PostMapping()
    @ApiOperation(value = "创建分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "标签分类名", paramType = "body", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "code", value = "标签分类编号", paramType = "body", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "scope", value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下", paramType = "body", dataTypeClass = Integer.class, required = true),
            @ApiImplicitParam(name = "organId", value = "租户id", paramType = "body", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "typeCode", value = "标签实体类型,资源下必填", paramType = "body", dataTypeClass = String.class),
            @ApiImplicitParam(name = "instanceId", value = "标签实体实例id,资源下必填", paramType = "body", dataTypeClass = Long.class)
    })
    R<Boolean> add(@RequestBody TagClassification tagClassification);

    @PostMapping("/update")
    @ApiOperation(value = "修改分类名称")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键ID", paramType = "body", dataTypeClass = Long.class, required = true),
            @ApiImplicitParam(name = "name", value = "标签分类名", paramType = "body", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "code", value = "标签分类编号", paramType = "body", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "scope", value = "作用域 1-平台及以下 2-租户及以下 3-资源及以下", paramType = "body", dataTypeClass = Integer.class, required = true),
            @ApiImplicitParam(name = "organId", value = "租户id", paramType = "body", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "typeCode", value = "标签实体类型,资源下必填", paramType = "body", dataTypeClass = String.class),
            @ApiImplicitParam(name = "instanceId", value = "标签实体实例id,资源下必填", paramType = "body", dataTypeClass = Long.class)
    })
    R<TagClassification> edit(@RequestBody TagClassification tagClassification);

    /**
     * 查询标签分类
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询标签分类列表")
    R<Page<TagClassification>> page(TagClassificationPage tagClassificationPage);

    /**
     * 删除标签分类
     * @param
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除标签分类")
    R<Boolean> deleteTagClassification(@RequestBody DeleteTagClassificationDto deleteTagClassificationDto);
}
