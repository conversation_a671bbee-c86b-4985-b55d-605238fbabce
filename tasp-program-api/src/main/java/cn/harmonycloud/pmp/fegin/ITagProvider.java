package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.dto.TagByNameDto;
import cn.harmonycloud.pmp.model.entity.Tag;
import cn.harmonycloud.pmp.model.entity.TagsByClassificationCode;
import cn.harmonycloud.pmp.model.vo.TagVo;
import cn.harmonycloud.pmp.resp.R;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "tagProvider",path = FeignConstant.PROVIDER+"/tag",url = "${amp.url:http://localhost:8080/}")
public interface ITagProvider {

    @PostMapping("/create")
    @ApiOperation(value = "创建新标签")
    R<Long> add(@RequestBody Tag tag);

    /**
     * 根据标签分类code查询标签，分scope
     */
    @ApiOperation(value = "根据标签分类code获取标签列表")
    @GetMapping("/classificationCode/list")
    R<List<Tag>> getByClassificationCodeAndScope(@SpringQueryMap TagsByClassificationCode tagClassificationCode);

    /**
     * 根据标签idList获取标签列表
     */
    @GetMapping("/ids/list")
    @ApiOperation(value = "根据标签idList获取标签列表")
    R<List<Tag>> getTagsByIds(@RequestParam("ids") List<Long> ids);

    /**
     * 根据标签idList获取标签nameMap
     */
    @GetMapping("/ids/map")
    @ApiOperation(value = "根据标签idList获取标签Name的map")
    R<Map<Long,String>> getTagNameMapByIds(@RequestParam("ids") List<Long> ids);

    /**
     * 根据标签idList获取标签对象的map
     * @param ids
     * @return
     */
    @GetMapping("/ids/entityMap")
    @ApiOperation(value = "根据标签idList获取标签对象的map")
    R<Map<Long,Tag>> getTagMapByIds(@RequestParam("ids") List<Long> ids);

    /**
     * 获取标签、标签分类树,支持标签名称的模糊查询
     */
    @ApiOperation(value = "获取标签分类-标签树")
    @GetMapping("/tagTagClass/tree")
    R<List<Tree<Long>>> getTagTagClassTree(@RequestParam("typeCode") String typeCode, @RequestParam("instanceId") Long instanceId, @RequestParam(value = "queryParam",required = false)String queryParam);


    /**
     * 获取标签、标签分类树,支持标签名称的模糊查询
     */
    @GetMapping("/name")
    @ApiOperation(value = "查询标签分页列表，支持模糊查询")
    R<Long> getByNameOrAdd(@SpringQueryMap TagByNameDto tagByNameDto);


    /**
     * 根据实例id查询标签
     */
    @GetMapping("/instance")
    @ApiOperation(value = "根据实例id查询标签")
    R<List<TagVo>> getVosByRelateId(@RequestParam("relateTypeCode") String relateTypeCode, @RequestParam("relateId") Long relateId);

    /**
     * 根据实例ids查询标签
     */
    @GetMapping("/instance/map")
    @ApiOperation(value = "根据实例ids查询标签")
    R<Map<Long,List<TagVo>>> getMapByRelateIds(@RequestParam("relateTypeCode") String relateTypeCode, @RequestParam("relateIds") List<Long> relateIds);
}
