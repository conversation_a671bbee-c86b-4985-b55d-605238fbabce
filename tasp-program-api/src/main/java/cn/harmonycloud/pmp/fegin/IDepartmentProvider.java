package cn.harmonycloud.pmp.fegin;

import cn.harmonycloud.pmp.constant.FeignConstant;
import cn.harmonycloud.pmp.model.entity.Department;
import cn.harmonycloud.pmp.resp.R;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @describe
 * @author: wangkuan
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "departmentProvider",url = "${amp.url:http://localhost:8080/}",path = FeignConstant.PROVIDER+"/departments")
public interface IDepartmentProvider {

    @GetMapping("/staff/tree")
    @ApiOperation(value = "获取拉平部门以及人员树", notes = "获取拉平部门以及人员树")
    R<List<Tree<Long>>> getDepartmentAndStaffTreeList(@SpringQueryMap Department department);

    @GetMapping("/batch")
    @ApiOperation(value = "根据机构id获取用户", notes = "根据机构id获取用户")
    R<List<Department>> getByOrganId(@RequestParam("ids") List<Long> departIds);

    @GetMapping
    @ApiOperation(value = "获取部门树", notes = "获取部门树")
    R<List<Tree<Long>>> getTreeList(@RequestParam("organizationId")Long organizationId);

    @GetMapping("/list/organId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "organId", value = "租户id", paramType = "query", dataType = "Long",dataTypeClass = Long.class,required = true)
    })
    @ApiOperation(value = "获取租户下的全量部门", notes = "获取租户下的全量部门")
    R<List<Department>> getDepartmentListByOrganId(@RequestParam("organId") Long organId);
}
