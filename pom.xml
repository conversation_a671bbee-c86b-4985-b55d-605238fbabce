<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.harmonycloud</groupId>
    <artifactId>tasp-program-svc</artifactId>
    <version>2.4.0-SNAPSHOT</version>
    <name>devops-development</name>
    <description>Harmony-cloud默认代码生成</description>
    <packaging>pom</packaging>
    <!--项目子模块-->
    <modules>
        <module>tasp-program-api</module>
        <module>tasp-program-biz</module>
        <module>tasp-program-common</module>
    </modules>

    <properties>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>33.1.0-jre</guava.version>
        <lombok.version>1.18.20</lombok.version>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.cloud.version>2025.0.0</spring.cloud.version>
        <spring.cloud.alibaba.version>2023.0.3.3</spring.cloud.alibaba.version>
        <spring.boot.version>3.5.3</spring.boot.version>
        <spring.version>6.2.8</spring.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <!-- 或直接指定 JGit 版本 -->
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit</artifactId>
                <version>6.5.0.202303070854-r</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 强制统一Spring版本，避免NestedIOException问题 -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>datasource-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>mybatis-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>openapi-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>redis-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>sequence-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>tenant-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.harmonycloud</groupId>
                <artifactId>web-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--    <distributionManagement>-->
    <!--        <repository>-->
    <!--            <id>nexus-releases</id>-->
    <!--            <name>repository-releases</name>-->
    <!--            <url>http://10.40.1.96:18081/nexus/content/repositories/releases/</url>-->
    <!--        </repository>-->
    <!--        <snapshotRepository>-->
    <!--            <id>nexus-snapshots</id>-->
    <!--            <name>repository-snapshots</name>-->
    <!--            <url>http://10.40.1.96:18081/nexus/content/repositories/snapshots/</url>-->
    <!--        </snapshotRepository>-->
    <!--    </distributionManagement>-->
    <build>
        <finalName>app</finalName>
        <plugins>
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <version>3.8.1</version>-->
<!--                <configuration>-->
<!--                    <annotationProcessorPaths>-->
<!--                        &lt;!&ndash; 必须放在lombok之前 &ndash;&gt;-->
<!--                        <path>-->
<!--                            <groupId>org.mapstruct</groupId>-->
<!--                            <artifactId>mapstruct-processor</artifactId>-->
<!--                            <version>1.5.5.Final</version>-->
<!--                        </path>-->
<!--                        <path>-->
<!--                            <groupId>org.projectlombok</groupId>-->
<!--                            <artifactId>lombok</artifactId>-->
<!--                            <version>${lombok.version}</version>-->
<!--                        </path>-->
<!--                    </annotationProcessorPaths>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
