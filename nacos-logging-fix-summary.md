# Nacos Logging Configuration Fix

## Problem
The error `ERROR in ch.qos.logback.core.rolling.RollingFileAppender[CONFIG_LOG_FILE] - 'File' option has the same value "/Users/<USER>/logs/nacos/config.log" as that given for appender [CONFIG_LOG_FILE] defined earlier.` indicates that there are duplicate CONFIG_LOG_FILE appenders trying to use the same log file path.

This is a common issue with Nacos client logging configuration where Nacos's internal logback configuration conflicts with the application's logging setup.

## Root Cause
1. Nacos client has its own internal logback configuration that creates a `CONFIG_LOG_FILE` appender
2. The application's logback configuration might be interfering with Nacos's logging setup
3. Multiple instances of the same appender are being created with the same file path

## Solution Applied

### 1. Updated Application.java
Added system properties to disable Nacos internal logging configuration:
```java
// 禁用 Nacos 内部日志配置，避免 CONFIG_LOG_FILE 冲突
System.setProperty("nacos.logging.default.config.enabled", "false");
System.setProperty("nacos.logging.config", "");
System.setProperty("com.alibaba.nacos.naming.log.level", "WARN");
System.setProperty("com.alibaba.nacos.config.log.level", "WARN");
```

### 2. Updated application.yml
- Disabled Nacos default logging configuration in both discovery and config sections
- Removed conflicting nacos.logging.path configuration
- Set appropriate log levels for Nacos components

### 3. Updated logback-spring.xml
- Added specific logger configurations for Nacos components with `additivity="false"`
- Configured Nacos loggers to only use CONSOLE appender
- Prevented Nacos logs from propagating to root logger

## Key Configuration Changes

### application.yml
```yaml
spring:
  cloud:
    nacos:
      discovery:
        logging:
          default-config-enabled: false
      config:
        logging:
          default-config-enabled: false

logging:
  level:
    com.alibaba.nacos: WARN

nacos:
  logging:
    default-config-enabled: false
```

### logback-spring.xml
```xml
<!-- Nacos 日志配置 - 禁用内部日志配置，避免冲突 -->
<logger name="com.alibaba.nacos" level="WARN" additivity="false">
    <appender-ref ref="CONSOLE"/>
</logger>
<logger name="com.alibaba.nacos.client.config" level="WARN" additivity="false">
    <appender-ref ref="CONSOLE"/>
</logger>
<logger name="com.alibaba.nacos.client.naming" level="WARN" additivity="false">
    <appender-ref ref="CONSOLE"/>
</logger>
```

## Verification
After applying these changes:
1. Restart the application
2. Check that the CONFIG_LOG_FILE error no longer appears
3. Verify that Nacos functionality still works correctly
4. Confirm that application logs are properly written to the configured locations

## Additional Notes
- The `additivity="false"` setting prevents Nacos logs from being processed by the root logger
- System properties are set before Spring Boot initialization to ensure they take effect
- All Nacos logging is now directed only to the console appender
- This solution maintains Nacos functionality while eliminating the logging conflicts
