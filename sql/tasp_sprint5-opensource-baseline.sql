SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for open_source_component_baseline
-- ----------------------------
DROP TABLE IF EXISTS `open_source_component_baseline`;
CREATE TABLE `open_source_component_baseline` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一标识（主键）',
  `name` varchar(128) NOT NULL COMMENT '组件名称',
  `category` varchar(32) DEFAULT NULL COMMENT '分类',
  `version` varchar(64) NOT NULL COMMENT '版本',
  `manager` varchar(64) DEFAULT NULL COMMENT '负责人',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `del_flag` tinyint(1) unsigned zerofill DEFAULT '0' COMMENT '删除标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='开源组件基线表';

SET FOREIGN_KEY_CHECKS = 1;