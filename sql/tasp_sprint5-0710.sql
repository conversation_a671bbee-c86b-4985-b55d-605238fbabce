ALTER TABLE `tasp_develop`.`scaffold_template`
    ADD COLUMN `service_name` varchar(64) NULL COMMENT '脚手架的服务名' AFTER `pic_url`,
ADD COLUMN `package_path` varchar(64) NULL COMMENT '后端脚手的包路径' AFTER `service_name`;

UPDATE `tasp_develop`.`scaffold_template` SET `service_name` = 'trina-frontend-web' WHERE `id` = 7
UPDATE `tasp_develop`.`scaffold_template` SET `service_name` = 'trina-backend',package_path = 'com.trinasolar.backend' WHERE `id` = 8
ALTER TABLE `tasp_develop`.`scaffold_template`
    ADD COLUMN `add_component` tinyint(1) ZEROFILL NULL DEFAULT 0 COMMENT '集成组件（是/否）' AFTER `package_path`;

UPDATE `tasp_develop`.`scaffold_template` SET `add_component` = 1 WHERE `id` = 8
UPDATE `tasp_develop`.`scaffold_template` SET `add_component` = 1 WHERE `id` = 1