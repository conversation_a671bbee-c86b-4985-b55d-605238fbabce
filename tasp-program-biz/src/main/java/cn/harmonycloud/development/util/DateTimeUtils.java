package cn.harmonycloud.development.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateTimeUtils {
    
    private static final DateTimeFormatter DEFAULT_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 将字符串转换为LocalDateTime
     * @param dateTimeStr 日期时间字符串，格式应为"yyyy-MM-dd HH:mm:ss"
     * @return 转换后的LocalDateTime对象
     * @throws IllegalArgumentException 如果字符串格式不正确
     */
    public static LocalDateTime stringToLocalDateTime(String dateTimeStr) {
        try {
            return LocalDateTime.parse(dateTimeStr, DEFAULT_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式不正确，应为yyyy-MM-dd HH:mm:ss", e);
        }
    }
    
    /**
     * 将LocalDateTime转换为字符串
     * @param dateTime LocalDateTime对象
     * @return 格式化后的字符串
     */
    public static String localDateTimeToString(LocalDateTime dateTime) {
        return dateTime.format(DEFAULT_FORMATTER);
    }
}