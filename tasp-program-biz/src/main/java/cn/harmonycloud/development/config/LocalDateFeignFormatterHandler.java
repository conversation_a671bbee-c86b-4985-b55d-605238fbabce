package cn.harmonycloud.development.config;

import cn.hutool.core.date.DatePattern;
import org.springframework.cloud.openfeign.FeignFormatterRegistrar;
import org.springframework.format.FormatterRegistry;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 解决feign时不时时间处理异常 24-4-18
 *
 * <AUTHOR>
 * @date 2024-04-18 17:50:55
 */
@Component
public class LocalDateFeignFormatterHandler implements FeignFormatterRegistrar {
    @Override
    public void registerFormatters(FormatterRegistry registry) {
        registry.addConverter(LocalDate.class, String.class, source -> {
            DateTimeFormatter df = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
            return source.format(df);
        });
        registry.addConverter(String.class, LocalDateTime.class, source -> {
            DateTimeFormatter df = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
            return LocalDateTime.parse(source, df);
        });
    }
}