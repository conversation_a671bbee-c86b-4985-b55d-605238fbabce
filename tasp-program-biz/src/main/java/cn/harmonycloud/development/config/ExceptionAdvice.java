package cn.harmonycloud.development.config;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.exception.UnauthorizedException;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.validation.UnexpectedTypeException;
import java.util.List;

/**
 * @Description 异常拦截统一处理
 * <AUTHOR>
 * @Date 2022/6/25 4:42 下午
 **/
@Slf4j
@RestControllerAdvice
public class ExceptionAdvice {

//    /**
//     * 自定义异常处理
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(SystemException.class)
//    public BaseResult handlerSystemException(SystemException e) {
//        String errMsg = StringUtils.isEmpty(e.getExMsg()) ? e.getExceptionCode().getMsg() : e.getExMsg();
//        log.error(errMsg);
//        return BaseResult.failed().setCode(e.getExceptionCode().getCode()).setMsg(errMsg);
//    }
//
//    @ExceptionHandler(BindException.class)
//    public BaseResult handlerBindException(BindException e) {
//        List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
//        String defaultMessage = allErrors.get(0).getDefaultMessage();
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID.getCode()).setMsg(defaultMessage);
//    }
//
//    @ExceptionHandler(UnauthorizedException.class)
//    @ResponseStatus(HttpStatus.UNAUTHORIZED)
//    public BaseResult handlerSystemException(UnauthorizedException e) {
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_OAUTH_CHECK_FAIL.getCode()).setMsg(ExceptionCode.REQUEST_OAUTH_CHECK_FAIL.getMsg());
//    }
//
//    /**
//     * 拦截请求方式错误
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
//    public BaseResult handlerHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
//        log.debug(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_METHOD_NOT_ALLOWED.getCode()).setMsg(ExceptionCode.REQUEST_METHOD_NOT_ALLOWED.getMsg());
//    }
//
//    /**
//     * 拦截参数体错误
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(HttpMessageNotReadableException.class)
//    public BaseResult handlerHttpMessageNotReadableException(HttpMessageNotReadableException e) {
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_MESSAGE_NOT_READ.getCode()).setMsg(ExceptionCode.REQUEST_MESSAGE_NOT_READ.getMsg());
//    }
//
//    /**
//     * 拦截参数类型错误
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
//    public BaseResult handlerMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_MESSAGE_NOT_READ.getCode()).setMsg(ExceptionCode.REQUEST_MESSAGE_NOT_READ.getMsg());
//    }
//
//    /**
//     * 拦截参数校验异常
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(MethodArgumentNotValidException.class)
//    public BaseResult handlerNoHandlerFoundException(MethodArgumentNotValidException e) {
//        String defaultMessage = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID.getCode()).setMsg(defaultMessage);
//    }
//
//    /**
//     * 拦截参数校验异常
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(UnexpectedTypeException.class)
//    public BaseResult handlerNoHandlerFoundException(UnexpectedTypeException e) {
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID.getCode()).setMsg("defaultMessage");
//    }
//
//    /**
//     * 拦截参数校验异常
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(MissingServletRequestParameterException.class)
//    public BaseResult handlerNoHandlerFoundException(MissingServletRequestParameterException e) {
//        log.error(e.getMessage());
//        return BaseResult.failed().setCode(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID.getCode()).setMsg("参数缺失："+e.getParameterName());
//    }
//


//    /**
//     * 未知异常处理
//     * @param exception
//     * @return
//     */
//    @ExceptionHandler(Exception.class)
//    public BaseResult handlerException(Exception exception) {
//        log.error(LogUtils.throwableExceptionString(exception));
//        return BaseResult.failed().setCode(ExceptionCode.INNER_EXCEPTION.getCode()).setMsg(ExceptionCode.INNER_EXCEPTION.getMsg());
//    }

}
