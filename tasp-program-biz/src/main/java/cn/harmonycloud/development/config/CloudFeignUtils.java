package cn.harmonycloud.development.config;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/29 9:40 上午
 **/
public class CloudFeignUtils {

    private static ThreadLocal<Boolean> enable = new ThreadLocal<>();

    private static ThreadLocal<String> token = new ThreadLocal<>();

    public static void setToken(String tokenString){
        token.set(tokenString);
    }

    public static String getToken(){
        return token.get();
    }

    public static void clearToken(){
        token.remove();
    }

    public static void openTransferHeader(){
        enable.set(true);
    }

    public static void closeTransferHeader(){
        enable.set(false);
    }

    public static Boolean getTransferHeader(){
        return enable.get();
    }
}
