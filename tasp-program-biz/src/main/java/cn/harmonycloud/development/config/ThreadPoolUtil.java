package cn.harmonycloud.development.config;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadPoolUtil {

    /**
     * 核心线程数
     **/
    private final static Integer CORE_POOL_SIZE = 20;
    /**
     * 最大线程数
     **/
    private final static Integer MAX_POOL_SIZE = 40;
    /**
     * 线程池维护线程所允许的空闲时间
     **/
    private final static int KEEP_ALIVE_TIME = 60 * 1000;
    /**
     * 线程名字
     **/
    private final static String CUSTOM_THREAD_NAME = "ThreadPool-";

    public final static ThreadPoolExecutor OA_THREAD_POOL_EXECUTOR =
        new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(10), new CustomFactory(Thread.NORM_PRIORITY));

    public static void execute(Runnable runnable) {
        OA_THREAD_POOL_EXECUTOR.execute(runnable);
    }

    public static void cancle(Runnable runnable) {
        OA_THREAD_POOL_EXECUTOR.remove(runnable);
    }

    private static class CustomFactory implements ThreadFactory {

        int mPriority;
        private AtomicInteger mInteger = new AtomicInteger(1);

        CustomFactory(int normal) {
            this.mPriority = normal;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread mThread = new Thread( r, CUSTOM_THREAD_NAME + mInteger.getAndIncrement());
            mThread.setPriority(mPriority);
            return mThread;
        }
    }
}