package cn.harmonycloud.development.config;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Data
@ConfigurationProperties(prefix = "git-sync")
@Accessors(chain = true)
public class GitSyncConfig {
    /**
     * 源仓库HTTP地址
     */
    private String pullGitUrl;
    
    /**
     * 拉取分支名称（默认主分支）
     */
    private String pullBranch;

    /**
     * 目标仓库HTTP地址（默认加速版springboot模板仓库）
     */
    private String pushGitUrl;
    
    /**
     * 推送分支名称（默认主分支）
     */
    private String pushBranch;

    /**
     * GitLab拉取私有访问令牌（需替换为有效token）
     */
    private String pullPrivateToken;

    /**
     * GitLab推送私有访问令牌（需替换为有效token）
     */
    private String pushPrivateToken;

    /**
     * 服务英文名称
     */
    private String programNameEn;

    /**
     * 包名
     */
    private String packageName;
}