package cn.harmonycloud.development.config;

import feign.Client;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.Retryer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Import({ FeignServiceInstanceInterceptor.class})
public class FeignConfig {

    @Autowired
    private FeignServiceInstanceInterceptor feignServiceInstanceInterceptor;

    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean
    public RequestInterceptor serviceInstanceInterceptor() {
        return feignServiceInstanceInterceptor;
    }

//    @Bean
//    public Client feignClient(LoadBalancerClient loadBalancerClient,
//                             LoadBalancerClientFactory loadBalancerClientFactory)
//                             throws NoSuchAlgorithmException, KeyManagementException {
//        log.info("创建 Feign 负载均衡客户端");
//
//        SSLContext sslContext = SSLContext.getInstance("TLS");
//        sslContext.init(null, new TrustManager[]{new X509TrustManager() {
//            @Override
//            public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
//            }
//
//            @Override
//            public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
//            }
//
//            @Override
//            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
//                return new java.security.cert.X509Certificate[]{};
//            }
//        }}, new SecureRandom());
//
//        // 使用 FeignBlockingLoadBalancerClient 替代默认的 Client
//        return new FeignBlockingLoadBalancerClient(
//                new Client.Default(sslContext.getSocketFactory(), (hostname, session) -> true),
//                loadBalancerClient,
//                loadBalancerClientFactory);
//    }

    @Bean
    public Request.Options options() {
        // 设置连接超时为5秒，读取超时为10秒
        return new Request.Options(5, TimeUnit.SECONDS, 10, TimeUnit.SECONDS, true);
    }

    @Bean
    public Retryer retryer() {
        // 重试3次，初始间隔100ms，最大间隔1s
        return new Retryer.Default(100, 1000, 3);
    }
}