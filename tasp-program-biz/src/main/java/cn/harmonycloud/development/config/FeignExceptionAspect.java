package cn.harmonycloud.development.config;

import cn.harmonycloud.common.core.exception.UnauthorizedException;
import cn.harmonycloud.constants.ApiConstance;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.util.LogUtils;
import com.alibaba.fastjson.JSONObject;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description fei调用异常统一处理
 * 网络异常、code失败等
 * <AUTHOR>
 * @Date 2022/10/8 5:28 下午
 **/
@Aspect
@Slf4j
@Component
public class FeignExceptionAspect  {

    @Around("@within(cn.harmonycloud.development.config.CloudFeignException)")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        Proxy target = (Proxy) joinPoint.getTarget();
        List<Class<?>> collect = Arrays.stream(target.getClass().getInterfaces()).collect(Collectors.toList());
        CloudFeignException annotation = collect.get(0).getAnnotation(CloudFeignException.class);
        try {
            Object proceed = joinPoint.proceed();
            String result = JSONObject.toJSONString(proceed);
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("code") == ApiConstance.OAUTH) {
                throw new UnauthorizedException(jsonObject.getString("msg"));
            }
            if (jsonObject.getInteger("code") != ApiConstance.SUCCESS) {
                throw new SystemException(annotation.exceptionCode(), jsonObject.getInteger("code"), jsonObject.getString("msg"));
            }
            return proceed;
        } catch (Throwable throwable) {
            if(throwable instanceof FeignException.Unauthorized){
                throw new UnauthorizedException(throwable.getMessage());
            }
            if (throwable instanceof SystemException) {
                throw throwable;
            }
            if (throwable instanceof UnauthorizedException) {
                throw throwable;
            }
            log.error(LogUtils.throwableExceptionString(throwable));
            throw new SystemException(annotation.exceptionCode());
        }
    }

}
