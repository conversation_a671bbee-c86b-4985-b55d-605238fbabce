package cn.harmonycloud.development.config;

import cn.harmonycloud.development.pojo.SystemConstance;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @Description 微服务调用统一添加请求头
 * <AUTHOR>
 * @Date 2022/6/27 9:22 上午
 **/
@Slf4j
@Configuration
public class CloudFeignConfiguration implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {

        if (CloudFeignUtils.getTransferHeader() != null && !CloudFeignUtils.getTransferHeader()) {
            String token = CloudFeignUtils.getToken();
            if(StringUtils.isNotEmpty(CloudFeignUtils.getToken())){
                requestTemplate.header(SystemConstance.SCM_TOKEN, token);
            }
            return;
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 定时任务触发
        if (servletRequestAttributes == null) {
            RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(new MockHttpServletRequest()));
            servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        }
        HttpServletRequest request = servletRequestAttributes.getRequest();
        requestTemplate.header(SystemConstance.OAUTH_TOKEN_HEADER, request.getHeader(SystemConstance.OAUTH_TOKEN_HEADER));
        requestTemplate.header(SystemConstance.OAUTH_TENANT_HEADER, request.getHeader(SystemConstance.OAUTH_TENANT_HEADER));
        requestTemplate.header(SystemConstance.AMP_APP_ID, request.getHeader(SystemConstance.AMP_APP_ID));
        requestTemplate.header(SystemConstance.AMP_APP_CODE, request.getHeader(SystemConstance.AMP_APP_CODE));
        String token = CloudFeignUtils.getToken();
        if(StringUtils.isNotEmpty(CloudFeignUtils.getToken())){
            requestTemplate.header(SystemConstance.SCM_TOKEN, token);
        }
    }

}
