package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.pojo.dto.subsystem.*;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.service.mapstruct.DevopsSubSystemMapstruct;
import cn.harmonycloud.development.service.mapstruct.VersionMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.SubSystemComponentEnum;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectRequest;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.coderepo.MergeStatisticInfoResponse;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigDTO;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigSaveRequest;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import cn.harmonycloud.pojo.subsystem.SubsystemQueryDto;
import cn.harmonycloud.pojo.version.MajorVersionDto;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【hzbank_sub_system(应用表)】的数据库操作Service实现
 * @createDate 2022-07-28 09:15:15
 */
@Service
public class SubSystemServiceImpl implements SubSystemService {

    private final CodeRepoService codeRepoService;
    private final SubsystemComponentRepository subsystemComponentRepository;
    private final PermissionRepository permissionRepository;
    private final PipelineService pipelineService;
    private final SubsystemRepository subsystemRepository;
    private final DevopsSubSystemMapstruct devopsSubSystemMapstruct;
    private final DevopsSubSystemMapstruct subSystemMapstruct;
    private final SystemComponentService systemComponentService;
    private final CodeProjectRepository codeProjectRepository;
    private final IamRepository iamRepository;
    private final @Lazy SubSystemComponentService subSystemComponentService;
    private final @Lazy SubSystemMemberService subSystemMemberService;
    private final FeatureRepository featureRepository;
    private final DevopsSystemRepository devopsSystemRepository;
    private final VersionRepository versionRepository;
    private final VersionMapstruct versionMapstruct;
    private final CodeMergeRepository codeMergeRepository;
    private final MajorVersionRepository majorVersionRepository;
    private final IssuesSubsystemRepository issuesSubsystemRepository;
    private final SubSystemConfigRepository subSystemConfigRepository;
    private final GitLabProjectService ********************;

    @Value("${biz.regex.subsystemName:^(?![-_])(?!.*?[-_]$)[a-zA-Z0-9_\\u4e00-\\u9fa5-]{1,100}$}")
    private String subsystemNameRegex;
    @Value("${biz.regex.subsystemCode:^(?![-_])(?!.*?[-_]$)(?!.*\\.(git|atom)$)[a-zA-Z0-9_-]{1,30}$}")
    private String subsystemCodeRegex;

    @Autowired
    public SubSystemServiceImpl(
            CodeRepoService codeRepoService,
            SubsystemComponentRepository subsystemComponentRepository,
            PermissionRepository permissionRepository,
            PipelineService pipelineService,
            SubsystemRepository subsystemRepository,
            DevopsSubSystemMapstruct devopsSubSystemMapstruct,
            DevopsSubSystemMapstruct subSystemMapstruct,
            SystemComponentService systemComponentService,
            CodeProjectRepository codeProjectRepository,
            IamRepository iamRepository,
            @Lazy SubSystemComponentService subSystemComponentService,
            @Lazy SubSystemMemberService subSystemMemberService,
            FeatureRepository featureRepository,
            DevopsSystemRepository devopsSystemRepository,
            VersionRepository versionRepository,
            VersionMapstruct versionMapstruct,
            CodeMergeRepository codeMergeRepository,
            MajorVersionRepository majorVersionRepository,
            IssuesSubsystemRepository issuesSubsystemRepository,
            SubSystemConfigRepository subSystemConfigRepository,
            GitLabProjectService ********************) {
        this.codeRepoService = codeRepoService;
        this.subsystemComponentRepository = subsystemComponentRepository;
        this.permissionRepository = permissionRepository;
        this.pipelineService = pipelineService;
        this.subsystemRepository = subsystemRepository;
        this.devopsSubSystemMapstruct = devopsSubSystemMapstruct;
        this.subSystemMapstruct = subSystemMapstruct;
        this.systemComponentService = systemComponentService;
        this.codeProjectRepository = codeProjectRepository;
        this.iamRepository = iamRepository;
        this.subSystemComponentService = subSystemComponentService;
        this.subSystemMemberService = subSystemMemberService;
        this.featureRepository = featureRepository;
        this.devopsSystemRepository = devopsSystemRepository;
        this.versionRepository = versionRepository;
        this.versionMapstruct = versionMapstruct;
        this.codeMergeRepository = codeMergeRepository;
        this.majorVersionRepository = majorVersionRepository;
        this.issuesSubsystemRepository = issuesSubsystemRepository;
        this.subSystemConfigRepository = subSystemConfigRepository;
        this.******************** = ********************;
    }

    @Override
    public SubSystemInfoDto info(Long subSystemId) {
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(subSystemId);
        if (devopsSubsystem == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "应用信息不存在");
        }
        return info(devopsSubsystem);
    }

    @Override
    public SubSystemInfoDto infoWithGitlab(Long subSystemId) {
        SubSystemInfoDto info = this.info(subSystemId);
        GitProjectDto project = ********************.gitlabInfo(subSystemId);
        if(project != null){
            info.setGitlabId(project.getId());
            info.setServerId(project.getServerId());
            info.setGitlabRepo(project.getInnerProxyHttp());
            info.setSshUrlToRepo(project.getSshUrl());
        }
        return info;
    }

    @Override
    public SubSystemInfoDto infoByCode(String subSystemCode) {
        DevopsSubSystem devopsSubSystem = subsystemRepository.getByParams(subSystemCode);
        if (devopsSubSystem == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "应用信息不存在");
        }
        return info(devopsSubSystem);
    }

    @Override
    public List<SubSystemGitlab> listByGitlabIds(List<String> gitlabIds) {
        List<SubSystemComponent> subSystemComponents = subsystemComponentRepository.listByParam(SystemConstance.ComponentType.GITLAB, gitlabIds);
        List<Long> subSystemIds = subSystemComponents.stream().map(SubSystemComponent::getSubSystemId).collect(Collectors.toList());
        Map<Long, DevopsSubSystem> mapSubSystem = subsystemRepository.mapByIds(subSystemIds);
        List<Long> systemIds = subSystemComponents.stream().map(SubSystemComponent::getSystemId).distinct().collect(Collectors.toList());
        Map<Long, DevopsSystem> mapSystem = devopsSystemRepository.mapByIds(systemIds);
        Map<String, List<SubSystemComponent>> component = subSystemComponents.stream().collect(Collectors.groupingBy(SubSystemComponent::getComponentKey));
        return gitlabIds.stream().map(id -> {
            SubSystemGitlab subSystemGitlab = new SubSystemGitlab();
            subSystemGitlab.setGitlabId(Integer.parseInt(id));
            List<SubSystemComponent> components = component.getOrDefault(id, new ArrayList<>()).stream().filter(c -> mapSubSystem.keySet().contains(c.getSubSystemId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(components)) {
                Long subSystemId = components.get(0).getSubSystemId();
                DevopsSubSystem orDefault = mapSubSystem.getOrDefault(subSystemId, new DevopsSubSystem());
                subSystemGitlab.setSubSystemId(subSystemId);
                subSystemGitlab.setSubSystemName(orDefault.getFullNameCn());
                subSystemGitlab.setSubCode(orDefault.getSubCode());
                subSystemGitlab.setSystemId(orDefault.getSystemId());
                subSystemGitlab.setSystemName(mapSystem.getOrDefault(orDefault.getSystemId(), new DevopsSystem()).getSubFullNameCn());
                subSystemGitlab.setTechDirectorId(orDefault.getTechDirectorId());
            }
            return subSystemGitlab;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Long> listResourceIds(Long systemId) {
        if (systemId == null) {
            return new ArrayList<>();
        }
        String subsystem = SystemConstance.AmpResourceTypeCode.SUBSYSTEM;
        String system = SystemConstance.AmpResourceTypeCode.SYSTEM;
        List<ResourceInstance> resourceInstances = permissionRepository.resourceList(system, systemId.toString(), subsystem);
        return resourceInstances.stream().map(ri -> ri.getResourceInstanceId()).collect(Collectors.toList());
    }
    @Transactional
    @Override
    public void postRemoveSystem(Long systemId) {
        List<DevopsSubSystem> subSystemDataVOS = subsystemRepository.listBySystemId(systemId);
        List<Long> subIds = subSystemDataVOS.stream().map(subsystem -> subsystem.getId()).collect(Collectors.toList());
        User currentUser = iamRepository.getCurrentUser();
        if (CollectionUtils.isNotEmpty(subIds)) {
            permissionRepository.batchDeleteResourceInstance(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subIds);
            subsystemRepository.batchRemoveLogic(subIds, currentUser);
            subSystemConfigRepository.removeBySubIds(subIds);
        }
        subsystemComponentRepository.deleteComponentBySystemId(systemId);
        pipelineService.unBind(subIds);
    }

    @Override
    public List<SubsystemDto> list(SubsystemQueryDto queryDto) {
        List<DevopsSubSystem> list = subsystemRepository.listByParams(queryDto.getSubsystemIds(), queryDto.getSystemIds());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> subIds = list.stream().map(sub -> sub.getId()).collect(Collectors.toList());
        List<DevopsSystem> systems = devopsSystemRepository.listByIds(list.stream().map(sub -> sub.getSystemId()).collect(Collectors.toList()));
        Map<Long, DevopsSystem> systemMap = systems.stream().collect(Collectors.toMap(DevopsSystem::getId, s -> s));
        Map<Long, List<SubSystemComponent>> componentMap = subsystemComponentRepository.mapComponent(subIds, SystemConstance.ComponentType.GITLAB);
        return list.stream().map(devopsSubSystem -> {
            SubsystemDto subsystemDto = devopsSubSystemMapstruct.toSubsystemDto(devopsSubSystem);
            List<SubSystemComponent> components = componentMap.get(devopsSubSystem.getId());
            if (CollectionUtils.isNotEmpty(components)) {
                int key = Integer.parseInt(components.get(0).getComponentKey());
                subsystemDto.setGitlabId(key);
            }
            subsystemDto.setSystemName(systemMap.getOrDefault(devopsSubSystem.getSystemId(), new DevopsSystem()).getSubFullNameCn());
            return subsystemDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SubsystemDto> listResource(SubsystemGeneralRequest request) {
        List<Long> ids = this.listResourceIds(request.getSystemId());
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<DevopsSubSystem> subList = subsystemRepository.listByParams(request.getSystemId(), ids, request.getFullNameCn());
        if (CollectionUtils.isEmpty(subList)) {
            return new ArrayList<>();
        }
        List<Long> subIds = subList.stream().map(sub -> sub.getId()).collect(Collectors.toList());
        List<Long> directorIds = subList.stream().filter(sub -> sub.getTechDirectorId() != null).map(sub -> sub.getTechDirectorId()).collect(Collectors.toList());
        List<DevopsFeature> listFeature = featureRepository.listBySubSystemIds(subIds);
        Map<Long, List<DevopsFeature>> mapFeature = listFeature.stream().collect(Collectors.groupingBy(DevopsFeature::getSubSystemId));
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(directorIds);
        List<SubsystemDto> data = subList.stream().map(sub -> {
            SubsystemDto subsystemDto = subSystemMapstruct.toDevopsSubSystem(sub);
            List<DevopsFeature> subFeature = mapFeature.get(sub.getId());
            int featureNumInOneSubSystem = CollectionUtils.isEmpty(subFeature) ? 0 : subFeature.size();
            subsystemDto.setFeatureNum(featureNumInOneSubSystem);
            User user = userMap.get(subsystemDto.getTechDirectorId());
            if (user != null) {
                subsystemDto.setTechDirectorName(user.getName());
            }
            return subsystemDto;
        }).collect(Collectors.toList());
        if(request.getWithGitlab()){
            withGitlab(data);
        }
        if(request.getWithMergeStatistic()){
            withMergeStatistic(data, request.getWithGitlab());
        }
        return data;
    }

    private List<SubsystemDto> withMergeStatistic(List<SubsystemDto> data, boolean haveGitlab) {
        if(!haveGitlab){
            withGitlab(data);
        }
        List<Integer> gitlabIds = data.stream().filter(sub -> sub.getGitlabId() != null).map(sub -> sub.getGitlabId()).collect(Collectors.toList());
        List<MergeStatisticInfoResponse> mergeStatisticInfoResponses = codeMergeRepository.mergeStatistic(gitlabIds);
        Map<Integer, MergeStatisticInfoResponse> mapStatistic = mergeStatisticInfoResponses.stream().collect(Collectors.toMap(MergeStatisticInfoResponse::getGitlabId, r -> r));
        for (SubsystemDto datum : data) {
            if(datum.getGitlabId() == null){
                datum.setMergeStatisticInfoResponse(new MergeStatisticInfoResponse());
            }else{
                MergeStatisticInfoResponse orDefault = mapStatistic.getOrDefault(datum.getGitlabId(), new MergeStatisticInfoResponse());
                datum.setMergeStatisticInfoResponse(orDefault);
            }
        }
        return data;
    }


    private List<SubsystemDto> withGitlab(List<SubsystemDto> data) {
        if(CollectionUtils.isEmpty(data)){
            return data;
        }
        List<Long> subIds = data.stream().map(sub -> sub.getId()).collect(Collectors.toList());
        Map<Long, List<SubSystemComponent>> collect = subsystemComponentRepository.mapComponent(subIds, SystemConstance.ComponentType.GITLAB);
        for (SubsystemDto datum : data) {
            List<SubSystemComponent> gitlabs = collect.get(datum.getId());
            if(CollectionUtils.isEmpty(gitlabs)){
                continue;
            }
            if(gitlabs.size() > 1){
                throw new SystemException(ExceptionCode.DATA_EXCEPTION_FAIL, "应用：" + datum.getFullNameCn() + "代码库数据错误，请联系管理员处理");
            }
            datum.setGitlabId(Integer.parseInt(gitlabs.get(0).getComponentKey()));
        }
        return data;
    }

    @Override
    public DevopsSubSystem creatSubsystemPost(DevopsSubSystem subsystem, SubsystemCreateRequest request) {
        User currentUser = iamRepository.getCurrentUser();
        addAdminRole(subsystem, request, currentUser);
        issuesSubsystemRepository.createIssues(subsystem);
        Integer codeRepoIdBySystemId = codeRepoService.getCodeRepoIdBySystemId(subsystem.getId());
        if(codeRepoIdBySystemId != null){
            codeProjectRepository.initCodeScan(codeRepoIdBySystemId, subsystem.getTechnology());
        }
        return subsystem;
    }

    @Transactional(rollbackFor = Exception.class)
    public DevopsSubSystem createSubsystem(SubsystemCreateRequest request, User currentUser){
        DevopsSubSystem subsystem = saveLocal(request, currentUser);
        Integer gitlabId = ********************.createProject(subsystem.getId(), new ProjectRequest(), true, true).intValue();
        permissionRepository.resourceInstances(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subsystem.getId(), subsystem.getFullNameCn(), SystemConstance.AmpResourceTypeCode.SYSTEM, subsystem.getSystemId().toString());
        return subsystem;
    }

    @Override
    public SubsystemDetails update(SubsystemUpdateRequest request) {
        // 空格和重名校验，长度校验
        checkParam(request.getFullNameCn(), null, request.getSubDescCn());
        DevopsSubSystem old = subsystemRepository.getById(request.getId());
        if (old == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        User currentUser = iamRepository.getCurrentUser();
        List<DevopsSubSystem> devopsSubSystems = subsystemRepository.listByParams(old.getSystemId(), request.getFullNameCn());
        if (CollectionUtils.isNotEmpty(devopsSubSystems) && !devopsSubSystems.get(0).getId().equals(request.getId())) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "应用名称已存在");
        }
        DevopsSubSystem devopsSubSystem = devopsSubSystemMapstruct.toDevopsSubSystem(request);
        devopsSubSystem.setUpdateBy(currentUser.getId());
        devopsSubSystem.setUpdateTime(LocalDateTime.now());
        subsystemRepository.updateById(devopsSubSystem);
        permissionRepository.updateResourceInstances(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, devopsSubSystem.getId(), devopsSubSystem.getFullNameCn());
        codeRepoService.updateBySubsystem(devopsSubSystem);
        codeRepoService.initCodeScan(devopsSubSystem.getId(), devopsSubSystem.getTechnology());
        updateAdminRole(old, devopsSubSystem);
        DevopsSubSystem result = subsystemRepository.getById(devopsSubSystem.getId());
        Integer codeRepoIdBySystemId = codeRepoService.getCodeRepoIdBySystemId(devopsSubSystem.getId());
        SubsystemDetails subsystemDetails = devopsSubSystemMapstruct.toDevopsSubSystemDetails(result);
        subsystemDetails.setGitlabId(codeRepoIdBySystemId);
        if(codeRepoIdBySystemId != null){
            GitProjectDto project = codeProjectRepository.getProject(codeRepoIdBySystemId);
            subsystemDetails.setCodeRepo(project);
        }
        return subsystemDetails;
    }

    @Override
    public SubsystemDetails details(Long id, Boolean codeDetails) {
        DevopsSubSystem devopsSubSystem = subsystemRepository.getById(id);
        if (devopsSubSystem == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        SubsystemDetails subsystemDetails = subSystemMapstruct.toDevopsSubSystemDetails(devopsSubSystem);
        Integer codeRepoIdBySystemId = codeRepoService.getCodeRepoIdBySystemId(devopsSubSystem.getId());
        subsystemDetails.setGitlabId(codeRepoIdBySystemId);
        if(codeDetails != null && codeRepoIdBySystemId !=null && codeDetails){
            GitProjectDto project = codeProjectRepository.getProject(codeRepoIdBySystemId);
            subsystemDetails.setCodeRepo(project);
        }
        List<SubSystemComponent> components = subsystemComponentRepository.listByParam(id, SubSystemComponentEnum.CONFIG.getComponent());
        if(CollectionUtils.isNotEmpty(components)){
            subsystemDetails.setConfigId(Long.parseLong(components.get(0).getComponentKey()));
        }
        return subsystemDetails;
    }

    @Override
    public void remove(Long id) {
        User currentUser = iamRepository.getCurrentUser();
        subsystemRepository.removeLogic(id, currentUser);
        permissionRepository.deleteResourceInstances(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, id);
        subSystemConfigRepository.removeBySubIds(Lists.newArrayList(id));
        subsystemComponentRepository.deleteComponent(id);
        issuesSubsystemRepository.deleteIssues(id);
        pipelineService.unBind(Lists.newArrayList(id));
    }

    @Override
    public List<SubsystemDto> list(SubsystemGeneralRequest request) {

        SubsystemGeneralQuery subsystemGeneralQuery = subSystemMapstruct.toDevopsGeneral(request);
        if(StringUtils.isNotEmpty(request.getVersion())){
            List<VersionManagement> versionManagements = versionRepository.versionList(request.getVersion());
            if(CollectionUtils.isNotEmpty(versionManagements)){
                List<Long> collect = versionManagements.stream().map(v -> v.getSubSystemId()).collect(Collectors.toSet()).stream().collect(Collectors.toList());
                subsystemGeneralQuery.getVersionSubIds().addAll(collect);
            }
        }
        if(request.getProjectId() != null && subsystemGeneralQuery.getSystemId() == null) {
            List<DevopsSystem> systemList = devopsSystemRepository.listByProject(request.getProjectId());
            if(CollectionUtils.isEmpty(systemList)) {
                return new ArrayList<>();
            }
            List<Long> systemIds = systemList.stream().map(DevopsSystem::getId).collect(Collectors.toList());
            subsystemGeneralQuery.setSystemIds(systemIds);
        }
        List<DevopsSubSystem> devopsSubSystems = subsystemRepository.listByParams(subsystemGeneralQuery);
        if (CollectionUtils.isEmpty(devopsSubSystems)) {
            return new ArrayList<>();
        }
        List<SubsystemDto> data = toSubsystemDto(devopsSubSystems);
        data = addSystem(data);
        addDirector(data);
        if (request.getWithVersion()) {
            addVersion(data);
        }
        if (request.getFilterByVersion()) {
            return data.stream().filter(d -> CollectionUtils.isNotEmpty(d.getVersions())).collect(Collectors.toList());
        }
        return data;
    }

    @Override
    public List<SubsystemDto> resourceListAll(SubsystemGeneralRequest request) {
        List<DevopsSubSystem> devopsSubSystems = null;
        if(!request.getResource()){
            devopsSubSystems = subsystemRepository.listAll();
        }else{
            List<ResourceInstance> resourceInfoDtoList = permissionRepository.resourceList(SystemConstance.AmpResourceTypeCode.SUBSYSTEM);
            List<Long> instanceIds = resourceInfoDtoList.stream().map(instance -> instance.getResourceInstanceId()).collect(Collectors.toList());
            devopsSubSystems = subsystemRepository.listByParams(instanceIds);
        }
        List<SubsystemDto> subsystemDtos = toSubsystemDto(devopsSubSystems);
        addSystem(subsystemDtos);
        if(request.getWithGitlab()){
            withGitlab(subsystemDtos);
        }
        if(request.getFilterGitlab()){
            subsystemDtos = subsystemDtos.stream().filter(d -> d.getGitlabId() != null).collect(Collectors.toList());
        }
        if(request.getWithVersion()){
            addVersion(subsystemDtos);
        }
        return subsystemDtos;
    }

    @Override
    public void checkPermission(Long subsystemId, @Nullable Long systemId) {
        if(systemId == null){
            DevopsSubSystem subsystem = subsystemRepository.getById(subsystemId);
            if(subsystem == null){
                throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
            }
            systemId = subsystem.getSystemId();
        }
        List<ResourceInstance> resourceInstances = permissionRepository.resourceList(SystemConstance.AmpResourceTypeCode.SYSTEM, systemId.toString(), SystemConstance.AmpResourceTypeCode.SUBSYSTEM);

        if(!permissionRepository.checkResource(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subsystemId, SystemConstance.AmpResourceTypeCode.SYSTEM, systemId)){
            throw new SystemException(ExceptionCode.PERMISSION_DENIED, "无应用权限");
        }
    }

    @Override
    public SubsystemConfigDTO getConfig(Long subsystemId) {
        SubSystemConfig subsystemConfig = subSystemConfigRepository.getByParams(subsystemId);
        return subSystemMapstruct.toSubsystemConfigDTO(subsystemConfig);
    }

    @Override
    public SubsystemConfigDTO saveConfig(SubsystemConfigSaveRequest request) {
        SubSystemConfig subsystemConfig = subSystemConfigRepository.getByParams(request.getSubsystemId());
        User currentUser = iamRepository.getCurrentUser();
        if(subsystemConfig == null){
            subsystemConfig = new SubSystemConfig();
            subsystemConfig.setCreateBy(currentUser.getId());
            subsystemConfig.setCreateTime(LocalDateTime.now());
        }
        subsystemConfig.setUpdateBy(currentUser.getId());
        subsystemConfig.setUpdateTime(LocalDateTime.now());
        subsystemConfig.setSubsystemId(request.getSubsystemId());
        subsystemConfig.setClearTemporaryBranchFlag(request.getClearTemporaryBranchFlag());
        subsystemConfig.setClearTemporaryBranchTime(request.getClearTemporaryBranchTime());
        subsystemConfig.setSubsystemId(request.getSubsystemId());
        subSystemConfigRepository.saveOrUpdate(subsystemConfig);
        return subSystemMapstruct.toSubsystemConfigDTO(subsystemConfig);
    }

    private List<SubsystemDto> addVersion(List<SubsystemDto> data) {
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        List<Long> ids = data.stream().map(sub -> sub.getId()).collect(Collectors.toList());
        List<MajorVersion> versionManagements = majorVersionRepository.listByParams(null, ids);
        Map<Long, List<MajorVersion>> versionGroup = versionManagements.stream().collect(Collectors.groupingBy(MajorVersion::getSubSystemId));
        for (SubsystemDto datum : data) {
            List<MajorVersion> list = versionGroup.get(datum.getId());
            if (CollectionUtils.isNotEmpty(list)) {
                List<MajorVersionDto> collect = list.stream().map(v -> versionMapstruct.toMajorVersionDto(v)).collect(Collectors.toList());
                datum.setVersions(collect);
            }
        }
        return data;
    }

    private List<SubsystemDto> addSystem(List<SubsystemDto> data) {
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        List<Long> sysIds = data.stream().map(sub -> sub.getSystemId()).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        Map<Long, DevopsSystem> systemMap = devopsSystemRepository.listByParams(sysIds).stream().collect(Collectors.toMap(DevopsSystem::getId, s -> s));
        data = data.stream().filter(sub -> systemMap.containsKey(sub.getSystemId())).collect(Collectors.toList());
        for (SubsystemDto datum : data) {
            DevopsSystem devopsSystem = systemMap.get(datum.getSystemId());
            if (devopsSystem != null) {
                datum.setSystemName(devopsSystem.getSubFullNameCn());
            }
        }
        return data;
    }

    private List<SubsystemDto> addDirector(List<SubsystemDto> data) {
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        List<Long> directorIds = data.stream().filter(sub -> sub.getTechDirectorId() != null).map(sub -> sub.getTechDirectorId()).collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(directorIds);
        for (SubsystemDto datum : data) {
            User user = userMap.get(datum.getTechDirectorId());
            if (user != null) {
                datum.setTechDirectorName(user.getName());
            }
        }
        return data;
    }

    private List<SubsystemDto> toSubsystemDto(List<DevopsSubSystem> data) {
        return data.stream().map(sub -> subSystemMapstruct.toSubsystemDto(sub)).collect(Collectors.toList());
    }

    private void updateAdminRole(DevopsSubSystem old, DevopsSubSystem devopsSubSystem) {
        if (devopsSubSystem.getTechDirectorId() == null) {
            return;
        }
        List<RoleInfoDto> roleInfoDto = permissionRepository.roleList(SystemConstance.AmpResourceTypeCode.SUBSYSTEM);
        for (RoleInfoDto infoDto : roleInfoDto) {
            if (SystemConstance.SubSystemRoleCode.SUB_ADMIN.equals(infoDto.getCode())) {
                SaveMemberDto memberDto = new SaveMemberDto();
                memberDto.setUserId(devopsSubSystem.getTechDirectorId());
                memberDto.setRoleId(infoDto.getId());
                memberDto.setInstanceId(devopsSubSystem.getId());
                subSystemMemberService.saveMember(memberDto);
            }
        }
    }

    private void addAdminRole(DevopsSubSystem subsystem, SubsystemCreateRequest request, User currentUser) {
        RoleInfoDto roleInfoDto = subSystemMemberService.subSystemAdminRoles();
        CreateMemberVO createMemberVO = new CreateMemberVO();
        List<Long> userIds = new ArrayList<>();
        userIds.add(currentUser.getId());
        if (subsystem.getTechDirectorId() != null) {
            if (!subsystem.getTechDirectorId().equals(currentUser.getId())) {
                userIds.add(subsystem.getTechDirectorId());
            }
        }
        createMemberVO.setRoleId(roleInfoDto.getId());
        createMemberVO.setInstanceId(subsystem.getId());
        createMemberVO.setUserIds(userIds);
        createMemberVO.setCheckCodeRepo(false);
        subSystemMemberService.createMember(createMemberVO, true);

    }

    @Transactional(rollbackFor = Exception.class)
    public DevopsSubSystem saveLocal(SubsystemCreateRequest request, User currentUser) {
        LocalDateTime now = LocalDateTime.now();
        DevopsSubSystem data = subSystemMapstruct.toDevopsSubSystem(request);
        data.setDelFlag(SystemConstance.NOT_DELETE);
        data.setCreateBy(currentUser.getId());
        data.setCreateTime(now);
        data.setUpdateBy(currentUser.getId());
        data.setUpdateTime(now);
        subsystemRepository.save(data);
        return data;
    }


    public void checkCreateRequestParam(SubsystemCreateRequest context) {
        checkParam(context.getFullNameCn(), context.getSubCode(), context.getSubDescCn());
        Long systemId = context.getSystemId();
        Integer gitlabId = context.getGitlabId();
        String subCode = context.getSubCode();
        if (gitlabId != null && gitlabId > 0) {
            // 关联代码仓库校验
            ArrayList<String> gitlabIds = Lists.newArrayList(gitlabId.toString());
            List<SubSystemComponent> gitRepo = subsystemComponentRepository.listByParam(SystemConstance.ComponentType.GITLAB, gitlabIds);
            if (CollectionUtils.isNotEmpty(gitRepo)) {
                throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "该代码仓库已被使用，请重新选择其他代码仓库");
            }
        } else if (gitlabId == null) {
            // 稍后关联
        } else if (gitlabId == 0) {
            // 新建代码仓库校验
            Integer codeGroupId = systemComponentService.getCodeGroupId(systemId);
            GitProjectDto gitProjectDtos = codeProjectRepository.getProject(codeGroupId, subCode);
            if (gitProjectDtos != null) {
                throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "代码仓库英文名已被使用，请更改应用编码后重试");
            }
        }
        List<DevopsSubSystem> devopsSubSystems = subsystemRepository.listByParams(context.getSystemId() , context.getFullNameCn());
        if (CollectionUtils.isNotEmpty(devopsSubSystems)) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "应用名称已存在");
        }
        devopsSubSystems = subsystemRepository.listByCode(systemId, context.getSubCode());
        if (CollectionUtils.isNotEmpty(devopsSubSystems)) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "应用code已存在");
        }
    }

    private void checkParam(String fullNameCn, String subCode, String desc) {

        if (!Pattern.matches(subsystemNameRegex, fullNameCn)) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "应用名称只能由中英文、数字、字母开头，由1-100位中英文、数字、字母、中划线和下划线组成");
        }
        if (StringUtils.isNotBlank(subCode) && !Pattern.matches("^(?![-_])(?!.*?[-_]$)(?!.*\\.(git|atom)$)[a-zA-Z0-9_-]{1,100}$", subCode)) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "应用编码格式错误：只能由英文、数字、字母开头,1-100位数字、字母、下划线和中横线组成");
        }
        if (StringUtils.isNotEmpty(desc) && desc.length() > 255) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "应用简介不能超过255个字符");
        }
    }

    private SubSystemInfoDto info(DevopsSubSystem subSystem) {
        DevopsSystem system = devopsSystemRepository.getById(subSystem.getSystemId());
        SubSystemInfoDto dto = new SubSystemInfoDto();
        dto.setSubSystemId(subSystem.getId());
        dto.setSubSystemCode(subSystem.getSubCode());
        dto.setSubSystemFullNameCn(subSystem.getFullNameCn());
        dto.setSystemId(system.getId());
        dto.setSystemCode(system.getSysCode());
        dto.setSystemNameCn(system.getSubFullNameCn());
        dto.setSystemFullNameCn(system.getSubFullNameCn());
        // 应用工程从应用变量里获取
        //dto.setOutCode(subSystem.getOutCode());
        return dto;
    }


}




