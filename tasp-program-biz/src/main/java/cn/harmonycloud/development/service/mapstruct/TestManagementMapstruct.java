package cn.harmonycloud.development.service.mapstruct;


import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.pojo.dto.project.IssuesWithProjectDto;
import cn.harmonycloud.development.pojo.dto.system.EnvTestListDTO;
import cn.harmonycloud.development.pojo.dto.test.TestManagementDetails;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.pojo.vo.test.TestManagementPageQuery;
import cn.harmonycloud.pojo.TestManageDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TestManagementMapstruct {

    @Mapping(target = "sqlUpdateFlag", ignore = true)
    TestManagementDetails toDetails(TestManagement tm);

    IssuesWithProjectDto toIssuesProject(IssuesDto is);

    TestManagementPageQuery toTestManagementPageQuery(EnvTestListDTO envTestListDTO);

    TestManageDto toTestManageDto(TestManagement tm);
}
