package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.pojo.vo.system.SubSystemQuery;
import cn.harmonycloud.development.outbound.api.dto.promotion.ProductMetadataDto;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.TestStatusEnum;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.db.mapper.TestManagementMapper;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.dto.project.IssuesWithProjectDto;
import cn.harmonycloud.development.pojo.dto.system.EnvTestListDTO;
import cn.harmonycloud.development.outbound.db.mapper.VersionInstanceMapper;
import cn.harmonycloud.development.pojo.dto.test.*;
import cn.harmonycloud.development.pojo.dto.system.EnvTestDTO;
import cn.harmonycloud.development.pojo.dto.web.WebFile;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.development.pojo.vo.autotest.ResultItemVO;
import cn.harmonycloud.development.pojo.vo.test.*;
import cn.harmonycloud.development.pojo.vo.test.EnvTestVO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.BuildDetailDto;
import cn.harmonycloud.development.outbound.api.dto.promotion.DetailDTO;
import cn.harmonycloud.development.outbound.api.dto.promotion.PromotionDetailRequest;
import cn.harmonycloud.development.service.TestManagementService;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.development.service.mapstruct.TestManagementMapstruct;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import cn.harmonycloud.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TestManagementServiceImpl extends ServiceImpl<TestManagementMapper, TestManagement> implements TestManagementService {

    @Autowired
    private IamRepository iamRepository;

    @Autowired
    private VersionInstanceMapper versionInstanceMapper;

    @Autowired
    private VersionRepository versionRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ProjectManagementService projectManagementService;

    @Autowired
    private BuildInstanceRepository buildInstanceRepository;

    @Autowired
    private TestManagementRepository testManagementRepository;
    @Autowired
    private DevopsStageService devopsStageService;
    @Autowired
    private TestManagementMapstruct testManagementMapstruct;
    @Autowired
    private SystemFileService systemFileService;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private DevopsSystemRepository devopsSystemRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;

    @Override
    public void create(CreateTestRequest createTestRequest) {
        DevopsSubSystem subSystem = subsystemRepository.getById(createTestRequest.getSubSystemId());
        String testCode = this.getTestCoupon(createTestRequest);
        List<TestManagement> list = new ArrayList<>();
        for (TestComponentRequest request : createTestRequest.getTestComponentRequestList()) {
            String patchNumber = request.getPatchNumber();
            List<String> ids = null;
            if (createTestRequest.getTestEnv().equals("main")) {
                LambdaQueryWrapper<VersionInstance> query = new LambdaQueryWrapper<VersionInstance>()
                        .eq(VersionInstance::getPipelineBuildId, request.getComponentIds().get(0));
                ids = Arrays.asList(versionInstanceMapper.selectOne(query).getId().toString());
            } else {
                ids = request.getComponentIds().stream().map(Object::toString).collect(Collectors.toList());
            }
            String componentIds = String.join(",", ids);
            TestManagement testManagement = new TestManagement();
            testManagement.setTestCode(testCode);
            testManagement.setPatchNumber(patchNumber);
            testManagement.setTestEnv(createTestRequest.getTestEnv());
            testManagement.setComponentIds(componentIds);
            testManagement.setSubSystemId(subSystem.getId());
            testManagement.setSystemId(subSystem.getSystemId());
            testManagement.setDirectorId(request.getDirectorId());
            testManagement.setCreateBy(iamRepository.getCurrentUser().getId());
            testManagement.setTestStatus(TestStatusEnum.TEST_WAIT.getCode());
            testManagement.setTestEnvVm(createTestRequest.getTestEnvVm());
            testManagement.setTestEnvContainer(createTestRequest.getTestEnvContainer());
            testManagement.setDescription(createTestRequest.getDescription());
            testManagement.setSqlUpdateFlag(createTestRequest.getSqlUpdateFlag() ? 1 : 0);
            testManagement.setCreateTime(LocalDateTime.now());
            list.add(testManagement);
        }
        this.saveBatch(list);
//        TestManagementEnvStatus testManagementEnvStatus = TestManagementEnvStatus.builder()
//                .testCode(testCode)
//                .testStatus(TestStatusEnum.TEST_WAIT.getCode())
//                .build();
//        testManagementEnvStatusMapper.insert(testManagementEnvStatus);
    }

    @Deprecated
    private String getTestCoupon(CreateTestRequest createTestRequest) {
        String versionFormat = "yyyyMMdd";
        String time = DateUtils.getNow(versionFormat);
        DevopsSubSystem subSystem = subsystemRepository.getById(createTestRequest.getSubSystemId());
        LambdaQueryWrapper<TestManagement> queryWrapper = new LambdaQueryWrapper<TestManagement>()
                .eq(TestManagement::getSubSystemId, createTestRequest.getSubSystemId())
                .eq(TestManagement::getTestEnv, createTestRequest.getTestEnv())
                .orderByDesc(TestManagement::getTestCode);
        List<TestManagement> lists = this.list(queryWrapper);
        String count;
        if (lists == null || lists.isEmpty()) {
            count = "01";
        } else {
            String testCode = lists.get(0).getTestCode();
            // 测试环境字符长度 + "-"字符长度 + 子系统outcode字符长度 + "-"字符长度 + 时间格式长度
            int start = createTestRequest.getTestEnv().length() + 1 + subSystem.getSubCode().length() + 1 + versionFormat.length();
            String substring = testCode.substring(start, start + 2);
            count = String.format("%02d", Integer.parseInt(substring) + 1);
        }
        return createTestRequest.getTestEnv() + "-" + subSystem.getSubCode() + "-" + time + count;
    }

    @Override
    public Page<EnvTestVersionVO> getVersionTestRecords(EnvTestDTO dto) {
        Page<TestManagement> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        LambdaQueryWrapper<TestManagement> queryWrapper = new LambdaQueryWrapper<TestManagement>()
                .eq(TestManagement::getSubSystemId, dto.getSubSystemId())
                .orderByDesc(TestManagement::getCreateTime)
                .eq(TestManagement::getTestEnv, "main");
        if (dto.getTestCode() != null) {
            queryWrapper.like(TestManagement::getTestCode, dto.getTestCode());
        }
        if (dto.getTestStatus() != null) {
            queryWrapper.eq(TestManagement::getTestStatus, dto.getTestStatus());
        }
        if (dto.getStartTime() != null) {
            queryWrapper.ge(TestManagement::getCreateTime, dto.getStartTime());
        }
        if (dto.getEndTime() != null) {
            queryWrapper.le(TestManagement::getCreateTime, dto.getEndTime());
        }
        Page<TestManagement> resPage = this.page(page, queryWrapper);
        List<TestManagement> list = resPage.getRecords();
        List<EnvTestVersionVO> resList = new ArrayList<>();
        for (TestManagement item : list) {
            EnvTestVersionVO vo = new EnvTestVersionVO();
            VersionInstance versionInstance = versionInstanceMapper.selectById(item.getComponentIds());
            VersionManagement version = versionRepository.getById(versionInstance.getVersionId());
            vo.setId(item.getId());
            vo.setTestCode(item.getTestCode());
            vo.setVersionName(version.getVersionNumber() + version.getPatchNumber());
            vo.setDirectorName(iamRepository.getUserById(item.getCreateBy()).getName());
            vo.setTestTime(item.getCreateTime());
            vo.setTestDirectorName(iamRepository.getUserById(item.getDirectorId()).getName());
            vo.setTestStatus(item.getTestStatus().toString());
            resList.add(vo);
        }
        Page<EnvTestVersionVO> voPage = new Page<>();
        voPage.setRecords(resList);
        voPage.setTotal(resPage.getTotal());
        voPage.setCurrent(resPage.getCurrent());
        voPage.setSize(resPage.getSize());
        return voPage;
    }

    @Override
    public ResultItemVO resultList(Long id) {
        TestManagement testManagement = this.getById(id);
        DevopsSubSystem devopsSubSystem = subsystemRepository.getById(testManagement.getSubSystemId());
        VersionInstance versionInstance = versionInstanceMapper.selectById(testManagement.getComponentIds());
        VersionManagement version = versionRepository.getById(versionInstance.getVersionId());
        List<String> tarList = new ArrayList<>();
        //获取buildId,remote查询详细信息
        Long buildId = versionInstance.getPipelineBuildId();
        PromotionDetailRequest promotionDetailRequest = new PromotionDetailRequest();
        promotionDetailRequest.setBuildId(buildId.toString());
        promotionDetailRequest.setEnvId(3L);
        List<DetailDTO> detailDTOS = productRepository.getProductPromotionDetail(promotionDetailRequest);
        for (DetailDTO dto : detailDTOS) {
            tarList.add(dto.getProductName());
        }
        ResultItemVO vo = new ResultItemVO();
        vo.setId(id);
        vo.setSubSystemName(devopsSubSystem.getFullNameCn());
        vo.setTester(iamRepository.getUserById(testManagement.getDirectorId()).getName());
        vo.setDownloadUrl(testManagement.getTestReportUrls());
        vo.setTestResult(testManagement.getTestStatus() < 3 ? null : testManagement.getTestStatus().toString());
        vo.setTestResultDescription(testManagement.getTestResultDescription());
        vo.setPromotionStatus(testManagement.getPromotionStatus() != 0);
        vo.setTarList(tarList.stream().distinct().collect(Collectors.toList()));
        return vo;
    }

    @Override
    public List<MyTestVO> myTest(Long subsystemId, Long stageEnvId) {
        List<TestManagement> list = testManagementRepository.listByParams(subsystemId, stageEnvId);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> userIds = list.stream().map(tm -> tm.getDirectorId()).collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);
        return list.stream().map(tm -> {
            MyTestVO vo = new MyTestVO();
            vo.setId(tm.getId());
            vo.setTestCode(tm.getTestCode());
            vo.setTestStatus(tm.getTestStatus());
            vo.setTestTime(tm.getCreateTime());
            vo.setTestDirectorName(userMap.getOrDefault(tm.getDirectorId(), new User()).getName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<EnvTestVO> getEnvTestRecords(EnvTestDTO envTestDTO) {
        //参数校验
        Page<TestManagement> page = testManagementRepository.queryPage(envTestDTO);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageUtils.exchangeRecordData(page, new ArrayList<>());
        }
        List<Long> userIds = page.getRecords().stream().map(tm -> tm.getCreateBy()).collect(Collectors.toList());
        List<Long> directorIds = page.getRecords().stream().map(tm -> tm.getDirectorId()).collect(Collectors.toList());
        List<Long> stageEnvIds = page.getRecords().stream().filter(tm -> tm.getStageEnvId() != null)
                .map(tm -> tm.getStageEnvId()).collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);
        Map<Long, User> director = iamRepository.listUserByIdsForMap(directorIds);
        List<DevopsStageEnvDto> devopsStageEnv = devopsStageService.envList(stageEnvIds);
        Map<Long, DevopsStageEnvDto> mapStage = devopsStageEnv.stream().collect(Collectors.toMap(DevopsStageEnvDto::getId, s -> s));
        return PageUtils.exchangeRecord(page, testManagement -> {
            EnvTestVO envTestVO = new EnvTestVO();
            envTestVO.setId(testManagement.getId());
            if (testManagement.getStageEnvId() == null) {
                // 1.8之前版本
                envTestVO.setTestEnv(testManagement.getTestEnv());
            } else {
                // 2.0版本
                DevopsStageEnvDto orDefault = mapStage.getOrDefault(testManagement.getStageEnvId(), new DevopsStageEnvDto());
                envTestVO.setTestEnv(orDefault.getStageEnvName());
            }
            envTestVO.setTestStatus(testManagement.getTestStatus());
            envTestVO.setTestCode(testManagement.getTestCode());
            envTestVO.setCreateBy(testManagement.getCreateBy());
            envTestVO.setCreateTime(testManagement.getCreateTime());
            envTestVO.setCreateByName(userMap.getOrDefault(testManagement.getCreateBy(), new User()).getName());
            envTestVO.setDirectorId(testManagement.getDirectorId());
            envTestVO.setDirectorName(director.getOrDefault(testManagement.getDirectorId(), new User()).getName());
            return envTestVO;
        });
    }

    @Override
    public Page<EnvTestListVO> envTestList(EnvTestListDTO envTestListDTO) {

        Page<TestManagement> testManagementPage = this.envTestPage(envTestListDTO);
        if (CollectionUtils.isEmpty(testManagementPage.getRecords())) {
            return PageUtils.exchangeRecordData(testManagementPage, new ArrayList<>());
        }
        List<Long> directorIds = testManagementPage.getRecords().stream().map(tm -> tm.getDirectorId()).collect(Collectors.toList());
        List<Long> createIds = testManagementPage.getRecords().stream().map(tm -> tm.getCreateBy()).collect(Collectors.toList());
        directorIds.addAll(createIds);
        List<Long> userIds = directorIds.stream().distinct().collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);

        List<Long> systemIds = testManagementPage.getRecords().stream().map(tm -> tm.getSystemId()).collect(Collectors.toList());
        Map<Long, DevopsSystem> systemMap = devopsSystemRepository.mapByIds(systemIds);

        List<Long> subSystemIds = testManagementPage.getRecords().stream().map(tm -> tm.getSubSystemId()).collect(Collectors.toList());
        Map<Long, DevopsSubSystem> subsystemMap = subsystemRepository.listByIds(subSystemIds).stream().collect(Collectors.toMap(DevopsSubSystem::getId, s -> s));

        return PageUtils.exchangeRecord(testManagementPage, tm -> {
            EnvTestListVO envTestListVO = new EnvTestListVO();
            BeanUtils.copyProperties(tm, envTestListVO);
            if (tm.getStageEnvId() != null) {
                DevopsStageEnvDto devopsStageEnvDto = devopsStageService.envInfo(tm.getStageEnvId());
                envTestListVO.setTestEnv(devopsStageEnvDto.getStageName() + "/" + devopsStageEnvDto.getEnvName());
            }
            envTestListVO.setStatus(tm.getTestStatus().toString());
            envTestListVO.setDirectorName(userMap.getOrDefault(tm.getDirectorId(), new User()).getName());
            envTestListVO.setCreateName(userMap.getOrDefault(tm.getCreateBy(), new User()).getName());
            envTestListVO.setSystemName(systemMap.getOrDefault(tm.getSystemId(), new DevopsSystem()).getSubFullNameCn());
            envTestListVO.setSubSystemName(subsystemMap.getOrDefault(tm.getSubSystemId(), new DevopsSubSystem()).getFullNameCn());
            return envTestListVO;
        });

    }

    public Page<TestManagement> envTestPage(EnvTestListDTO envTestListDTO) {
        TestManagementPageQuery query = testManagementMapstruct.toTestManagementPageQuery(envTestListDTO);
        if (StringUtils.isNotBlank(envTestListDTO.getSystemName())) {
            List<DevopsSystem> systemDataVOS = devopsSystemRepository.listByParams(envTestListDTO.getSystemName(), null);
            List<Long> systemIds = systemDataVOS.stream().map(sys -> sys.getId()).collect(Collectors.toList());
            query.setSystemIds(systemIds);
        }
        if (StringUtils.isNotBlank(envTestListDTO.getSubSystemName())) {
            SubSystemQuery systemQuery = new SubSystemQuery();
            systemQuery.setSubSystemName(envTestListDTO.getSubSystemName());
            List<DevopsSubSystem> subSystemDataVOS = subsystemRepository.listByParams(envTestListDTO.getSubSystemName(), true);
            List<Long> subsystemIds = subSystemDataVOS.stream().map(sys -> sys.getId()).collect(Collectors.toList());
            query.setSubSystemIds(subsystemIds);
        }
        if (StringUtils.isNotBlank(envTestListDTO.getStartTime())) {
            query.setStartTime(envTestListDTO.getStartTime() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(envTestListDTO.getEndTime())) {
            query.setEndTime(envTestListDTO.getEndTime() + " 23:59:59");
        }
        return testManagementRepository.pageQuery(query);
    }

    /**
     * 版本测试-状态改变
     *
     * @param id
     * @param status
     */
    @Override
    public Boolean updateStatus(Long id, Integer status) {
        TestManagement testManagement = this.getById(id);
        testManagement.setTestStatus(status);
        return this.updateById(testManagement);
    }

    /**
     * 转派测试人员
     *
     * @param id
     * @param directorId
     * @return
     */
    @Override
    public Boolean updateDirector(Long id, Long directorId) {
        TestManagement testManagement = this.getById(id);
        testManagement.setDirectorId(directorId);
        return this.updateById(testManagement);
    }

    /**
     * 保存测试报告
     *
     * @param testReportUrls
     * @return
     */
    @Override
    public Boolean saveTestReport(String ids, Integer testResult, String testReportUrls, String testResultDescription) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<TestManagement> managements = new ArrayList<>();
        for (Long id : idList) {
            TestManagement testManagement = this.getById(id);
            testManagement.setTestStatus(testResult);
            testManagement.setTestReportUrls(testReportUrls);
            testManagement.setTestResultDescription(testResultDescription);
            managements.add(testManagement);
        }
        return this.updateBatchById(managements);
    }

    @Override
    public List<TestManagement> list(TestManagementQuery query) {
        LambdaQueryWrapper<TestManagement> dbQuery = new LambdaQueryWrapper<>();
        dbQuery.eq(TestManagement::getSubSystemId, query.getSubSystemId());
        dbQuery.eq(query.getUserId() != null, TestManagement::getCreateBy, query.getUserId());
        dbQuery.gt(TestManagement::getCreateTime, query.getStartTime());
        dbQuery.lt(TestManagement::getCreateTime, query.getEndTime());
        return Optional.ofNullable(list(dbQuery)).orElse(new ArrayList<>());
    }

    @Override
    public List<IssuesDTO> issues(Long stageEnvId, Long jobId) {
        return null;
    }

    @Override
    public void save(TestCreateRequest createTestRequest) {

        DevopsSubSystem subSystem = subsystemRepository.getById(createTestRequest.getSubsystemId());
        TestManagement testManagement = new TestManagement();
        testManagement.setStageEnvId(createTestRequest.getStageEnvId());
        testManagement.setBuildInstanceId(createTestRequest.getBuildInstanceId());
        testManagement.setSubSystemId(subSystem.getId());
        testManagement.setSystemId(subSystem.getSystemId());
        testManagement.setDirectorId(createTestRequest.getDirector());
        testManagement.setCreateBy(iamRepository.getCurrentUser().getId());
        testManagement.setUpdateBy(iamRepository.getCurrentUser().getId());
        testManagement.setTestStatus(TestStatusEnum.TEST_WAIT.getCode());
        testManagement.setTestEnvVm(createTestRequest.getTestEnvVm());
        testManagement.setTestEnvContainer(createTestRequest.getTestEnvContainer());
        testManagement.setDescription(createTestRequest.getDescription());
        testManagement.setSqlUpdateFlag(createTestRequest.getSqlUpdateFlag() ? 1 : 0);
        testManagement.setCreateTime(LocalDateTime.now());
        testManagement.setUpdateTime(LocalDateTime.now());
        this.save(testManagement);
        String versionFormat = "yyyyMMdd";
        String time = DateUtils.getNow(versionFormat);
        String testCode = subSystem.getSubCode() + "-" + time + testManagement.getId();
        testManagement.setTestCode(testCode);
        this.saveOrUpdate(testManagement);
        // 更新制品元数据提测单
        BuildInstance byId = buildInstanceRepository.getById(createTestRequest.getBuildInstanceId());
        if (byId == null) {
            return;
        }
        List<TestManagement> testManagements = testManagementRepository.listByParams(createTestRequest.getBuildInstanceId());
        List<String> collect = testManagements.stream().map(p -> p.getTestCode()).collect(Collectors.toList());
        ProductMetadataDto pm = new ProductMetadataDto();
        pm.setBuildId(byId.getBuildId());
        pm.setTestManagements(Joiner.on(",").join(collect));
        productRepository.updateMetadataByBuildId(pm);
//        TestManagementEnvStatus testManagementEnvStatus = TestManagementEnvStatus.builder()
//                .testCode(testCode)
//                .testStatus(TestStatusEnum.TEST_WAIT.getCode())
//                .build();
//        testManagementEnvStatusMapper.insert(testManagementEnvStatus);
    }

    @Override
    public TestPreCreateResponse preCreate(TestPreCreateRequest createTestRequest) {
        BuildInstance buildInstance = buildInstanceRepository.getLastOneByParam(createTestRequest.getStageEnvId(), createTestRequest.getJobId());
        if (buildInstance == null) {
            throw new SystemException(ExceptionCode.JOB_EXECUTE_FAIL, "请先运行流水线");
        }
        Long buildId = buildInstance.getBuildId();
        BuildDetailDto recentBuildByBuildId = jobRepository.getRecentBuildByBuildId(buildInstance.getJobId(), buildId);
        if (!"SUCCESS".equals(recentBuildByBuildId.getStatus())) {
            throw new SystemException(ExceptionCode.JOB_EXECUTE_FAIL, "请在流水线运行成功后再发起提测");
        }
        List<IssuesWithProjectDto> project = projectManagementService.listIssuesWithProjectName(buildInstance.getFeatureId());
        TestPreCreateResponse response = new TestPreCreateResponse();
        response.setIssues(project);
        response.setBuildInstanceId(buildInstance.getId());
        return response;
    }

    @Override
    public TestManagementDetails detail(Long id) {
        TestManagement tm = testManagementRepository.getById(id);
        if (tm == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        DevopsSubSystem subsystem = subsystemRepository.getById(tm.getSubSystemId());
        DevopsSystem system = devopsSystemRepository.getById(tm.getSystemId());
        User user = iamRepository.getUserById(tm.getDirectorId());
        User byUserId = iamRepository.getUserById(tm.getCreateBy());
        TestManagementDetails details = testManagementMapstruct.toDetails(tm);
        details.setSubsystemId(tm.getSubSystemId());
        details.setSubsystemName(subsystem.getFullNameCn());
        details.setSystemName(system.getSubFullNameCn());
        details.setDirectorName(user.getName());
        details.setCreateName(byUserId.getName());
        details.setSqlUpdateFlag(tm.getSqlUpdateFlag() == 1 ? true : false);
        if (tm.getStageEnvId() != null) {
            DevopsStageEnvDto devopsStageEnvDto = devopsStageService.envInfo(tm.getStageEnvId());
            details.setTestEnv(devopsStageEnvDto.getStageName() + "/" + devopsStageEnvDto.getEnvName());
        }
        if (StringUtils.isNotEmpty(tm.getManualReport())) {
            // 2.0版本数据
            details.setManualFile(systemFileService.getWebFileByIds(tm.getManualReportIds()));
        } else if (StringUtils.isNotEmpty(tm.getTestReportUrls())) {
            // 1.8版本数据兼容展示
            WebFile webFile = new WebFile();
            webFile.setUrl(tm.getTestReportUrls());
            webFile.setName(tm.getTestReportUrls().substring(tm.getTestReportUrls().lastIndexOf("/")));
            details.getManualFile().add(webFile);
        }
        if (StringUtils.isNotEmpty(tm.getAutotestReport())) {
            details.setAutotestFile(systemFileService.getWebFileByIds(tm.getAutotestReportIds()));
        }
        if (tm.getBuildInstanceId() != null) {
            // 2.0版本数据
            BuildInstance buildInstanceId = buildInstanceRepository.getById(tm.getBuildInstanceId());
            List<IssuesWithProjectDto> issuesDTOS = projectManagementService.listIssuesWithProjectName(buildInstanceId.getFeatureId());
            details.setIssues(issuesDTOS);
            if (buildInstanceId.getVersionId() != null) {
                // 版本提测
                VersionManagement byId = versionRepository.getById(buildInstanceId.getVersionId());
                details.setVersion(byId.getVersionString());
            }
        } else if (StringUtils.isNotEmpty(tm.getComponentIds())) {
            List<String> strings = Splitter.on(",").splitToList(tm.getComponentIds());
            List<Long> collect = strings.stream().map(fid -> Long.parseLong(fid)).collect(Collectors.toList());
            List<IssuesWithProjectDto> issuesDTOS = projectManagementService.listIssuesWithProjectName(collect);
            details.setIssues(issuesDTOS);
        }

        return details;
    }

    @Override
    public void modify(TestModifyRequest modifyRequest) {
        User currentUser = iamRepository.getCurrentUser();
        testManagementRepository.modify(modifyRequest, currentUser);


    }

    @Override
    public void batchModify(TestBatchModifyRequest modifyRequest) {
        User currentUser = iamRepository.getCurrentUser();
        testManagementRepository.modify(modifyRequest, currentUser);
    }


}
