package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.pojo.dto.product.ProductTreeRequestDto;
import cn.harmonycloud.development.pojo.vo.repository.ProductTreeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface DevopsProductMapstruct {

    ProductTreeRequestDto toProductTreeRequestDto(ProductTreeQuery request);
}
