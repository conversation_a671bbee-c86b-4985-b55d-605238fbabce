package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.pojo.dto.system.SystemCreateRequest;
import cn.harmonycloud.development.pojo.dto.system.SystemDictDto;
import cn.harmonycloud.development.pojo.dto.system.SystemUpdateRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.pojo.vo.system.SystemDataVO;
import cn.harmonycloud.development.pojo.vo.system.SystemDetails;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 */
@Mapper(componentModel = "spring")
public interface DevopsSystemMapstruct {
    DevopsSystemMapstruct MAPSTRUCT = Mappers.getMapper(DevopsSystemMapstruct.class);


    @Mappings({
            @Mapping(source = "subFullNameCn", target = "fullNameCn"),
            @Mapping(source = "sysCode", target = "code"),
            @Mapping(source = "projectDirectorId", target = "director"),
            @Mapping(source = "sysDescCn", target = "descCn")
    })
    SystemDataVO toSystemDataVO(DevopsSystem devopsSystem);

    SystemDictDto toSystemDictDto(SystemDict dict);

    DevopsSystemDto toDevopsSystemDto(DevopsSystem system);

    DevopsSystem toDevopsSystem(SystemCreateRequest request);

    SystemDetails toDevopsSystemDetails(DevopsSystem devopsSystem);

    DevopsSystem toDevopsSystem(SystemUpdateRequest request);
}
