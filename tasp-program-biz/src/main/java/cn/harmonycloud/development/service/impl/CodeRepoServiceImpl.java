package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchDto;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.development.outbound.api.dto.coderepo.TagDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.outbound.db.mapper.MergeResourceMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.MergeResource;
import cn.harmonycloud.development.pojo.entity.MergeTask;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import cn.harmonycloud.development.pojo.vo.testenv.SubmitMergeSourceBranchRequest;
import cn.harmonycloud.development.service.CodeRepoService;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.PostConstruct;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/22 5:38 下午
 **/
@Service
public class CodeRepoServiceImpl extends BaseRepositoryImpl<MergeResourceMapper, MergeResource> implements CodeRepoService {

    private final SubsystemComponentRepository subsystemComponentRepository;
    private final SystemDictRepository systemDictRepository;
    private final MergeResourceMapper mergeResourceMapper;
    private final CodeProjectRepository codeProjectRepository;
    private final CodeBranchRepository codeBranchRepository;
    private final CodeMergeRepository codeMergeRepository;
    private final MergeTaskRepository mergeTaskRepository;
    private final GitLabProjectService ********************;

    private String baseBranch;

    @Autowired
    public CodeRepoServiceImpl(
            SubsystemComponentRepository subsystemComponentRepository,
            SystemDictRepository systemDictRepository,
            MergeResourceMapper mergeResourceMapper,
            CodeProjectRepository codeProjectRepository,
            CodeBranchRepository codeBranchRepository,
            CodeMergeRepository codeMergeRepository,
            MergeTaskRepository mergeTaskRepository,
            @Lazy GitLabProjectService ********************) {
        this.subsystemComponentRepository = subsystemComponentRepository;
        this.systemDictRepository = systemDictRepository;
        this.mergeResourceMapper = mergeResourceMapper;
        this.codeProjectRepository = codeProjectRepository;
        this.codeBranchRepository = codeBranchRepository;
        this.codeMergeRepository = codeMergeRepository;
        this.mergeTaskRepository = mergeTaskRepository;
        this.******************** = ********************;
    }

    @PostConstruct
    private void init() {
        List<SystemDict> systemDict = systemDictRepository.getByParams(SystemConstance.SystemDictSubject.BASE_BRANCH);
        if (CollectionUtils.isEmpty(systemDict)) {
            throw new BeanInitializationException("初始化数据错误：错误的基础分支数据");
        }
        baseBranch = systemDict.get(0).getDictCode();
    }

    @Override
    public Integer getScmIdBySystemId(Long subSystemId) {
        return ********************.getScmIdBySystemId(subSystemId);
    }

    @Override
    public Integer getCodeRepoIdBySystemId(Long subSystemId) {
        return ********************.getCodeRepoIdBySystemId(subSystemId);
    }

    @Override
    public Map<Long, Integer> mapCodeRepoIdBySystemId(List<Long> subSystemIds) {
        return ********************.mapCodeRepoIdBySystemId(subSystemIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMergeTask(Long subSystemId, String envCode, Long mergeId, List<SubmitMergeSourceBranchRequest> branches) {
      //  SubSystemInfoDto info = ********************.getSubSystemInfo(subSystemId);
        SubSystemInfoDto info = new SubSystemInfoDto();
        Long mergeTaskId = mergeTaskRepository.getTaskIdByParams(subSystemId, envCode);
        if (mergeTaskId == null) {
            MergeTask mergeTask = new MergeTask();
            mergeTask.setSubSystemId(subSystemId);
            mergeTask.setEnvCode(envCode);
            mergeTask.setMergeTaskId(mergeId);
            mergeTask.setMergeType(SystemConstance.MergeType.MERGE_TASK);
            mergeTask.setCreateTime(LocalDateTime.now());
            mergeTaskRepository.save(mergeTask);
        } else {
            MergeTask mergeTask = new MergeTask();
            mergeTask.setCreateTime(LocalDateTime.now());
            mergeTask.setMergeTaskId(mergeId);
            LambdaUpdateWrapper<MergeTask> update = new LambdaUpdateWrapper<>();
            update.eq(MergeTask::getSubSystemId, subSystemId);
            update.eq(MergeTask::getEnvCode, envCode);
            mergeTaskRepository.update(mergeTask, update);
        }
        if (CollectionUtils.isNotEmpty(branches)) {
            LambdaUpdateWrapper<MergeResource> delete = new LambdaUpdateWrapper<>();
            delete.eq(MergeResource::getSubSystemId, subSystemId);
            delete.eq(MergeResource::getEnvCode, envCode);
            mergeResourceMapper.delete(delete);
            List<MergeResource> collect = branches.stream().map(branch -> {
                MergeResource mergeResource = new MergeResource();
                mergeResource.setFeatureId(branch.getFeatureId());
                mergeResource.setBranchName(branch.getBranchName());
                mergeResource.setSystemId(info.getSystemId());
                mergeResource.setEnvCode(envCode);
                mergeResource.setSubSystemId(subSystemId);
                mergeResource.setProjectId(branch.getProjectId());
                return mergeResource;
            }).collect(Collectors.toList());
            this.saveBatch(collect);
        }
    }

    @Override
    public String getBaseBranch() {
        return baseBranch;
    }

    @Override
    public GitProjectDto getProject(Long subSystemId, boolean throwException) {
        return ********************.getProject(subSystemId, throwException);
    }

    @Override
    public List<BranchDto> branches(Long subSystemId) {
        return ********************.branches(subSystemId);
    }

    @Override
    public Integer merCount(Long subsystemId, String startTime, String endTime) {
        return ********************.merCount(subsystemId, startTime, endTime);
    }

    @Override
    public BranchStageDto branchStage(Long subsystemId, String branch) {
        return ********************.branchStage(subsystemId, branch);
    }

    @Override
    public String baseBranch(Long subSystemId) {
        return this.baseBranch;
    }

    @Override
    public List<TagDto> tagList(Long subSystemId, String name) {
        return ********************.tagList(subSystemId, name);
    }

    @Override
    public void updateBySubsystem(DevopsSubSystem devopsSubSystem) {
        ********************.updateBySubsystem(devopsSubSystem);
    }

    @Override
    public List<String> listBranchAndTag(Long subSystemId) {
        return ********************.listBranchAndTag(subSystemId);
    }

    @Override
    public void clearMergeBranch(Integer codeRepoId, Integer clearTemporaryBranchTime) {
        ********************.clearMergeBranch(codeRepoId, clearTemporaryBranchTime);
    }

    @Override
    public void initCodeScan(Long subsystemId, String technology) {
        ********************.initCodeScan(subsystemId, technology);
    }
}
