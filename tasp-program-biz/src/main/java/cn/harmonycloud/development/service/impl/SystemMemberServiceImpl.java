package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleBindingDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.query.MemberQuery;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupMemberDto;
import cn.harmonycloud.development.service.SystemComponentService;
import cn.harmonycloud.pmp.model.dto.UserOrganizationDto;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;
import cn.harmonycloud.pmp.model.vo.UserVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.harmonycloud.development.service.SystemMemberService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【system_member(成员表)】的数据库操作Service实现
 * @createDate 2022-08-02 11:14:36
 */
@Service
public class SystemMemberServiceImpl implements SystemMemberService {

    @Autowired
    private SystemComponentService systemComponentService;
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    @Lazy
    private SubSystemService subSystemService;
    @Autowired
    private SubsystemPipelineRepository subsystemPipelineRepository;
    @Autowired
    private CodeGroupRepository codeGroupRepository;
    @Autowired
    private DevopsSystemRepository devopsSystemRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;

    private static String AMP_CODE = SystemConstance.AmpResourceTypeCode.SYSTEM;

    @Override
    public List<RoleInfoDto> systemRoles() {
        return permissionRepository.roleList(AMP_CODE);
    }

    @Override
    public RoleInfoDto systemAdminRoles() {
        List<RoleInfoDto> roleInfo = systemRoles();
        for (RoleInfoDto roleInfoDto : roleInfo) {
            if (SystemConstance.SystemRoleCode.SYS_ADMIN.equals(roleInfoDto.getCode())) {
                return roleInfoDto;
            }
        }
        return null;
    }

    @Override
    public void createMember(CreateMemberVO createMemberVO) {
        if (createMemberVO.isSyncCodeRepo()) {
            addGitlabMember(createMemberVO);
        }
        addAmpMember(createMemberVO);
        addPipelineMember(createMemberVO);
    }

    private void addPipelineMember(CreateMemberVO createMemberVO) {
        List<DevopsSubSystem> subSystemDataVOS = subsystemRepository.listBySystemId(createMemberVO.getInstanceId());
        if (CollectionUtils.isEmpty(subSystemDataVOS)) {
            return;
        }
        List<Long> subIds = subSystemDataVOS.stream().map(subSystemDataVO -> subSystemDataVO.getId()).collect(Collectors.toList());
        List<Long> jobIds = subsystemPipelineRepository.listJobIdByParam(subIds);
        if(CollectionUtils.isEmpty(jobIds)){
            return;
        }
        RoleBindingDto pipelineRoleBindingInfo = permissionRepository.getPipelineRoleBindingInfo(AMP_CODE, createMemberVO.getRoleId().toString());
        if(pipelineRoleBindingInfo != null){
            subsystemPipelineRepository.savePermission(jobIds, createMemberVO.getUserIds(), pipelineRoleBindingInfo.getTargetRoleId());
        }
    }

    @Override
    public void saveMember(SaveMemberDto member) {
        List<UserRoleVo> userRoles = permissionRepository.getRolesByInstance(AMP_CODE, member.getInstanceId(), Lists.newArrayList(member.getUserId()));
        if (CollectionUtils.isNotEmpty(userRoles)) {
            for (UserRoleVo userRole : userRoles) {
                if (userRole.getRoleId().equals(member.getRoleId())) {
                    return;
                }
            }
            DeleteMemberVO deleteMemberVO = new DeleteMemberVO();
            deleteMemberVO.setUserId(member.getUserId());
            deleteMemberVO.setInstanceId(member.getInstanceId());
            deleteMemberVO.setCheckDirector(false);
            deleteMember(deleteMemberVO);
        }
        CreateMemberVO createMember = new CreateMemberVO();
        createMember.setUserIds(Arrays.asList(member.getUserId()));
        createMember.setRoleId(member.getRoleId());
        createMember.setInstanceId(member.getInstanceId());
        createMember(createMember);
    }

    public void addGitlabMember(CreateMemberVO createMemberVO) {
        List<Long> collect = createMemberVO.getUserIds().stream().filter(userId -> !userId.equals(1L)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return;
        }
        RoleBindingDto gitlabGroupRoleBindingInfo = permissionRepository.getGitlabGroupRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SYSTEM, createMemberVO.getRoleId().toString());
        if (gitlabGroupRoleBindingInfo == null) {
            return;
        }
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(collect);
        String groupIdStr = systemComponentService.getComponentKeyBySystemId(createMemberVO.getInstanceId());
        Integer groupId = Integer.parseInt(groupIdStr);
        List<GroupMemberDto> members = collect.stream().map(userId -> {
            GroupMemberDto member = new GroupMemberDto();
            member.setUserId(userId);
            member.setUsername(userMap.get(userId).getUsername());
            member.setRoleId(gitlabGroupRoleBindingInfo.getTargetRoleId());
            return member;
        }).collect(Collectors.toList());
        codeGroupRepository.addGroupMember(groupId, members);
    }

    private void addAmpMember(CreateMemberVO createMemberVO) {
        UserOrganizationDto resourceUser = new UserOrganizationDto();
        resourceUser.setResourceInstanceId(createMemberVO.getInstanceId());
        resourceUser.setResourceTypeCode(AMP_CODE);
        resourceUser.setRoleIds(Lists.newArrayList(createMemberVO.getRoleId()));
        resourceUser.setUserIds(createMemberVO.getUserIds());
        permissionRepository.resourceInstancesUser(resourceUser);
    }

    @Override
    public void deleteMember(DeleteMemberVO deleteMemberVO) {
        DevopsSystem devopsSystem = devopsSystemRepository.getById(deleteMemberVO.getInstanceId());
        if (deleteMemberVO.isCheckDirector() && deleteMemberVO.getUserId().equals(devopsSystem.getProjectDirectorId())) {
            throw new SystemException(ExceptionCode.SYSTEM_MEMBER_FILE, "无法移除系统负责人");
        }
        Long systemId = deleteMemberVO.getInstanceId();
        Long userId = deleteMemberVO.getUserId();
        List<UserRoleVo> roles = permissionRepository.getRolesByInstance(AMP_CODE, systemId, Lists.newArrayList(userId));
        iamRepository.deleteMember(AMP_CODE, systemId, userId);
        User userInfo = iamRepository.getUserById(userId);
        if (CollectionUtils.isNotEmpty(roles)) {
            RoleBindingDto gitlabGroupRoleBindingInfo = permissionRepository.getGitlabGroupRoleBindingInfo(AMP_CODE, roles.get(0).getRoleId().toString());
            if (gitlabGroupRoleBindingInfo != null) {
                // 系统管理员、同步删除gitlab角色
                String groupIdStr = systemComponentService.getComponentKeyBySystemId(systemId);
                int groupId = Integer.parseInt(groupIdStr);
                codeGroupRepository.deleteGroupMember(groupId, userInfo.getUsername());
            }
        }
        // 删除流水线权限
        ArrayList<Long> userIds = Lists.newArrayList(deleteMemberVO.getUserId());
        List<Long> subIds = subsystemRepository.listBySystemId(systemId).stream().map(subSystem -> subSystem.getId()).collect(Collectors.toList());
        List<Long> jobIds = subsystemPipelineRepository.listJobIdByParam(subIds);
        subsystemPipelineRepository.deletePermission(jobIds, userIds);
    }

    private Map<Long, List<Long>> getPipelineUser(Long systemId, List<Long> userIds) {
        List<DevopsSubSystem> subSystemDataVOS = subsystemRepository.listBySystemId(systemId);
        if (CollectionUtils.isEmpty(subSystemDataVOS)) {
            return null;
        }
        Map<Long, List<Long>> user = new HashMap<>();
        for (DevopsSubSystem subSystemDataVO : subSystemDataVOS) {
            user.put(subSystemDataVO.getId(), userIds);
        }
        return user;
    }


    @Override
    public Page<ListSystemMemberVO> querySystemMember(MemberQuery query) {
        int pageNo = query.getPageNo();
        int pageSize = query.getPageSize();
        Page<UserVo> userInfoList = iamRepository.pageMember(AMP_CODE, query.getInstanceId(), pageNo, pageSize, query.getQueryParam());
        if (CollectionUtils.isEmpty(userInfoList.getRecords())) {
            return PageUtils.exchangeRecordData(userInfoList, new ArrayList<>());
        }
        return PageUtils.exchangeRecord(userInfoList, user -> {
            ListSystemMemberVO vo = new ListSystemMemberVO();
            vo.setName(user.getName());
            vo.setUserId(user.getId());
            if (CollectionUtils.isNotEmpty(user.getRoles())) {
                RoleVo roleVo = user.getRoles().get(0);
                vo.setRoleName(roleVo.getName());
                vo.setRoleId(roleVo.getId());
            }

            return vo;
        });
    }

    @Override
    public List<UserVo> listOverUser(Long systemId, String queryParam) {
        return iamRepository.usersByCurrentOriginOver(AMP_CODE, systemId, queryParam);
    }

    @Override
    public List<ListSystemByCurrentVo> listByCurrent(ListSystemByCurrentRequest request) {
        List<ResourceInstance> resourceInfoDtoList = permissionRepository.resourceList(AMP_CODE);
        if (CollectionUtils.isEmpty(resourceInfoDtoList)) {
            return new ArrayList<>();
        }
        List<Long> systemIds = resourceInfoDtoList.stream().map(resource -> resource.getResourceInstanceId()).collect(Collectors.toList());
        List<DevopsSystem> systemList = devopsSystemRepository.listByParams(systemIds);
        return systemList.stream().map(system -> {
            ListSystemByCurrentVo systemByCurrentVo = new ListSystemByCurrentVo();
            systemByCurrentVo.setId(system.getId());
            systemByCurrentVo.setName(system.getSubFullNameCn());
            return systemByCurrentVo;
        }).collect(Collectors.toList());
    }

    @Override
    public void modifyMember(ModifyMemberVo req) {
        Long roleId = req.getRoleIds().get(0);
        RoleBindingDto oldRoleBinding = null;
        List<UserRoleVo> userRoles = permissionRepository.getRolesByInstance(AMP_CODE, req.getInstanceId(), Lists.newArrayList(req.getUserId()));
        if (CollectionUtils.isNotEmpty(userRoles)) {
            for (UserRoleVo userRole : userRoles) {
                if (userRole.getRoleId().equals(roleId)) {
                    // 角色不变，直接返回
                    return;
                }
            }
            oldRoleBinding = permissionRepository.getGitlabGroupRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SYSTEM, userRoles.get(0).getRoleId().toString());
        }
        RoleBindingDto newRoleBinding = permissionRepository.getGitlabGroupRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SYSTEM, roleId.toString());
        updateGitlabRole(req, oldRoleBinding, newRoleBinding);
        updateAmpRole(req);
    }

    private void updateAmpRole(ModifyMemberVo req) {
        permissionRepository.resourceUpdateRole(SystemConstance.AmpResourceTypeCode.SYSTEM, req.getInstanceId(), req.getUserId(), req.getRoleIds());
    }

    private void updateGitlabRole(ModifyMemberVo req, RoleBindingDto oldRoleBinding, RoleBindingDto newRoleBinding) {
        if (newRoleBinding == null && oldRoleBinding != null) {
            // 删除gitlab人员原有角色
            User user = iamRepository.getUserById(req.getUserId());
            String groupIdStr = systemComponentService.getComponentKeyBySystemId(req.getInstanceId());
            Integer groupId = Integer.parseInt(groupIdStr);
            codeGroupRepository.deleteGroupMember(groupId, user.getUsername());
        }
        if (newRoleBinding != null) {
            // 保存gitlab角色
            CreateMemberVO createMemberVO = new CreateMemberVO();
            createMemberVO.setUserIds(Lists.newArrayList(req.getUserId()));
            createMemberVO.setRoleId(req.getRoleIds().get(0));
            createMemberVO.setInstanceId(req.getInstanceId());
            addGitlabMember(createMemberVO);

        }
    }

}




