package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.pojo.dto.subsystem.*;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.SubSystemConfig;
import cn.harmonycloud.development.pojo.vo.system.SubSystemDataVO;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigDTO;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2022/7/28
 */
@Mapper(componentModel = "spring")
public interface DevopsSubSystemMapstruct {
    DevopsSubSystemMapstruct MAPSTRUCT = Mappers.getMapper(DevopsSubSystemMapstruct.class);


    /**
     * DO转systemDataVO
     * @param system
     * @return
     */
    @Mappings({
            @Mapping(source = "subCode", target = "code"),
            @Mapping(source = "subDescCn", target = "descCn")
    })
    SubSystemDataVO doToSystemData(DevopsSubSystem system);


    SubsystemDto toSubsystemDto(DevopsSubSystem devopsSubSystem);

    SubsystemDto toDevopsSubSystem(DevopsSubSystem sub);

    DevopsSubSystem toDevopsSubSystem(SubsystemCreateRequest request);

    DevopsSubSystem toDevopsSubSystem(SubsystemUpdateRequest request);

    SubsystemDetails toDevopsSubSystemDetails(DevopsSubSystem devopsSubSystem);

    SubsystemGeneralQuery toDevopsGeneral(SubsystemGeneralRequest request);

    SubsystemConfigDTO toSubsystemConfigDTO(SubSystemConfig subsystemConfig);
}
