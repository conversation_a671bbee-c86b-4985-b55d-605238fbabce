package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.SystemComponent;
import cn.harmonycloud.development.pojo.query.MemberQuery;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleBindingDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectMemberDto;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.development.service.mapstruct.DevopsSubSystemMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pmp.model.dto.UserOrganizationDto;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.RoleBase;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/20 3:55 下午
 **/
@Slf4j
@Service
public class SubSystemMemberServiceImpl implements SubSystemMemberService {

    @Autowired
    private IamRepository iamRepository;

    private CodeRepoService codeRepoService;
    @Autowired
    public void setCodeRepoService(@Lazy CodeRepoService codeRepoService) {
        this.codeRepoService = codeRepoService;
    }
    private SubSystemService subSystemService;
    @Autowired
    public void setSubSystemService(SubSystemService subSystemService) {
        this.subSystemService = subSystemService;
    }
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private SubsystemPipelineRepository subsystemPipelineRepository;
    @Autowired
    private CodeProjectRepository codeProjectRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private DevopsSubSystemMapstruct devopsSubSystemMapstruct;
    @Autowired
    private SubsystemComponentRepository subsystemComponentRepository;

    private static String AMP_CODE = SystemConstance.AmpResourceTypeCode.SUBSYSTEM;

    @Override
    public List<RoleInfoDto> roles() {
        return permissionRepository.roleList(AMP_CODE);
    }

    @Override
    public void createMember(CreateMemberVO req, Boolean syncGitlab) {
        // gitlab添加权限
        checkLevel(req.getRoleId(), req.getInstanceId(), req.getUserIds());
        if(syncGitlab){
            Integer gitlabId;
            if(req.isCheckCodeRepo()){
                gitlabId = codeRepoService.getScmIdBySystemId(req.getInstanceId());
            }else {
                gitlabId = codeRepoService.getCodeRepoIdBySystemId(req.getInstanceId());
            }
            if(gitlabId != null){
                addGitlabMember(req, gitlabId);
            }
        }
        // 调用amp接口添加成员,注册资源
        resourceInstanceUser(req);
        // 添加流水线权限
        pipelinePermission(req);
    }

    private void pipelinePermission(CreateMemberVO req) {
        List<Long> jobIds = subsystemPipelineRepository.getJobIds(req.getInstanceId());
        if(CollectionUtils.isEmpty(jobIds)){
            return ;
        }
        RoleBindingDto pipelineRoleBindingInfo = permissionRepository.getPipelineRoleBindingInfo(AMP_CODE, req.getRoleId().toString());
        if(pipelineRoleBindingInfo == null){
            subsystemPipelineRepository.deletePermission(jobIds, req.getUserIds());
        }else{
            subsystemPipelineRepository.savePermission(jobIds, req.getUserIds(), pipelineRoleBindingInfo.getTargetRoleId());
        }
    }

    private void resourceInstanceUser(CreateMemberVO req) {
        UserOrganizationDto resourceUser = new UserOrganizationDto();
        resourceUser.setResourceInstanceId(req.getInstanceId());
        resourceUser.setResourceTypeCode(AMP_CODE);
        resourceUser.setRoleIds(Lists.newArrayList(req.getRoleId()));
        resourceUser.setUserIds(req.getUserIds());
        permissionRepository.resourceInstancesUser(resourceUser);
    }

    private void addGitlabMember(CreateMemberVO req, Integer gitlabId) {
        RoleBindingDto gitlabRoleBindingInfo = permissionRepository.getGitlabRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, req.getRoleId().toString());
        if(gitlabRoleBindingInfo == null ){
            log.debug(String.format("resourceTypeCode: %s,roleId: %s。gitlab roleBinding is not found",SystemConstance.AmpResourceTypeCode.SUBSYSTEM, req.getRoleId().toString()));
            return ;
        }
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(req.getUserIds());
        List<ProjectMemberDto> members = req.getUserIds().stream().map(userId -> {
            ProjectMemberDto member = new ProjectMemberDto();
            member.setUserId(userId);
            member.setUsername(userMap.get(userId).getUsername());
            member.setRoleId(gitlabRoleBindingInfo.getTargetRoleId());
            return member;
        }).collect(Collectors.toList());
        codeProjectRepository.addProjectMember(gitlabId, members);
    }

    /**
     *
     * 校验角色是否可操作
     * 当前应用所在租户的管理员、系统管理、都只能添加应用管理员角色
     * gitlab 角色不允许降级修改，循环访问gitlab api性能太低，这里牺牲准确性，提高校验性能，使用amp的角色信息校验
     *
     *
     * @param roleId 目标角色的id
     * @param instanceId 实例id
     * @param userIds 用户id列表
     */
    private void checkLevel(Long roleId, Long instanceId, List<Long> userIds) {
        RoleVo roleVo = permissionRepository.roleDetails(roleId);
        if(roleVo.getBolAdmin() == 1){
            // 是应用管理员，一般对应gitlab  maintainer，不存在gitlab level降级
            return ;
        }
        // 校验用户在系统的角色信息
        DevopsSubSystem subsystem = subsystemRepository.getById(instanceId);
        List<UserRoleVo> resourceRoleVos = permissionRepository.getRolesByInstance(SystemConstance.AmpResourceTypeCode.SYSTEM, subsystem.getSystemId(), userIds);
        List<UserRoleVo> filterResourceAdminRole = resourceRoleVos.stream().filter(userRoleVo -> userRoleVo.getBolAdmin() == 1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterResourceAdminRole)){
            List<User> users = iamRepository.listUserByIds(filterResourceAdminRole.stream().map(UserRoleVo::getUserId).collect(Collectors.toList()));
            List<String> collect = users.stream().map(User::getName).collect(Collectors.toList());
            throw new SystemException(ExceptionCode.SUBSYSTEM_MEMBER_FILE, Joiner.on(",").join(collect) + " 是系统管理员，不能添加非管理员角色");
        }

        // 校验用户在租户下的角色信息
        List<UserRoleVo> tenantRole = permissionRepository.getRolesByInstance(null, null, userIds);
        List<UserRoleVo> filterTenantAdminRole = tenantRole.stream().filter(userRoleVo -> userRoleVo.getBolAdmin() == 1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterTenantAdminRole)){
            List<User> users = iamRepository.listUserByIds(filterTenantAdminRole.stream().map(UserRoleVo::getUserId).collect(Collectors.toList()));
            List<String> collect = users.stream().map(User::getName).collect(Collectors.toList());
            throw new SystemException(ExceptionCode.SUBSYSTEM_MEMBER_FILE, Joiner.on(",").join(collect) + " 是租户管理员，不能添加非管理员角色");
        }
    }

    @Override
    public void deleteMember(DeleteMemberVO deleteMemberVO) {
        // 应用管理权限
        Long sybSystemId = deleteMemberVO.getInstanceId();
        Long userId = deleteMemberVO.getUserId();
        iamRepository.deleteMember(AMP_CODE, sybSystemId, userId);
        // 代码仓库权限
        Integer gitlabId = codeRepoService.getCodeRepoIdBySystemId(sybSystemId);
        User user = iamRepository.getUserById(userId);
        if(gitlabId != null){
            codeProjectRepository.deleteProjectMember(gitlabId, user.getUsername());
        }
        // 流水线权限
        deletePipelineJobPermission(deleteMemberVO);

    }

    private void deletePipelineJobPermission(DeleteMemberVO deleteMemberVO) {
//        DevopsSubSystem subsystem = subsystemRepository.getById(deleteMemberVO.getInstanceId());
//        String systemAmpCode = SystemConstance.AmpResourceTypeCode.SUBSYSTEM;
//        List<UserRoleVo> roleByUser = permissionRepository.getRoleByUser(systemAmpCode, subsystem.getSystemId().toString(), deleteMemberVO.getUserId());
//        for (UserRoleVo userRoleVo : roleByUser) {
//            if(userRoleVo.getBolAdmin() == 1){
//                // 系统管理角色不做操作
//                return ;
//            }
//        }
        subsystemPipelineRepository.deletePermission(deleteMemberVO.getInstanceId(), Lists.newArrayList(deleteMemberVO.getUserId()));
    }

    @Override
    public Page<ListSystemMemberVO> querySystemMember(MemberQuery req) {
        int pageNo = req.getPageNo();
        int pageSize = req.getPageSize();
        Page<UserVo> userInfoList = iamRepository.pageMember(AMP_CODE, req.getInstanceId(), pageNo, pageSize, req.getQueryParam());
        if(CollectionUtils.isEmpty(userInfoList.getRecords())){
            return PageUtils.exchangeRecordData(userInfoList, new ArrayList<>());
        }
        return PageUtils.exchangeRecord(userInfoList, user -> {
            ListSystemMemberVO vo = new ListSystemMemberVO();
            vo.setName(user.getName());
            vo.setUserId(user.getId());
            if(CollectionUtils.isNotEmpty(user.getRoles())){
                RoleVo roleVo = user.getRoles().get(0);
                vo.setRoleName(roleVo.getName());
                vo.setRoleId(roleVo.getId());
            }
            return vo;
        });
    }

    @Override
    public List<UserVo> listOverUser(Long subSystemId, String queryParam) {
        DevopsSubSystem subsystem = subsystemRepository.getById(subSystemId);
        Page<UserVo> userInfoList = iamRepository.pageMember(SystemConstance.AmpResourceTypeCode.SYSTEM, subsystem.getSystemId(), 1, 9999, queryParam);
        List<UserVo> systemMember = userInfoList.getRecords();
        if(CollectionUtils.isEmpty(systemMember)){
            return new ArrayList<>();
        }
        List<UserVo> subSystemMember = iamRepository.resourceMember(AMP_CODE, subSystemId);
        return iamRepository.userOver(systemMember, subSystemMember);
    }

    @Override
    public List<ListSubSystemByCurrentVo> listByCurrent() {
        List<ResourceInstance> resourceInfoDtoList = permissionRepository.resourceList(AMP_CODE);
        return Optional.ofNullable(resourceInfoDtoList).orElse(new ArrayList<>()).stream().map(resource -> {
            ListSubSystemByCurrentVo vo = new ListSubSystemByCurrentVo();
            vo.setId(resource.getResourceInstanceId());
            vo.setName(resource.getResourceInstanceName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ListSystemMemberVO> allMember(Long subSystemId, String user) {
        List<UserVo> userInfo = iamRepository.resourceMember( AMP_CODE, subSystemId);
        List<UserVo> userInfoDto = Optional.ofNullable(userInfo).orElse(new ArrayList<>());
        return userInfoDto.stream().map(u -> {
            ListSystemMemberVO listSystemMemberVO = new ListSystemMemberVO();
            listSystemMemberVO.setUserId(u.getId());
            listSystemMemberVO.setName(u.getName());
            if(CollectionUtils.isNotEmpty(u.getRoles())){
                listSystemMemberVO.setRoleName(u.getRoles().get(0).getName());
                listSystemMemberVO.setRoleId(u.getRoles().get(0).getId());
            }
            return listSystemMemberVO;
        }).collect(Collectors.toList());
    }

    @Override
    public RoleInfoDto subSystemAdminRoles() {
        List<RoleInfoDto> roleInfo = permissionRepository.roleList(AMP_CODE);
        for (RoleInfoDto roleInfoDto : roleInfo) {
            if(SystemConstance.SubSystemRoleCode.SUB_ADMIN.equals(roleInfoDto.getCode())){
                return roleInfoDto;
            }
        }
        return null;
    }

    @Override
    public void initGitlabMember(Long subSystemId, Long gitlabId) {
        List<ListSystemMemberVO> listSystemMemberVOS = allMember(subSystemId, "");
        if(CollectionUtils.isEmpty(listSystemMemberVOS)){
            return ;
        }
        List<Long> userIds = listSystemMemberVOS.stream().map(m -> m.getUserId()).collect(Collectors.toList());
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);
        List<ProjectMemberDto> projectMember = listSystemMemberVOS.stream().map(subSystemMember -> {
            RoleBindingDto gitlabRoleBindingInfo = permissionRepository.getGitlabRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subSystemMember.getRoleId().toString());
            ProjectMemberDto member = new ProjectMemberDto();
            member.setUserId(subSystemMember.getUserId());
            member.setUsername(userMap.get(subSystemMember.getUserId()).getUsername());
            member.setRoleId(gitlabRoleBindingInfo.getTargetRoleId());
            return member;
        }).collect(Collectors.toList());
        codeProjectRepository.addProjectMember(gitlabId.intValue(), projectMember);
    }


    @Override
    public List<UserVo> listMember(Long subSystemId, Boolean withSystemAdmin) {
        SubSystemInfoDto info = null;
        try {
            info = subSystemService.info(subSystemId);
            List<UserVo> subSystemMember = iamRepository.resourceMember(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subSystemId);
            if(withSystemAdmin){
                List<UserVo> systemMember = iamRepository.resourceMemberAdmin(SystemConstance.AmpResourceTypeCode.SYSTEM, info.getSystemId());
                subSystemMember.addAll(systemMember);
            }
            return subSystemMember;
        }catch (Exception e){
            String exception = ((SystemException) e).getExMsg();
            if(e instanceof SystemException && ("事项不存在".equals(exception) || (StringUtils.isNotEmpty(exception) && exception.contains("资源实例不存在")))){
                return new ArrayList<>();
            }
            throw e;
        }
    }

    @Override
    public void saveMember(SaveMemberDto member) {
        List<UserRoleVo> userRoles = permissionRepository.getRolesByInstance(AMP_CODE, member.getInstanceId(), Lists.newArrayList(member.getUserId()));
        if(CollectionUtils.isNotEmpty(userRoles)){
            for (UserRoleVo userRole : userRoles) {
                if(userRole.getRoleId().equals(member.getRoleId())){
                    return ;
                }
            }
            DeleteMemberVO deleteMemberVO = new DeleteMemberVO();
            deleteMemberVO.setUserId(member.getUserId());
            deleteMemberVO.setInstanceId(member.getInstanceId());
            deleteMemberVO.setCheckDirector(false);
            deleteMember(deleteMemberVO);
        }
        CreateMemberVO createMember = new CreateMemberVO();
        createMember.setUserIds(Arrays.asList(member.getUserId()));
        createMember.setRoleId(member.getRoleId());
        createMember.setInstanceId(member.getInstanceId());
        createMember.setCheckCodeRepo(false);
        createMember(createMember, true);
    }

    @Override
    public void modifyMember(ModifyMemberVo req) {
        Long roleId = req.getRoleIds().get(0);
        RoleBindingDto oldGitlabRoleBinding = null;
        RoleBindingDto oldPipelineRoleBinding = null;
        List<UserRoleVo> userRoles = permissionRepository.getRolesByInstance(AMP_CODE, req.getInstanceId(), Lists.newArrayList(req.getUserId()));
        if(CollectionUtils.isNotEmpty(userRoles)){
            for (UserRoleVo userRole : userRoles) {
                if(userRole.getRoleId().equals(roleId)){
                    // 角色不变，直接返回
                    return ;
                }
            }
            oldGitlabRoleBinding = permissionRepository.getGitlabRoleBindingInfo(AMP_CODE, userRoles.get(0).getRoleId().toString());
            oldPipelineRoleBinding = permissionRepository.getPipelineRoleBindingInfo(AMP_CODE, userRoles.get(0).getRoleId().toString());
        }
        checkLevel(roleId, req.getInstanceId(), Lists.newArrayList(req.getUserId()));
        RoleBindingDto newRoleBinding = permissionRepository.getGitlabRoleBindingInfo(AMP_CODE, roleId.toString());
        RoleBindingDto newPipelineBinding = permissionRepository.getPipelineRoleBindingInfo(AMP_CODE, roleId.toString());
        updateGitlabRole(req, oldGitlabRoleBinding, newRoleBinding);
        updatePipelineRole(req, oldPipelineRoleBinding, newPipelineBinding);
        updateAmpRole(req);
    }

    private void updatePipelineRole(ModifyMemberVo req, RoleBindingDto oldPipelineRoleBinding, RoleBindingDto newPipelineBinding) {
        List<Long> jobIds = subsystemPipelineRepository.listJobIdByParam(Arrays.asList(req.getInstanceId()));
        if(CollectionUtils.isEmpty(jobIds)){
            return ;
        }
        if(newPipelineBinding == null && oldPipelineRoleBinding != null){
            // 删除流水线人员原有角色
            subsystemPipelineRepository.deletePermission(jobIds, Arrays.asList(req.getUserId()));
        }
        if(newPipelineBinding != null){
            // 保存流水线角色
            subsystemPipelineRepository.savePermission(jobIds, Arrays.asList(req.getUserId()), newPipelineBinding.getTargetRoleId());
        }

    }

    @Override
    public List<SubsystemDto> listSubsystemRole(Long systemId, Long userId) {
        List<DevopsSubSystem> devopsSubSystems = subsystemRepository.listBySystemId(systemId);
        if(CollectionUtils.isEmpty(devopsSubSystems)){
            return new ArrayList<>();
        }
        List<Long> subIds = devopsSubSystems.stream().map(sub -> sub.getId()).collect(Collectors.toList());
        Map<Long, List<RoleBase>> roleMap = permissionRepository.listRoleByUserId(userId, SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subIds);
        return devopsSubSystems.stream().map(sub -> {
            SubsystemDto subsystemDto = devopsSubSystemMapstruct.toSubsystemDto(sub);
            List<RoleBase> roleBases = roleMap.get(sub.getId());
            if(roleBases != null) {
                subsystemDto.setRoles(roleBases);
            }
            return subsystemDto;
        }).collect(Collectors.toList());
    }

    @Override
    public void modifyMemberBatch(ModifyMemberBatchRequest req) {
        checkLevel(req.getRoleId(), req.getInstanceIds().get(0), Lists.newArrayList(req.getUserId()));
        List<Long> resourceInstanceIds = req.getInstanceIds();
        Long userId = req.getUserId();
        Long roleId = req.getRoleId();
        updateGitlabRoleBatch(req);
        permissionRepository.modifyMemberBatch(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, resourceInstanceIds, userId, roleId);
    }

    private void updateAmpRole(ModifyMemberVo req) {
        permissionRepository.resourceUpdateRole(AMP_CODE, req.getInstanceId(), req.getUserId(), req.getRoleIds());
    }

    private void updateGitlabRoleBatch(ModifyMemberBatchRequest req) {
        List<Integer> gitlabIds = subsystemComponentRepository.listByParam(req.getInstanceIds(), SystemConstance.ComponentType.GITLAB).stream().map(component -> Integer.parseInt(component.getComponentKey())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(gitlabIds)){
            return ;
        }
        RoleBindingDto newRoleBinding = permissionRepository.getGitlabRoleBindingInfo(AMP_CODE, req.getRoleId().toString());
        if(newRoleBinding == null){
            log.error(String.format("resourceTypeCode: %s,roleId: %s。gitlab roleBinding is not found",SystemConstance.AmpResourceTypeCode.SUBSYSTEM, req.getRoleId().toString()));
            codeProjectRepository.updateBatchProjectMember(req.getUserId(), gitlabIds, 0L);
        }
        codeProjectRepository.updateBatchProjectMember(req.getUserId(), gitlabIds, newRoleBinding.getTargetRoleId());
    }

    private void updateGitlabRole(ModifyMemberVo req, RoleBindingDto oldRoleBinding, RoleBindingDto newRoleBinding) {
        Integer gitlabId = codeRepoService.getCodeRepoIdBySystemId(req.getInstanceId());
        if(gitlabId == null){
            return ;
        }
        if(newRoleBinding == null && oldRoleBinding != null){
            // 删除gitlab人员原有角色
            User user = iamRepository.getUserById(req.getUserId());
            codeProjectRepository.deleteProjectMember(gitlabId, user.getUsername());
        }
        if(newRoleBinding != null){
            // 保存gitlab角色
            CreateMemberVO createMemberVO = new CreateMemberVO();
            createMemberVO.setUserIds(Lists.newArrayList(req.getUserId()));
            createMemberVO.setRoleId(req.getRoleIds().get(0));
            createMemberVO.setInstanceId(req.getInstanceId());
            addGitlabMember(createMemberVO, gitlabId);
        }
    }

}
