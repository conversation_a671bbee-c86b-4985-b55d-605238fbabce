package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.enums.EnvVarInitEnum;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.db.mapper.SubSystemEnvMapper;
import cn.harmonycloud.development.pojo.dto.env.EnvVarDTO;
import cn.harmonycloud.development.pojo.entity.SubSystemEnv;
import cn.harmonycloud.development.pojo.entity.SubSystemVariable;
import cn.harmonycloud.development.pojo.vo.env.*;
import cn.harmonycloud.development.pojo.vo.pipeline.WebSelectorDto;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import cn.harmonycloud.development.outbound.api.feign.JobFeign;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.development.service.SubSystemEnvService;
import cn.harmonycloud.development.service.SubSystemVariableService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName SubSystemEnvServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/9/6 9:47 AM
 **/
@Service
public class SubSystemEnvServiceImpl extends ServiceImpl<SubSystemEnvMapper, SubSystemEnv> implements SubSystemEnvService {

    @Autowired
    SubSystemVariableService variableService;

    @Resource
    JobFeign jobFeign;

    @Resource
    SubSystemService subSystemService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult createEnvVar(EnvVarDTO envVarDTO) {
        SubSystemEnv subSystemEnv = SubSystemEnv.builder().subSystemId(envVarDTO.getSubSystemId())
                .envId(envVarDTO.getEnvId()).build();
        SubSystemEnv one = getOne(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getEnvId, envVarDTO.getEnvId())
                .eq(SubSystemEnv::getSubSystemId, envVarDTO.getSubSystemId())
                .last("limit 1"));
        if (null != one) {
            return BaseResult.failed(null, String.format("该子系统下已存在%s环境", envVarDTO.getEnvId()));
        }
        save(subSystemEnv);
        List<SubSystemVariable> vars = null == envVarDTO.getVars() ? new ArrayList<>() : envVarDTO.getVars();
        for (SubSystemVariable var : vars) {
            var.setEId(subSystemEnv.getId());
        }
        variableService.saveBatch(vars);
        return BaseResult.ok(null, "创建成功");
    }

    /**
     * 初始化子系统默认全局环境
     *
     * @param subSystemId
     */
    @Override
    public void InitSubSystemEnv(Long subSystemId) {
        EnvVarDTO envVarDTO = new EnvVarDTO();
        envVarDTO.setSubSystemId(subSystemId);
        envVarDTO.setInitVarsFlag(Boolean.FALSE);
        createEnvVar(envVarDTO);
    }

    @Override
    public BaseResult updateVar(SubSystemVariable subSystemVariable) {
        variableService.updateById(subSystemVariable);
        return BaseResult.ok();
    }

    public SubSystemVariable getVarByParam(Long envId, String varKey) {
        LambdaQueryWrapper<SubSystemVariable> query = new LambdaQueryWrapper<>();
        query.eq(SubSystemVariable::getEId, envId);
        query.eq(SubSystemVariable::getVarKey, varKey);
        List<SubSystemVariable> list = variableService.list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public BaseResult getEnvNames(Long subSystemId) {
        List<SubSystemEnv> list = list(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getSubSystemId, subSystemId));
        Map<Integer, EnvVariableVO> envIdVarMap = getEnvIdVarMap();
        List<WebSelectorDto> result = new ArrayList<>();
        for (SubSystemEnv subSystemEnv : list) {
            if (!envIdVarMap.containsKey(subSystemEnv.getEnvId())) {
                continue;
            }
            EnvVariableVO envVariableVO = envIdVarMap.get(subSystemEnv.getEnvId());
            WebSelectorDto webSelectorDto = new WebSelectorDto(subSystemEnv.getId().toString(), envVariableVO.getEnvName());
            result.add(webSelectorDto);
        }
        return BaseResult.ok(result);
    }

    @Override
    public List<SubSystemVariableVO> getDefaultAndSystemVars(Long envId) {
        List<SubSystemVariableVO> resultList = new ArrayList<>();
        //获取子系统环境变量
        SubSystemEnv subSystemEnv = getById(envId);
        if (null == subSystemEnv) {
            return resultList;
        }
        List<SubSystemVariableVO> vars = getVars(envId);
        resultList.addAll(vars);

        //获取全局环境变量
        SubSystemEnv defaultSystemEnv = getOne(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getSubSystemId, subSystemEnv.getSubSystemId())
                .isNull(SubSystemEnv::getEnvId)
                .last("limit 1"));
        if (null != defaultSystemEnv) {
            List<SubSystemVariableVO> defaultVars = getVars(defaultSystemEnv.getId());
            resultList.addAll(defaultVars);
        }
        return resultList;
    }

    @Override
    public Map<String, Object> getDefaultAndSystemVarsMap(Long envId) {
        SubSystemEnv byId = getById(envId);
        EnvVariableVO envVariableVO = getEnvIdVarMap().get(byId.getEnvId());
        if (envVariableVO == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        List<SubSystemVariableVO> defaultAndSystemVars = getDefaultAndSystemVars(envId);
        Map<String, Object> result = Maps.newHashMap();
        result.put("envName", envVariableVO.getEnvName());
        for (SubSystemVariableVO var : defaultAndSystemVars) {
            String varValue = var.getVarValue();
            if (var.getVariableVO() != null && var.getVariableVO().getId() != null) {
                varValue = var.getVariableVO().getVarValue() + "/" + varValue;
            }
            result.put(var.getVarKey(), varValue);
        }
        return result;
    }

    @Override
    public Map<String, List<SubSystemVariableVO>> getAllEnvData(String subSystemCode, String envCode) {
        SubSystemInfoDto infoDto = subSystemService.infoByCode(subSystemCode);
        List<EnvVo> data = getEnvs(infoDto.getSubSystemId()).getData();
        Map<String, List<SubSystemVariableVO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        data.removeIf(item -> item.getEnvName() == null);
        if (StringUtils.isNotBlank(envCode)) {
            data = data.stream().filter(item -> item.getEnvName().equals(envCode)).collect(Collectors.toList());
        }
        for (EnvVo envVo : data) {
            List<SubSystemVariableVO> vars = getVars(envVo.getId());
            result.put(envVo.getEnvName(), vars);
        }
        return result;
    }


    private void InitVars(List<SubSystemVariable> vars, Long subSystemEnvId) {
        for (EnvVarInitEnum initEnum : EnvVarInitEnum.values()) {
            vars.add(new SubSystemVariable(subSystemEnvId, initEnum.key, initEnum.value, initEnum.description));
        }
    }

    @Override
    public BaseResult createVar(SubSystemVariable subSystemVariable) {
        SubSystemVariable varByParam = getVarByParam(subSystemVariable.getEId(), subSystemVariable.getVarKey());
        if (varByParam != null) {
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "变量已存在，请更改变量名称后重试");
        }
        variableService.save(subSystemVariable);
        return BaseResult.ok(null, "新建成功");
    }

    @Override
    public List<SubSystemVariableVO> getVars(Long envId) {
        SubSystemEnv subSystemEnv = getById(envId);
        List<SubSystemVariable> list = variableService.list(new QueryWrapper<SubSystemVariable>().lambda().eq(SubSystemVariable::getEId, envId));
        Map<Integer, VariableVO> varIdMap = getVarIdMap(subSystemEnv.getEnvId());
        List<SubSystemVariableVO> resultVOS = new ArrayList<>();
        for (SubSystemVariable subSystemVariable : list) {
            SubSystemVariableVO subSystemVariableVO = new SubSystemVariableVO();
            BeanUtils.copyProperties(subSystemVariable, subSystemVariableVO);
            if (null != subSystemVariable.getRelateId()) {
                subSystemVariableVO.setVariableVO(varIdMap.get(subSystemVariable.getRelateId()));
            }
            resultVOS.add(subSystemVariableVO);
        }
        return resultVOS;
    }

    @Override
    public BaseResult<List<EnvVo>> getEnvs(Long subSystemId) {
        List<SubSystemEnv> list = list(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getSubSystemId, subSystemId));
        Map<Integer, EnvVariableVO> envIdVarMap = getEnvIdVarMap();
        List<EnvVo> envVos = new ArrayList<>();
        for (SubSystemEnv subSystemEnv : list) {
            if (!envIdVarMap.containsKey(subSystemEnv.getEnvId()) && null != subSystemEnv.getEnvId()) {
                continue;
            }
            EnvVo envVo = new EnvVo();
            BeanUtils.copyProperties(subSystemEnv, envVo);
            EnvVariableVO envVariableVO = envIdVarMap.get(subSystemEnv.getEnvId());
            if (null != subSystemEnv.getEnvId()) {
                envVo.setEnvName(envVariableVO.getEnvName());
            }
            envVos.add(envVo);
        }
        return BaseResult.ok(envVos);
    }

    @Override
    public BaseResult<MainVarsVO> getMainVars(Long subSystemId) {
        Map<String, EnvVariableVO> envNameVarMap = getEnvNameVarMap();
        EnvVariableVO systemMain = envNameVarMap.get("main");
        if (null == systemMain) {
            return BaseResult.failed(null, "找不到main系统环境");
        }
        SubSystemEnv main = getOne(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getSubSystemId, subSystemId)
                .eq(SubSystemEnv::getEnvId, systemMain.getEnvId())
                .last("limit 1"));
        if (null == main) {
            return BaseResult.failed(null, "该子系统不存在main环境，请先创建main环境");
        }
        MainVarsVO mainVarsVO = new MainVarsVO();
        List<SubSystemVariable> list = variableService.list(new QueryWrapper<SubSystemVariable>().lambda().eq(SubSystemVariable::getEId, main.getId())
                .eq(SubSystemVariable::getDefaultFlag, Boolean.TRUE));
        Map<String, String> map = list.stream().collect(Collectors.toMap(SubSystemVariable::getVarKey, i -> null == i.getVarValue() ? "" : i.getVarValue()));

        String project = null;
        try {
            project = getVarProjectName(subSystemId);
        } catch (Exception e) {
            return BaseResult.failed(null, e.getMessage());
        }
        mainVarsVO.setContainerArtifact(Arrays.asList(map.get("containerArtifact").split(",")));
        mainVarsVO.setOrdinaryArtifact(map.get("ordinaryArtifact").equals("") ? null : Arrays.asList(map.get("ordinaryArtifact").split(",")));
        mainVarsVO.setProjectName(project);
        mainVarsVO.setUpdateRegion(map.get("updateRegion").equals("") ? null : Arrays.asList(map.get("updateRegion").split(",")));
        mainVarsVO.setIsContainer(mainVarsVO.getContainerArtifact().contains("yaml"));
        return BaseResult.ok(mainVarsVO);
    }

    @Override
    public Map getDefaultEnvVar(Long subSystemId) {
        List<SubSystemEnv> list = list(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getSubSystemId, subSystemId).isNull(SubSystemEnv::getEnvId));
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap();
        }
        List<SubSystemVariableVO> vars = getVars(list.get(0).getId());
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap();
        }
        return vars.stream().collect(Collectors.toMap(p -> p.getVarKey(), p -> Optional.ofNullable(p.getVarValue())));
    }

    private String getVarProjectName(Long subSystemId) {
        SubSystemEnv subSystemEnv = getOne(new QueryWrapper<SubSystemEnv>().lambda().eq(SubSystemEnv::getSubSystemId, subSystemId)
                .isNull(SubSystemEnv::getEnvId)
                .last("limit 1"));

        if (null == subSystemEnv) {
            throw new BusinessException("该子系统没有全局环境");
        }
        List<SubSystemVariable> list = variableService.list(new QueryWrapper<SubSystemVariable>().lambda().eq(SubSystemVariable::getEId, subSystemEnv.getId()));
        Optional<SubSystemVariable> any = list.stream().filter(i -> "project".equals(i.getVarKey())).findAny();
        if (any.isPresent()) {
            return any.get().getVarValue();
        }
        return "";
    }

    private Map<Integer, EnvVariableVO> getEnvIdVarMap() {
        BaseResult<List<EnvVariableVO>> result = jobFeign.getAllEnvVariable();
        Map<Integer, EnvVariableVO> map = new HashMap<>();
        if (0 == result.getCode() && CollectionUtils.isNotEmpty(result.getData())) {
            map = result.getData().stream().collect(Collectors.toMap(EnvVariableVO::getEnvId, Function.identity()));
        }
        return map;
    }

    private Map<String, EnvVariableVO> getEnvNameVarMap() {
        BaseResult<List<EnvVariableVO>> result = jobFeign.getAllEnvVariable();
        Map<String, EnvVariableVO> map = new HashMap<>();
        if (0 == result.getCode() && CollectionUtils.isNotEmpty(result.getData())) {
            map = result.getData().stream().collect(Collectors.toMap(EnvVariableVO::getEnvName, Function.identity()));
        }
        return map;
    }

    private Map<Integer, VariableVO> getVarIdMap(Integer varId) {
        BaseResult<Page<VariableVO>> result = jobFeign.getVarByEnvId(varId, 1, 9999);
        Map<Integer, VariableVO> map = new HashMap<>();
        if (0 == result.getCode() && CollectionUtils.isNotEmpty(result.getData().getRecords())) {
            map = result.getData().getRecords().stream().collect(Collectors.toMap(VariableVO::getId, Function.identity()));
        }
        return map;
    }


}
