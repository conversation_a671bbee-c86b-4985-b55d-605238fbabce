package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.enums.TestStatusEnum;
import cn.harmonycloud.development.outbound.db.mapper.AutotestManagementMapper;
import cn.harmonycloud.development.pojo.dto.autotest.AutotestResultDTO;
import cn.harmonycloud.development.pojo.dto.autotest.AutotestDTO;
import cn.harmonycloud.development.pojo.dto.autotest.CreateAutotestDTO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestDetailResultListVO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestDetailVO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestVO;
import cn.harmonycloud.development.pojo.vo.autotest.ResultItemVO;
import cn.harmonycloud.development.pojo.vo.version.ProductPromotionVo;
import cn.harmonycloud.development.outbound.api.dto.promotion.DetailDTO;
import cn.harmonycloud.development.outbound.api.dto.promotion.PromotionDetailRequest;
import cn.harmonycloud.development.service.AutotestManagementService;
import cn.harmonycloud.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AutotestManagementServiceImpl extends ServiceImpl<AutotestManagementMapper, AutotestManagement> implements AutotestManagementService {

    @Autowired
    private IamRepository iamRepository;

    @Autowired
    private TestManagementServiceImpl testManagementService;

    @Autowired
    private TestManagementRepository testManagementRepository;

    @Autowired
    private DevopsSystemRepository devopsSystemRepository;

    @Autowired
    private SubsystemRepository subsystemRepository;

    @Autowired
    private VersionInstanceRepository versionInstanceRepository;

    @Autowired
    private ProductRepository productRepository;

    @Override
    public void create(CreateAutotestDTO dto) {
        Calendar cal = Calendar.getInstance();
        Date date = new Date();//现在的日期
        cal.setTime(date);
        String time = "" + cal.get(Calendar.YEAR) + String.format("%02d", (cal.get(Calendar.MONTH) + 1)) + String.format("%02d", cal.get(Calendar.DAY_OF_MONTH));
        LambdaQueryWrapper<AutotestManagement> queryWrapper = new LambdaQueryWrapper<AutotestManagement>()
                .like(AutotestManagement::getCode, time)
                .orderByDesc(AutotestManagement::getCode);
        List<AutotestManagement> list = this.list(queryWrapper);
        String count = null;
        if (list == null || list.isEmpty()) {
            count = "01";
        } else {
            count = list.get(0).getCode().substring(8);
            count = String.format("%02d", Integer.parseInt(count) + 1);
        }
        List<String> ids = dto.getIds().stream().map((item) -> item.toString()).collect(Collectors.toList());
        String testCodeIds = String.join(",", ids);
        AutotestManagement autotestManagement = new AutotestManagement();
        autotestManagement.setCode(time + count);
        autotestManagement.setTestCodeIds(testCodeIds);
        autotestManagement.setDescription(dto.getDescription());
        autotestManagement.setTestStatus(TestStatusEnum.TEST_WAIT.getCode());
        autotestManagement.setDirectorId(iamRepository.getCurrentUser().getId());
        autotestManagement.setCreateTime(DateUtils.getNowTime());
        autotestManagement.setCreateBy(iamRepository.getCurrentUser().getId());
        this.save(autotestManagement);
        List<TestManagement> updateList = dto.getIds().stream().map((item) -> {
            TestManagement testManagement = new TestManagement();
            testManagement.setId(item);
            testManagement.setTestStatus(TestStatusEnum.TEST_BEING.getCode());
            testManagement.setSqlUpdateFlag(1);
            return testManagement;
        }).collect(Collectors.toList());
        testManagementRepository.updateBatchById(updateList);
    }

    @Override
    public List<AutotestVO> startAutotestList(List<Long> testIdList) {
        List<AutotestVO> list = new ArrayList<>();
        List<TestManagement> testManagements = testManagementRepository.listByIds(testIdList);
        for (TestManagement item : testManagements) {
            DevopsSubSystem subSystem = subsystemRepository.getById(item.getSubSystemId());
            DevopsSystem system = devopsSystemRepository.getById(subSystem.getSystemId());
            AutotestVO vo = new AutotestVO();
            vo.setId(item.getId());
            vo.setTestCode(item.getTestCode());
            vo.setMainSystem(system.getSubFullNameCn());
            vo.setSubSystem(subSystem.getFullNameCn());
            vo.setTestTime(item.getCreateTime());
            vo.setLeader(iamRepository.getUserById(item.getCreateBy()).getName());
            vo.setTester(iamRepository.getUserById(item.getDirectorId()).getName());
            vo.setStatus(TestStatusEnum.getTestStatus(item.getTestStatus()));
            list.add(vo);
        }
        return list;
    }

    /**
     * 自动化测试分页列表
     *
     * @param dto
     * @return
     */
    @Override
    public Page<AutotestVO> getPage(AutotestDTO dto) {
        LambdaQueryWrapper<AutotestManagement> wrapper = new LambdaQueryWrapper<>();
        if (null != dto.getTestCode()) {
            wrapper.like(AutotestManagement::getCode, dto.getTestCode());
        }
        if (null != dto.getStartTime()) {
            wrapper.ge(AutotestManagement::getCreateTime, dto.getStartTime());
        }
        if (null != dto.getEndTime()) {
            wrapper.le(AutotestManagement::getCreateTime, dto.getEndTime());
        }
        wrapper.orderByDesc(AutotestManagement::getCreateTime);
        List<AutotestManagement> list = this.list(wrapper);
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        Page<AutotestVO> page = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(list)) {
            List<AutotestVO> autotestVOS = new ArrayList<>();
            for (AutotestManagement record : list) {
                Boolean waitingFlag = false;
                Boolean testingFlag = false;
                Boolean completeFlag = false;
                List<Long> ids = Arrays.stream(record.getTestCodeIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                List<TestManagement> testManagements = testManagementRepository.listByIds(ids);
                for (TestManagement item : testManagements) {
                    if (item.getTestStatus().equals(TestStatusEnum.TEST_WAIT.getCode())) {
                        waitingFlag = true;
                    } else if (item.getTestStatus().equals(TestStatusEnum.TEST_BEING.getCode())) {
                        testingFlag = true;
                    } else {
                        completeFlag = true;
                    }
                }
                Integer testStatus = checkTestStatus(waitingFlag, testingFlag, completeFlag);
                if (dto.getTestStatus() != null && !testStatus.equals(dto.getTestStatus())) continue;
                AutotestVO autotestVO = new AutotestVO();
                autotestVO.setId(record.getId());
                autotestVO.setTestCode(record.getCode());
                autotestVO.setTestTime(record.getCreateTime());
                //获取测试负责人名称
                String tester = iamRepository.getUserById(record.getDirectorId()).getName();
                if (null != tester) {
                    autotestVO.setTester(tester);
                }
                autotestVO.setStatus(testStatus.toString());
                autotestVO.setDescription(record.getDescription());
                autotestVOS.add(autotestVO);
            }
            page.setTotal(autotestVOS.size());
            page.setRecords(autotestVOS.subList((pageNum - 1) * pageSize, Math.min(autotestVOS.size(), pageNum * pageSize)));
        }
        return page;
    }

    private Integer checkTestStatus(Boolean waitingFlag, Boolean testingFlag, Boolean completeFlag) {
        if (!waitingFlag && !testingFlag && completeFlag) {
            return TestStatusEnum.Test_COMPLETE.getCode();
        }
        if (testingFlag) {
            return TestStatusEnum.TEST_BEING.getCode();
        }
        return TestStatusEnum.TEST_WAIT.getCode();
    }

    /**
     * 自动化测试状态变更
     *
     * @param id
     * @param status
     */
    @Override
    public Boolean updateStatus(Long id, Integer status) {
        AutotestManagement autotestManagement = this.getById(id);
        autotestManagement.setTestStatus(status);
        return this.updateById(autotestManagement);
    }

    @Override
    public BaseResult result(AutotestResultDTO dto) {
        TestManagement testManagement = testManagementRepository.getById(dto.getId());
        //已完成自动晋级
        if (testManagement.getPromotionStatus().equals(1)) {
            if (!dto.getTestResult().equals(TestStatusEnum.TEST_PASS.getCode())) {
                return BaseResult.failed(null, "已完成自动晋级，无法修改测试结果");
            }
            if (!dto.getPromotionStatus()) {
                return BaseResult.failed(null, "已完成自动晋级，无法选择是否自动晋级");
            }
        }
        if (dto.getPromotionStatus().equals(true) && dto.getTestResult().equals(TestStatusEnum.TEST_FAIL.getCode())) {
            return BaseResult.failed(null, "测试失败无法开启自动晋级");
        }
        if (dto.getTestResult() != null) {
            testManagement.setTestResult(dto.getTestResult());
            testManagement.setTestStatus(dto.getTestResult());
        }
        testManagement.setPromotionStatus(dto.getPromotionStatus() ? 1 : 0);
        testManagement.setTestResultDescription(dto.getTestResultDescription());
        testManagement.setTestReportUrls(dto.getDownloadUrl());
        testManagementRepository.updateById(testManagement);
        // 制品自动晋级
        String msg = null;
        if (dto.getPromotionStatus().equals(true)) {
            try {
                VersionInstance versionInstance = versionInstanceRepository.getById(testManagement.getComponentIds());
                //获取buildId,remote查询详细信息
                Long buildId = versionInstance.getPipelineBuildId();
                PromotionDetailRequest promotionDetailRequest = new PromotionDetailRequest();
                promotionDetailRequest.setBuildId(buildId.toString());
                promotionDetailRequest.setEnvId(3L);
                List<DetailDTO> detailDTOS = productRepository.getProductPromotionDetail(promotionDetailRequest);
                Map<String, List<DetailDTO>> map = detailDTOS.stream().collect(Collectors.groupingBy(DetailDTO::getMd5));
                Boolean flag = false;
                for (String key : map.keySet()) {
                    List<DetailDTO> items = map.get(key);
                    //是否包含main制品库
                    Boolean mainFlag = false;
                    //是否已经晋级
                    Boolean prodFlag = false;
                    for (DetailDTO item : items) {
                        if (item.getEnv().getEnvId() == 4 || item.getEnv().getEnvId() == 5) {
                            prodFlag = true;
                        }
                        if (item.getEnv().getEnvId() == 3) {
                            mainFlag = true;
                        }
                    }
                    if (mainFlag && !prodFlag) {
                        //制品晋级
                        flag = true;
                        ProductPromotionVo vo = new ProductPromotionVo();
                        vo.setProductId(Integer.parseInt(items.get(0).getProductId().toString()));
                        vo.setSourceEnvId(3);
                        vo.setTargetEnvId(4);
                        productRepository.promoteProduct(vo);
                    }
                }
                if (!flag) {
                    msg = "所有制品均已晋级";
                } else {
                    msg = "制品晋级成功";
                }
            } catch (Exception e) {
                e.printStackTrace();
                return BaseResult.failed(null, "制品晋级失败");
            }
        }
        return BaseResult.ok(null, msg);
    }

    @Override
    public AutotestDetailVO autotestDetailList(Long id) {
        AutotestManagement autotestManagement = this.getById(id);
        List<String> ids = Arrays.asList(autotestManagement.getTestCodeIds().split(","));
        List<Long> idList = new ArrayList<>();
        ids.forEach((item) -> {
            if (!item.equals("")) {
                idList.add(Long.parseLong(item));
            }
        });
        List<AutotestVO> records = startAutotestList(idList);
        AutotestDetailVO vo = new AutotestDetailVO();
        vo.setId(id);
        vo.setDescription(autotestManagement.getDescription());
        vo.setRecords(records);
        return vo;
    }

    @Override
    public List<AutotestDetailResultListVO> autotestDetailResultList(Long id) {
        AutotestManagement autotestManagement = this.getById(id);
        List<String> ids = Arrays.asList(autotestManagement.getTestCodeIds().split(","));
        List<Long> idList = ids.stream().map(Long::parseLong).collect(Collectors.toList());
        List<TestManagement> testManagements = testManagementRepository.listByIds(idList);
        Map<Long, List<TestManagement>> map = testManagements.stream().collect(Collectors.groupingBy(TestManagement::getSystemId));
        List<AutotestDetailResultListVO> resList = new ArrayList<>();
        for (Long key : map.keySet()) {
            DevopsSystem systemDataVO = devopsSystemRepository.getById(key);
            List<TestManagement> list = map.get(key);
            AutotestDetailResultListVO systemVO = new AutotestDetailResultListVO();
            List<ResultItemVO> itemList = new ArrayList<>();
            list.forEach((item) -> {
                ResultItemVO vo = testManagementService.resultList(item.getId());
                itemList.add(vo);
            });
            systemVO.setSystemName(systemDataVO.getSubFullNameCn());
            systemVO.setRecords(itemList);
            resList.add(systemVO);
        }
        return resList;
    }
}
