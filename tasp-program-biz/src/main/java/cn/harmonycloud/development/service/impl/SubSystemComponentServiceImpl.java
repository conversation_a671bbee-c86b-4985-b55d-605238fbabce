package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigCreateDTO;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigEnvCreateDTO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.outbound.db.mapper.SubSystemComponentMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.scm.RelateProjectRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.SubSystemComponent;
import cn.harmonycloud.development.pojo.vo.config.SubConfigCreateRspVO;
import cn.harmonycloud.development.pojo.vo.config.SubConfigCreateVO;
import cn.harmonycloud.development.pojo.vo.scm.MyMergeRequestVO;
import cn.harmonycloud.development.pojo.vo.scm.ProjectListVO;
import cn.harmonycloud.development.pojo.vo.system.CreateMemberVO;
import cn.harmonycloud.development.pojo.vo.system.DeleteMemberVO;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.UpdateProjectRequest;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.SubSystemComponentEnum;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;
import cn.harmonycloud.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.harmonycloud.development.pojo.SystemConstance.SystemRole.SYS_ADMIN;

@Service
public class SubSystemComponentServiceImpl extends ServiceImpl<SubSystemComponentMapper, SubSystemComponent> implements SubSystemComponentService {

    private final SystemComponentService systemComponentService;
    private final @Lazy SubSystemMemberService subSystemMemberService;
    private final IamRepository iamRepository;
    private final SubsystemComponentRepository subsystemComponentRepository;
    private final CodeProjectRepository codeProjectRepository;
    private final CodeMergeRepository codeMergeRepository;
    private final FeatureBranchRepository featureBranchRepository;
    private final PermissionRepository permissionRepository;
    private final SystemMemberService systemMemberService;
    private final SubsystemRepository subsystemRepository;
    private final ConfigRepository configRepository;
    private final DeployEnvRepository deployEnvRepository;
    private final GitLabProjectService ********************;

    @Autowired
    public SubSystemComponentServiceImpl(
            SystemComponentService systemComponentService,
            @Lazy SubSystemMemberService subSystemMemberService,
            IamRepository iamRepository,
            SubsystemComponentRepository subsystemComponentRepository,
            CodeProjectRepository codeProjectRepository,
            CodeMergeRepository codeMergeRepository,
            FeatureBranchRepository featureBranchRepository,
            PermissionRepository permissionRepository,
            SystemMemberService systemMemberService,
            SubsystemRepository subsystemRepository,
            ConfigRepository configRepository,
            DeployEnvRepository deployEnvRepository,
            GitLabProjectService ********************) {
        this.systemComponentService = systemComponentService;
        this.subSystemMemberService = subSystemMemberService;
        this.iamRepository = iamRepository;
        this.subsystemComponentRepository = subsystemComponentRepository;
        this.codeProjectRepository = codeProjectRepository;
        this.codeMergeRepository = codeMergeRepository;
        this.featureBranchRepository = featureBranchRepository;
        this.permissionRepository = permissionRepository;
        this.systemMemberService = systemMemberService;
        this.subsystemRepository = subsystemRepository;
        this.configRepository = configRepository;
        this.deployEnvRepository = deployEnvRepository;
        this.******************** = ********************;
    }

    private static final String GITLAB = SubSystemComponentEnum.GITLAB.getComponent();

    @Override
    public GitProjectDto gitlabInfo(Long subSystemId) {
        return ********************.gitlabInfo(subSystemId);
    }

    @Override
    public List<MyMergeRequestVO> myMergeRequest(Long subSystemId, Integer type) {
        if (type == null) {
            type = 0;
        }
        List<SubSystemComponent> list = subsystemComponentRepository.listByParam(subSystemId, GITLAB);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        return codeMergeRepository.myMergeRequest(Integer.parseInt(list.get(0).getComponentKey()), type);
    }

    @Override
    public GitProjectDto updateRelation(String oldId, String newId) {
        return ********************.updateRelation(oldId, newId);
    }

    @Override
    public void deleteRelation(String gitlabId) {
        ********************.deleteRelation(gitlabId);
    }

    @Override
    public Long createProject(Long subSystemId, ProjectRequest request, boolean initCodeScan, boolean initMember) {
        return ********************.createProject(subSystemId, request, initCodeScan, initMember);
    }

    @Override
    public GitProjectDto relateProject(RelateProjectRequest request, boolean initCodeScan, boolean initMember) {
        return ********************.relateProject(request, initCodeScan, initMember);
    }

    @Override
    public List<GitProjectDto> getProjectDetail(String subSystemId) {
        LambdaQueryWrapper<SubSystemComponent> queryWrapper = new LambdaQueryWrapper<SubSystemComponent>()
                .eq(SubSystemComponent::getSubSystemId, subSystemId)
                .eq(SubSystemComponent::getComponent, "GITLAB")
                .eq(SubSystemComponent::getKeyType, "id");
        List<SubSystemComponent> list = this.list(queryWrapper);
        if (list == null || list.isEmpty()) {
            return null;
        }
        List<GitProjectDto> resList = new ArrayList<>();
        list.forEach((item) -> {
            GitProjectDto dto = codeProjectRepository.getProject(Integer.parseInt(item.getComponentKey()));
            String createdAt = dto.getCreatedAt();
            String time = DateUtils.changeZoneTime(createdAt, DateUtils.DATE_FORMAT, ZoneId.systemDefault());
            if (item.getCreateTime() != null) {
                time = DateUtils.getTime(item.getCreateTime(), DateUtils.DATE_FORMAT);
            }
            dto.setCreatedAt(time);
            resList.add(dto);
        });
        return resList;
    }

    @Override
    public List<ProjectListVO> getProjectList(String systemId, String search) {
        String groupId = systemComponentService.getComponentKeyBySystemId(Long.parseLong(systemId));
        List<GitProjectDto> projectList = codeProjectRepository.projectList(Integer.parseInt(groupId));
        LambdaQueryWrapper<SubSystemComponent> queryWrapper = new LambdaQueryWrapper<SubSystemComponent>()
                .eq(SubSystemComponent::getSystemId, systemId)
                .eq(SubSystemComponent::getComponent, "GITLAB")
                .eq(SubSystemComponent::getKeyType, "id");
        List<String> usedIds = this.list(queryWrapper).stream().map(SubSystemComponent::getComponentKey).collect(Collectors.toList());
        List<GitProjectDto> list = projectList.stream().filter((item) -> !usedIds.contains(item.getId().toString())).collect(Collectors.toList());
        List<ProjectListVO> resList = new ArrayList<>();
        list.forEach((item) -> {
            if (item.getName().contains(search)) {
                ProjectListVO projectListVO = new ProjectListVO();
                projectListVO.setId(item.getId().toString());
                projectListVO.setName(item.getName());
                resList.add(projectListVO);
            }
        });
        return resList;
    }

    @Override
    public void updateProject(UpdateProjectRequest request) {
        codeProjectRepository.updateProject(request);
    }

    @Override
    public Integer createProject(Long subSystemId, Integer gitlabId) {

        // 数据补偿，将用户变成系统管理员
        checkAndUpdateUserRole(subSystemId);
        if(gitlabId == null){
            return gitlabId;
        }
        if(gitlabId <= 0){
            return createProject(subSystemId, new ProjectRequest(), false, false).intValue();
        }else{
            RelateProjectRequest request = new RelateProjectRequest();
            request.setSubSystemId(subSystemId);
            request.setGitlabId(gitlabId.toString());
            relateProject(request, false, false);
        }
        return gitlabId;
    }

    @Override
    public SubConfigCreateRspVO createConfig(SubConfigCreateVO createVO) {
        List<SubSystemComponent> components = subsystemComponentRepository.listByParam(createVO.getSubsystemId(), SubSystemComponentEnum.CONFIG.getComponent());
        if(CollectionUtils.isNotEmpty(components)){
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "应用已有配置库");
        }
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(createVO.getSubsystemId());
        SubConfigCreateRspVO rsp = new SubConfigCreateRspVO();
        Long configId = createVO.getConfigId();
        if(createVO.getConfigId() == null) {
            configId = configRepository.createConfig(new ConfigCreateDTO(createVO.getProjectName(), createVO.getDescription()));
        }
        SubSystemComponent subSystemComponent = new SubSystemComponent();
        subSystemComponent.setSubSystemId(createVO.getSubsystemId());
        subSystemComponent.setComponent(SubSystemComponentEnum.CONFIG.getComponent());
        subSystemComponent.setComponentKey(configId.toString());
        subSystemComponent.setKeyType(SubSystemComponentEnum.CONFIG.getKeyType());
        subSystemComponent.setCreateBy(iamRepository.getCurrentUser().getId());
        subSystemComponent.setCreateTime(LocalDateTime.now());
        subSystemComponent.setSubSystemId(createVO.getSubsystemId());
        subSystemComponent.setSystemId(devopsSubsystem.getSystemId());
        subSystemComponent.setDelFlag(0);
        subsystemComponentRepository.save(subSystemComponent);

        // 初始化系统的环境作为配置库的环境
//        List<DoDeployEnv> doDeployEnvs = deployEnvRepository.listBySystem(devopsSubsystem.getSystemId());
//        if(CollectionUtils.isNotEmpty(doDeployEnvs)){
//            for (DoDeployEnv doDeployEnv : doDeployEnvs) {
//                configRepository.createEnv(new ConfigEnvCreateDTO(configId, doDeployEnv.getName()));
//            }
//        }
        rsp.setId(configId);
        return rsp;
    }

    private void checkAndUpdateUserRole(Long subSystemId) {
        DevopsSubSystem subsystem = subsystemRepository.getById(subSystemId);
        Long id = iamRepository.getCurrentUser().getId();
        List<UserRoleVo> roles = permissionRepository.getRolesByInstance(SystemConstance.AmpResourceTypeCode.SYSTEM, subsystem.getSystemId(), Lists.newArrayList(id));
        CreateMemberVO createMemberVO = new CreateMemberVO();
        createMemberVO.setRoleId(SYS_ADMIN);
        createMemberVO.setUserIds(Lists.newArrayList(id));
        createMemberVO.setInstanceId(subsystem.getSystemId());
        if(CollectionUtils.isEmpty(roles)){
            // 当前登录人不在系统重，直接分配系统管理员权限
            systemMemberService.createMember(createMemberVO);
            return ;
        }
        Long roleId = roles.get(0).getRoleId();
        // RoleBindingDto gitlabRoleBindingInfo = appManageRemote.getGitlabGroupRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SYSTEM, roleId.toString());
        if(roleId == SYS_ADMIN){
            // 系统管理员不做处理
            return;
        }
        DeleteMemberVO deleteMemberVO = new DeleteMemberVO();
        deleteMemberVO.setInstanceId(subsystem.getSystemId());
        deleteMemberVO.setUserId(id);
        deleteMemberVO.setCheckDirector(false);
        systemMemberService.deleteMember(deleteMemberVO);
        systemMemberService.createMember(createMemberVO);
    }

}
