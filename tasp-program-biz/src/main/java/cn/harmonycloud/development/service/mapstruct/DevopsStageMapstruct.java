package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageConfigDto;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvCreate;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageDto;
import cn.harmonycloud.development.pojo.entity.DevopsStage;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnv;
import cn.harmonycloud.pojo.devopsstage.DevopsStageEnvRspDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface DevopsStageMapstruct {


    @Mappings({
            @Mapping(source = "stageName", target = "name"),
            @Mapping(target = "accessFeatureStatus", ignore = true)
    })
    DevopsStageDto devopsStageToList(DevopsStage devopsStage);

    @Mapping(target = "hostId", ignore = true)
    DevopsStageEnvDto devopsStageEnvToDto(DevopsStageEnv env);

    @Mapping(target = "hostId", ignore = true)
    DevopsStageEnv toDevopsStageEnv(DevopsStageEnvCreate request);

    DevopsStageEnv copy(DevopsStageEnv devopsStageEnv);

    @Mapping(target = "hostId", ignore = true)
    DevopsStageEnvRspDto toDevopsStageEnvRspDto(DevopsStageEnv e);

    @Mappings({
            @Mapping(source = "name", target = "stageName"),
            @Mapping(target = "accessFeatureStatus", ignore = true)
    })
    DevopsStage toDevopsStage(DevopsStageConfigDto config);
}
