package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.SystemFileRepository;
import cn.harmonycloud.development.pojo.dto.web.WebFile;
import cn.harmonycloud.development.pojo.entity.SystemFile;
import cn.harmonycloud.development.service.SystemFileService;
//import cn.harmonycloud.minio.config.MinioProperties;
//import cn.harmonycloud.minio.service.FileTemplate;
import cn.harmonycloud.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/1 2:09 下午
 **/
@Slf4j
@Service
public class SystemFileServiceImpl implements SystemFileService {

    @Autowired
    private SystemFileRepository systemFileRepository;
//    @Autowired
//    private FileTemplate fileTemplate;
    @Value("${minio.bucket-name}")
    private String bucketName;
//    @Resource
//    private MinioProperties minioProperties;
    @Autowired
    private IamRepository iamRepository;
    @Value("${biz.file.downloadUrl:/api/cloud/file/download?id=%s}")
    private String downloadUrl;

    @Override
    public WebFile getWebFileById(Long id) {
        SystemFile systemFile = systemFileRepository.getById(id);
        if (systemFile == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        return toWebFile(systemFile);
    }

    @Override
    public List<WebFile> getWebFileByIds(List<Long> id) {
        if (CollectionUtils.isEmpty(id)) {
            return new ArrayList<>();
        }
        List<SystemFile> systemFiles = systemFileRepository.listByIds(id);
        if (CollectionUtils.isEmpty(systemFiles)) {
            return new ArrayList<>();
        }
        return systemFiles.stream().map(sf -> toWebFile(sf)).collect(Collectors.toList());
    }

    private WebFile toWebFile(SystemFile systemFile) {
        WebFile webFile = new WebFile();
        webFile.setUid(systemFile.getId().toString());
        webFile.setName(systemFile.getFileName());
        webFile.setUrl(getDownloadUrl(systemFile));
        return webFile;
    }

    private String getDownloadUrl(SystemFile systemFile) {
        return String.format(downloadUrl, systemFile.getId());
    }


    @Override
    public WebFile upload(MultipartFile file, String subject) {
        String fileName = UUID.randomUUID().toString();
        try {
          //  fileTemplate.putObject(bucketName, fileName, file.getInputStream(), file.getSize(), file.getContentType());
         //   String url = minioProperties.getUrl() + "/" + bucketName + "/" + fileName;
        } catch (Exception e) {
            log.error(LogUtils.throwableExceptionString(e));
            throw new SystemException(ExceptionCode.INNER_EXCEPTION);
        }
        SystemFile systemFile = new SystemFile();
        systemFile.setFileKey(fileName);
        systemFile.setFileName(file.getOriginalFilename());
        systemFile.setFileSubject(subject);
        systemFile.setBucket(bucketName);
        systemFile.setCreateBy(iamRepository.getCurrentUser().getId());
        systemFile.setCreateTime(LocalDateTime.now());
       // systemFile.setServerUrl(minioProperties.getUrl());
        systemFile.setSaveType(1);
        systemFileRepository.save(systemFile);
        return toWebFile(systemFile);
    }

    @Override
    public void download(Long id, HttpServletResponse response) {
        SystemFile byId = systemFileRepository.getById(id);
        InputStream inStream = null;
        OutputStream outStream = null;
        try {
         //   inStream = fileTemplate.getObject(byId.getBucket(), byId.getFileKey());
            //创建输出流
            outStream = response.getOutputStream();//输出数据
            if (!byId.getFileName().endsWith(".png")) {
                // 设置在下载框默认显示的文件名
                response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(byId.getFileName(), "UTF-8"));
                // 指明response的返回对象是文件流
                response.setContentType("application/octet-stream");
            } else {
                response.setContentType("image/png");
            }
            //写入数据
            IOUtils.copy(inStream, outStream);
        } catch (Exception e) {
            log.error(LogUtils.throwableExceptionString(e));
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "文件下载失败");
        }
    }

}
