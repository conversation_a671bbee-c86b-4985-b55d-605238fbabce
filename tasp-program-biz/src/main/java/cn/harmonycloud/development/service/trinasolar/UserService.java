package cn.harmonycloud.development.service.trinasolar;

import cn.harmonycloud.trinasolar.model.User;
import cn.harmonycloud.trinasolar.model.UserVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserService {

   User getUser();

   String getUserRealName();

   UserVO getUserById(Long id);

   List<UserVO> getUserByIds(List<Long> ids);

   Boolean isAdmin(Long userId, Long projectId);
}
