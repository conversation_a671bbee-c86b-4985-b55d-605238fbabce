package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DeployHostDTO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.devopsstage.*;
import cn.harmonycloud.development.service.DevopsStageService;
import cn.harmonycloud.development.service.mapstruct.DevopsStageMapstruct;
import cn.harmonycloud.pmp.model.entity.User;
import com.alibaba.fastjson.JSONObject;
import cn.harmonycloud.pojo.devopsstage.DevopsStageEnvRspDto;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/7 10:14 上午
 **/
@Service
public class DevopsStageServiceImpl implements DevopsStageService {

    @Autowired
    private DevopsStageRepository devopsStageRepository;
    @Autowired
    private DevopsStageEnvRepository devopsStageEnvRepository;
    @Autowired
    private DevopsStageMapstruct devopsStageMapstruct;
    @Autowired
    private BuildInstanceRepository buildInstanceRepository;
    @Autowired
    private SystemDictRepository systemDictRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private VersionRepository versionRepository;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    private DeployEnvRepository deployEnvRepository;

    @Override
    public List<DevopsStageDto> list(Long subSystemId) {
        List<DevopsStage> devopsStages = devopsStageRepository.listBySubsystemId(subSystemId);
        if (CollectionUtils.isEmpty(devopsStages)) {
            initDefaultStage(subSystemId);
            devopsStages = devopsStageRepository.listBySubsystemId(subSystemId);
        }
        Map<Long, List<DevopsStageEnv>> envGroup = devopsStageEnvRepository.envList(Lists.newArrayList(subSystemId)).stream().collect(Collectors.groupingBy(DevopsStageEnv::getStageId));
        Map<Long, SystemDict> longSystemDictMap = mapSystemStage();
        return devopsStages.stream().map(devopsStage -> {
            SystemDict systemDict = longSystemDictMap.get(devopsStage.getStageDictId());
            DevopsStageDto devopsStageDto = devopsStageMapstruct.devopsStageToList(devopsStage);
            if (systemDict != null) {
                devopsStageDto.setType(Integer.parseInt(systemDict.getDictType()));
            }
            devopsStageDto.setAccessFeatureStatus(devopsStage.getAccessFeatureStatusList());
            List<DevopsStageEnv> devopsStageEnvs = envGroup.get(devopsStage.getId());
            int stageEnvNum = CollectionUtils.isEmpty(devopsStageEnvs) ? 0 : devopsStageEnvs.size();
            devopsStageDto.setStageEnvNum(stageEnvNum);
            return devopsStageDto;
        }).collect(Collectors.toList());
    }

    private Map<Long, SystemDict> mapSystemStage() {
        return listSystemStage().stream().collect(Collectors.toMap(SystemDict::getId, dict -> dict));
    }

    private List<SystemDict> listSystemStage() {
        List<SystemDict> systemDict = systemDictRepository.getByParams(SystemConstance.SystemDictSubject.DEVOPS_STAGE);
        if (CollectionUtils.isEmpty(systemDict)) {
            throw new SystemException(ExceptionCode.DATA_CONFIG_FAIL);
        }
        return systemDict;
    }

    @Override
    public DevopsStageDto info(Long stageId) {
        DevopsStage info = devopsStageRepository.getById(stageId);
        if (info == null) {
            return null;
        }
        DevopsStageDto devopsStageDto = devopsStageMapstruct.devopsStageToList(info);
        devopsStageDto.setAccessFeatureStatus(info.getAccessFeatureStatusList());
        String dictType = mapSystemStage().get(devopsStageDto.getStageDictId()).getDictType();
        devopsStageDto.setType(Integer.parseInt(dictType));
        return devopsStageDto;
    }

    @Override
    public DevopsStageDto infoByStageEnvId(Long stageEnvId) {
        DevopsStageEnvDto devopsStageEnvDto = this.envInfo(stageEnvId);
        return this.info(devopsStageEnvDto.getStageId());
    }

    private int getMaxStage() {
        List<SystemDict> byParams = systemDictRepository.getByParams(SystemConstance.SystemDictSubject.MAX_DEVOPS_STAGE);
        return CollectionUtils.isEmpty(byParams) ? 5 : Integer.parseInt(byParams.get(0).getDictValue());
    }

    @Override
    public void configSave(DevopsStageSaveRequest request) {
        Long subSystemId = request.getSubSystemId();
        List<DevopsStageConfigDto> devopsStageDtoList = request.getDevopsStage();
        Integer maxStage = getMaxStage();
        if (CollectionUtils.isEmpty(devopsStageDtoList) || devopsStageDtoList.size() > maxStage) {
            String msg = String.format("阶段数量最大不得超过%d个", maxStage);
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, msg);
        }
        List<DevopsStage> collect = devopsStageDtoList.stream().map(config -> {
            DevopsStage devopsStage = devopsStageMapstruct.toDevopsStage(config);
            if (CollectionUtils.isNotEmpty(config.getAccessFeatureStatus())) {
                String join = Joiner.on(",").join(config.getAccessFeatureStatus());
                devopsStage.setAccessFeatureStatus(join);
            } else {
                devopsStage.setAccessFeatureStatus("");
            }
            devopsStage.setSubsystemId(subSystemId);
            if (devopsStage.getId() == null) {
                devopsStage.setConfigStatus(1);
            }
            return devopsStage;
        }).collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            collect.get(i).setSort(i);
        }

        User currentUser = iamRepository.getCurrentUser();
        List<DevopsStage> old = devopsStageRepository.listBySubsystemId(request.getSubSystemId());
        List<Long> newIds = collect.stream().filter(ds -> ds.getId() != null).map(ds -> ds.getId()).collect(Collectors.toList());
        List<Long> deleteOld = old.stream().filter(ds -> !newIds.contains(ds.getId())).map(ds -> ds.getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteOld)) {
            devopsStageRepository.removeByIds(deleteOld);
            devopsStageEnvRepository.deleteByStageIds(deleteOld, currentUser.getId());
        }
        devopsStageRepository.saveOrUpdateBatch(collect);
    }

    @Override
    public List<DevopsStageEnvDto> envList(DevopsStageEnvListRequest request) {

        List<DevopsStageEnv> devopsStageEnvs = devopsStageEnvRepository.envList(request);
        return devopsStageEnvToDto(devopsStageEnvs);

    }

    private List<DevopsStageEnvDto> devopsStageEnvToDto(List<DevopsStageEnv> devopsStageEnvs) {
        if (CollectionUtils.isEmpty(devopsStageEnvs)) {
            return new ArrayList<>();
        }
        List<Long> collect = devopsStageEnvs.stream().map(env -> env.getStageId()).collect(Collectors.toList());
        Map<Long, DevopsStage> mapDevopsStage = devopsStageRepository.listByIds(collect).stream().collect(Collectors.toMap(DevopsStage::getId, s -> s));
        return devopsStageEnvs.stream().map(env -> {
            DevopsStageEnvDto devopsStageEnvDto = envToDto(env);
            DevopsStage info = mapDevopsStage.get(env.getStageId());
            devopsStageEnvDto.setEnvId(info.getEnvId());
            devopsStageEnvDto.setStageName(info.getStageName());
            devopsStageEnvDto.setStageEnvName(devopsStageEnvDto.getStageName() + "/" + env.getEnvName());
            return devopsStageEnvDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DevopsStageEnvDto> envList(List<Long> stageEnvIds) {
        if (CollectionUtils.isEmpty(stageEnvIds)) {
            return new ArrayList<>();
        }
        List<DevopsStageEnv> devopsStageEnvs = devopsStageEnvRepository.listByIds(stageEnvIds);
        return devopsStageEnvToDto(devopsStageEnvs);
    }

    @Override
    public DevopsStageEnvDto envInfo(Long stageEnvId) {
        return envInfo(stageEnvId, false);
    }

    @Override
    public DevopsStageEnvDto envInfo(Long stageEnvId, boolean details) {
        DevopsStageEnv stageEnv = devopsStageEnvRepository.getById(stageEnvId);
        if (stageEnv == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        DevopsStageEnvDto devopsStageEnvDto = envToDto(stageEnv);
        if(details){
            // 填充部署资源信息
            if(devopsStageEnvDto.getEnvId() != null){
                DoDeployEnv deployEnv = deployEnvRepository.getById(devopsStageEnvDto.getEnvId().longValue());
                if(deployEnv != null){
                    devopsStageEnvDto.setDeployEnvName(deployEnv.getName());
                }
            }
            List<Integer> hostIds = devopsStageEnvDto.getHostId();
            if(devopsStageEnvDto.getClusterId() != null){
                hostIds.add(devopsStageEnvDto.getClusterId().intValue());
            }
            Map<Integer, DeployHostDTO> hostDTOMap = deployEnvRepository.mapDeployHostByIds(hostIds);
            if(CollectionUtils.isNotEmpty(hostIds)){
                List<String> collect = hostIds.stream().filter((hostId -> hostDTOMap.containsKey(hostId))).map(hostId -> hostDTOMap.get(hostId).getName()).collect(Collectors.toList());
                devopsStageEnvDto.setHostsName(Joiner.on(",").join(collect));
            }
            if(devopsStageEnvDto.getClusterId() != null){
                devopsStageEnvDto.setClusterName(hostDTOMap.getOrDefault(devopsStageEnvDto.getClusterId(), new DeployHostDTO()).getName());
            }
        }
        return devopsStageEnvDto;
    }

    private DevopsStageEnvDto envToDto(DevopsStageEnv stageEnv) {
        DevopsStageEnvDto devopsStageEnvDto = devopsStageMapstruct.devopsStageEnvToDto(stageEnv);
        if (StringUtils.isNotEmpty(stageEnv.getHostId())) {
            List<String> strings = Splitter.on(",").splitToList(stageEnv.getHostId());
            List<Integer> collect = strings.stream().map(id -> Integer.parseInt(id)).collect(Collectors.toList());
            devopsStageEnvDto.setHostId(collect);
        }
        DevopsStage info = devopsStageRepository.getById(stageEnv.getStageId());
        devopsStageEnvDto.setStageName(info.getStageName());
        devopsStageEnvDto.setEnvId(info.getEnvId());
        devopsStageEnvDto.setRawRepoId(info.getRawRepoId());
        devopsStageEnvDto.setContainerRepoId(info.getContainerRepoId());
        devopsStageEnvDto.setStageDictId(info.getStageDictId());
        BuildInstance lastOneByParam = buildInstanceRepository.getLastOneByParam(stageEnv.getId());
        if (lastOneByParam != null) {
            devopsStageEnvDto.setLastBuildJob(lastOneByParam.getJobId());
            devopsStageEnvDto.setLastBuildVersion(lastOneByParam.getVersionId());
            if (lastOneByParam.getVersionId() != null) {
                VersionManagement byId = versionRepository.getById(lastOneByParam.getVersionId());
                if (byId != null && byId.getDeleteStatus() == SystemConstance.NOT_DELETE) {
                    devopsStageEnvDto.setLastBuildVersion(lastOneByParam.getVersionId());
                }
            }
        }

        if (devopsStageEnvDto.getLastBuildJob() != null) {
            devopsStageEnvDto.setLastBuildJobStr(String.valueOf(devopsStageEnvDto.getLastBuildJob()));
        }
        if (devopsStageEnvDto.getLastBuildVersion() != null) {
            Long majorVersionId = versionRepository.getById(devopsStageEnvDto.getLastBuildVersion()).getMajorVersionId();
            devopsStageEnvDto.setLastBuildVersion(majorVersionId);
            devopsStageEnvDto.setLastBuildVersionStr(String.valueOf(majorVersionId));
        }
        return devopsStageEnvDto;
    }

    @Override
    public DevopsStageEnv envCreate(DevopsStageEnvCreate request) {
        Long subsystemId = request.getSubsystemId();
        List<DevopsStageEnv> devopsStageEnvs = devopsStageEnvRepository.envList(subsystemId, request.getStageId(), request.getEnvName());
        if (CollectionUtils.isNotEmpty(devopsStageEnvs)) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "环境名称重复");
        }
        devopsStageEnvs = devopsStageEnvRepository.envList(subsystemId, request.getStageEnvCode());
        if (CollectionUtils.isNotEmpty(devopsStageEnvs)) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "阶段环境编码重复");
        }
        DevopsStageEnv record = devopsStageMapstruct.toDevopsStageEnv(request);
        if (CollectionUtils.isNotEmpty(request.getHostId())) {
            record.setHostId(Joiner.on(",").join(request.getHostId()));
        }
        Long userId = iamRepository.getCurrentUser().getId();
        LocalDateTime now = LocalDateTime.now();
        record.setCreateBy(userId);
        record.setCreateTime(now);
        record.setUpdateBy(userId);
        record.setUpdateTime(now);
        devopsStageEnvRepository.save(record);
        return record;
    }

    @Override
    public void envModify(DevopsStageEnvModify request) {
        DevopsStageEnv devopsStageEnv = devopsStageEnvRepository.getById(request.getId());
        if (devopsStageEnv == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        List<DevopsStageEnv> devopsStageEnvs = devopsStageEnvRepository.envList(devopsStageEnv.getSubsystemId(), devopsStageEnv.getStageId(), request.getEnvName());
        if (CollectionUtils.isNotEmpty(devopsStageEnvs) && !devopsStageEnvs.get(0).getId().equals(request.getId())) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "环境名称重复");
        }
        devopsStageEnvs = devopsStageEnvRepository.envList(devopsStageEnv.getSubsystemId(), request.getStageEnvCode());
        if (CollectionUtils.isNotEmpty(devopsStageEnvs) && !devopsStageEnvs.get(0).getId().equals(request.getId())) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "环境编码重复");
        }
        Long userId = iamRepository.getCurrentUser().getId();
        devopsStageEnv.setEnvName(request.getEnvName());
        devopsStageEnv.setDeployType(request.getDeployType());
        devopsStageEnv.setClusterId(request.getClusterId());
        devopsStageEnv.setStageEnvCode(request.getStageEnvCode());
        devopsStageEnv.setNamespace(request.getNamespace());
        devopsStageEnv.setAddress(request.getAddress());
        if (CollectionUtils.isNotEmpty(request.getHostId())) {
            devopsStageEnv.setHostId(Joiner.on(",").join(request.getHostId()));
        }
        devopsStageEnv.setUpdateBy(userId);
        devopsStageEnv.setUpdateTime(LocalDateTime.now());
        devopsStageEnvRepository.updateById(devopsStageEnv);
    }

    @Override
    public void envDelete(Long stageEnvId) {
        Long userId = iamRepository.getCurrentUser().getId();
        devopsStageEnvRepository.delete(stageEnvId, false, userId);
    }

    @Override
    public Map<Long, SystemStage> mapAllSystemStage() {
        List<SystemDict> systemDicts = listSystemStage();
        return systemDicts.stream().map(dict -> {
            SystemStage stage = new SystemStage();
            stage.setId(dict.getId());
            stage.setCode(dict.getDictCode());
            stage.setName(dict.getDictName());
            stage.setType(Integer.parseInt(dict.getDictType()));

            return stage;
        }).collect(Collectors.toMap(SystemStage::getId, stage -> stage));
    }

    @Override
    public SystemStage getSystemStageByEnvId(Long stageEnvId) {
        DevopsStageEnvDto devopsStageEnvDto = this.envInfo(stageEnvId);
        DevopsStage byId = devopsStageRepository.getById(devopsStageEnvDto.getStageId());
        return mapAllSystemStage().get(byId.getStageDictId());
    }

    @Override
    public void initDefaultStage(Long subSystemId) {
        List<SystemDict> systemDict = systemDictRepository.getByParams(SystemConstance.SystemDictSubject.DEFAULT_DEVOPS_STAGE);
        List<SystemDict> featureStatus = systemDictRepository.getByParams(SystemConstance.SystemDictSubject.FEATURE_STATUS);
        if (CollectionUtils.isEmpty(systemDict)) {
            return;
        }
        List<String> status = featureStatus.stream().filter(dict -> {
            if (StringUtils.isNotEmpty(dict.getDictCustomizeParam())) {
                JSONObject customize = JSONObject.parseObject(dict.getDictCustomizeParam());
                if (customize != null && customize.containsKey("filter") && customize.getBoolean("filter")) {
                    return false;
                }
            }
            return true;
        }).map(dict -> dict.getDictValue()).collect(Collectors.toList());
        String accessFeatureStatus = CollectionUtils.isEmpty(status) ? "" : Joiner.on(",").join(status);
        List<DevopsStage> devopsStageRecord = systemDict.stream().map(dict -> {
            DevopsStage devopsStage = new DevopsStage();
            if (StringUtils.isNotEmpty(dict.getDictCustomizeParam())) {
                DefaultDevopsStageParams defaultDevopsStageParams = JSONObject.parseObject(dict.getDictCustomizeParam(), DefaultDevopsStageParams.class);
                devopsStage.setStageDictId(defaultDevopsStageParams.getStageDictId());
                devopsStage.setDescription(defaultDevopsStageParams.getDescription());
                devopsStage.setSubsystemId(subSystemId);
                devopsStage.setSort(dict.getDictSort());
                devopsStage.setDescription(dict.getDescription());
                devopsStage.setConfigStatus(defaultDevopsStageParams.isCanRemoveFlag() ? 1 : 0);
                devopsStage.setStageName(dict.getDictName());
                devopsStage.setAccessFeatureStatus(accessFeatureStatus);
            }
            return devopsStage;
        }).collect(Collectors.toList());
        devopsStageRepository.saveBatch(devopsStageRecord);
    }

    @Override
    public Map<Integer, List<DevopsStageEnvRspDto>> mapStageEnv(Long systemId, Integer deployEnv, List<Integer> deployIds) {
        List<Long> subSystemIds = subsystemRepository.listBySystemId(systemId).stream().map(sub -> sub.getId()).collect(Collectors.toList());
        Map<Integer, List<DevopsStageEnv>> integerListMap = devopsStageEnvRepository.envMap(subSystemIds, deployEnv, deployIds);
        if (integerListMap.size() == 0) {
            return new HashMap<>();
        }
        Set<Long> subIds = new HashSet<>();
        for (Integer hostId : integerListMap.keySet()) {
            Set<Long> ids = integerListMap.get(hostId).stream().map(e -> e.getSubsystemId()).collect(Collectors.toSet());
            subIds.addAll(ids);
        }
        List<DevopsSubSystem> list = subsystemRepository.listByParams(subIds.stream().collect(Collectors.toList()));
        Map<Long, DevopsSubSystem> mapSubsystem = list.stream().collect(Collectors.toMap(DevopsSubSystem::getId, sub -> sub));

        Map<Integer, List<DevopsStageEnvRspDto>> result = new HashMap<>();
        for (Integer hostId : integerListMap.keySet()) {
            List<DevopsStageEnvRspDto> collect = integerListMap.get(hostId).stream().map(e -> {
                DevopsStageEnvRspDto devopsStageEnvRspDto = devopsStageMapstruct.toDevopsStageEnvRspDto(e);
                DevopsSubSystem devopsSubSystem = mapSubsystem.get(devopsStageEnvRspDto.getSubsystemId());
                String subName = devopsSubSystem == null ? "-" : devopsSubSystem.getFullNameCn();
                devopsStageEnvRspDto.setSubsystemName(subName);
                if (devopsStageEnvRspDto.getDeployType() != DevelopmentConstance.EnvDeployType.k8s && StringUtils.isNotEmpty(e.getHostId())) {
                    List<Integer> idList = Splitter.on(",").splitToStream(e.getHostId()).map(hid -> Integer.parseInt(hid)).collect(Collectors.toList());
                    devopsStageEnvRspDto.setHostId(idList);
                }
                return devopsStageEnvRspDto;
            }).collect(Collectors.toList());
            result.put(hostId, collect);
        }
        return result;
    }
}
