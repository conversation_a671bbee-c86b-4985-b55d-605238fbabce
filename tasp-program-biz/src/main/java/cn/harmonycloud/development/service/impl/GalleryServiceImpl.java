package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.FeatureRepository;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.JobRepository;
import cn.harmonycloud.development.outbound.SubsystemPipelineRepository;
import cn.harmonycloud.development.pojo.dto.test.TestManagementQuery;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.pojo.vo.feature.FeatureListQuery;
import cn.harmonycloud.development.pojo.vo.gallery.StatisticsRequestVo;
import cn.harmonycloud.development.pojo.vo.gallery.StatisticsResponseVo;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.pojo.TimeScope;
import cn.harmonycloud.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/17 5:02 下午
 **/
@Service
public class GalleryServiceImpl implements GalleryService {

    @Autowired
    private FeatureRepository featureRepository;

    @Autowired
    private TestManagementService testManagementService;

    @Autowired
    private IamRepository iamRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private SubsystemPipelineRepository subsystemPipelineRepository;

    @Autowired
    private CodeRepoService codeRepoService;

    @Override
    public StatisticsResponseVo statistics(StatisticsRequestVo requestVo) {
        StatisticsResponseVo response = new StatisticsResponseVo();
        TimeScope timeScope = DateUtils.getTimeScope(requestVo.getTime());
        Long userId = iamRepository.getCurrentUser().getId();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        CompletableFuture.allOf(CompletableFuture.runAsync(() -> {
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    response.setFeature(statisticsFeature(requestVo, timeScope, userId));
                }),  CompletableFuture.runAsync(() -> {
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    response.setPipeline(countBuild(requestVo, timeScope, userId));
                }), CompletableFuture.runAsync(() -> {
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    response.setCodeReview(countCode(requestVo, timeScope, userId));
                })
        ).join();
        return response;
    }

    private Integer countCode(StatisticsRequestVo requestVo, TimeScope timeScope, Long userId) {
        String startTime = DateUtils.getTime(timeScope.getStartTime(), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtils.getTime(timeScope.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        return codeRepoService.merCount(requestVo.getSubSystemId(), startTime, endTime);
    }

    private Integer countBuild(StatisticsRequestVo requestVo, TimeScope timeScope, Long userId) {
        String startTime = DateUtils.getTime(timeScope.getStartTime(), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtils.getTime(timeScope.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        List<Long> idsBySubsystem = subsystemPipelineRepository.getJobIds(requestVo.getSubSystemId());
        if (CollectionUtils.isEmpty(idsBySubsystem)) {
            return 0;
        }
        Integer integer = jobRepository.countBuild(idsBySubsystem, startTime, endTime);
        return integer;
    }

    private int statisticsTestManagement(StatisticsRequestVo requestVo, TimeScope timeScope, Long userId) {
        TestManagementQuery testManagementQuery = new TestManagementQuery();
        testManagementQuery.setSubSystemId(requestVo.getSubSystemId());
        testManagementQuery.setStartTime(timeScope.getStartTime());
        testManagementQuery.setEndTime(timeScope.getEndTime());
        List<TestManagement> list = testManagementService.list(testManagementQuery);
        return list.size();
    }

    private int statisticsFeature(StatisticsRequestVo requestVo, TimeScope timeScope, Long userId) {
        FeatureListQuery featureQuery = new FeatureListQuery();
        String startTime = DateUtils.getTime(timeScope.getStartTime(), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtils.getTime(timeScope.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        featureQuery.setSubSystemId(requestVo.getSubSystemId());
        featureQuery.setStartTime(startTime);
        featureQuery.setEndTime(endTime);
        List<DevopsFeature> listFeatureDtos = featureRepository.list(featureQuery);
        return listFeatureDtos.size();
    }
}
