package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.operation.SystemEnv;
import cn.harmonycloud.development.pojo.entity.DevopsStage;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnv;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.service.OperationService;
import cn.harmonycloud.development.service.SystemComponentService;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.UserVo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/15 1:58 下午
 **/
@Slf4j
@Service
public class OperationServiceImpl implements OperationService {


    @Autowired
    private DevopsStageEnvRepository devopsStageEnvRepository;
    @Autowired
    private DevopsStageRepository devopsStageRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private DeployEnvRepository deployEnvRepository;
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private DevopsSystemRepository systemRepository;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    private CodeGroupRepository codeGroupRepository;
    @Autowired
    private SystemComponentService systemComponentService;

    @Override
    public void iterationEnv() {
        List<DevopsStage> list = devopsStageRepository.list();
        // 所有环境
        List<DevopsStage> filter = list.stream().filter(stage -> stage.getEnvId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        List<Long> subIds = filter.stream().map(s -> s.getSubsystemId()).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        Map<String, DevopsStage> mapStage = filter.stream().collect(Collectors.toMap(s -> s.getId().toString(), s -> s));
        List<DevopsSubSystem> subList = mapSubsystem(subIds);
        Map<Long, DevopsSubSystem> subSystemMap = subList.stream().collect(Collectors.toMap(DevopsSubSystem::getId, s -> s));
        List<DevopsStageEnv> devopsStageEnvs = devopsStageEnvRepository.envList(subIds);
        Map<Long, Map<Integer, Set<Integer>>> deploys = new HashMap<>();
        for (DevopsStage devopsStage : filter) {
            // 填充环境
            DevopsSubSystem devopsSubSystem = subSystemMap.get(devopsStage.getSubsystemId());
            if (devopsSubSystem == null) {
                continue;
            }
            Map<Integer, Set<Integer>> envMap = deploys.get(devopsSubSystem.getSystemId());
            if (envMap == null) {
                envMap = new HashMap<>();
                deploys.put(devopsSubSystem.getSystemId(), envMap);
            }
            envMap.put(devopsStage.getEnvId(), null);
        }
        List<DevopsStageEnv> multiple = new ArrayList<>();
        for (DevopsStageEnv devopsStageEnv : devopsStageEnvs) {
            DevopsSubSystem devopsSubSystem = subSystemMap.get(devopsStageEnv.getSubsystemId());
            if (devopsSubSystem == null) {
                continue;
            }
            DevopsStage devopsStage = mapStage.get(devopsStageEnv.getStageId().toString());
            if (devopsStage == null) {
                continue;
            }
            Map<Integer, Set<Integer>> envMap = deploys.get(devopsSubSystem.getSystemId());
            if (envMap == null) {
                envMap = new HashMap<>();
                deploys.put(devopsSubSystem.getSystemId(), envMap);
            }
            Set<Integer> hostIs = envMap.get(devopsStage.getEnvId());
            if (hostIs == null) {
                hostIs = new HashSet<>();
                envMap.put(devopsStage.getEnvId(), hostIs);
            }
            if (devopsStageEnv.getDeployType() == DevelopmentConstance.EnvDeployType.k8s) {
                hostIs.add(devopsStageEnv.getClusterId());
            } else {
                if (StringUtils.isNotEmpty(devopsStageEnv.getHostId())) {
                    List<Integer> collect = Splitter.on(",").splitToStream(devopsStageEnv.getHostId()).map(h -> Integer.parseInt(h)).collect(Collectors.toList());
                    hostIs.addAll(collect);
                    if (collect.size() > 1) {
                        multiple.add(devopsStageEnv);
                    }
                }
            }
        }

        List<SystemEnv> systemEnvList = new ArrayList<>();
        Set<Long> set = deploys.keySet();
        for (Long systemId : set) {
            Map<Integer, Set<Integer>> setMap = deploys.get(systemId);
            if (setMap != null) {
                for (Integer envId : setMap.keySet()) {
                    Set<Integer> hostIds = setMap.get(envId);
                    if (CollectionUtils.isEmpty(hostIds)) {
                        SystemEnv record = new SystemEnv();
                        record.setSystemId(systemId);
                        record.setEnvId(envId);
                        systemEnvList.add(record);
                    } else {
                        for (Integer hostId : hostIds) {
                            SystemEnv record = new SystemEnv();
                            record.setSystemId(systemId);
                            record.setEnvId(envId);
                            record.setHostId(hostId);
                            systemEnvList.add(record);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(systemEnvList)) {
            return;
        }
        System.out.println(JSONObject.toJSONString(systemEnvList));

        List<SystemEnv> systemEnv = deployEnvRepository.createSystemEnv(systemEnvList);
        if (CollectionUtils.isEmpty(systemEnv)) {
            return;
        }
        Map<Long, List<DevopsSubSystem>> collect = subList.stream().collect(Collectors.groupingBy(DevopsSubSystem::getSystemId));
        for (SystemEnv env : systemEnv) {
            List<DevopsSubSystem> devopsSubSystems = collect.get(env.getSystemId());
            if (CollectionUtils.isEmpty(devopsSubSystems)) {
                continue;
            }
            List<Long> subsystemIds = devopsSubSystems.stream().map(s -> s.getId()).collect(Collectors.toList());
            devopsStageRepository.updateEnvId(subsystemIds, env.getEnvId(), env.getNewEnvId());
            if (env.getHostId() != null && env.getNewHostId() != null && !env.getHostId().equals(env.getNewHostId())) {
                LambdaUpdateWrapper<DevopsStageEnv> updateHostWrapper = new LambdaUpdateWrapper<>();
                updateHostWrapper.in(DevopsStageEnv::getSubsystemId, subsystemIds);
                updateHostWrapper.eq(DevopsStageEnv::getHostId, env.getHostId());
                updateHostWrapper.set(DevopsStageEnv::getHostId, env.getNewHostId());
                devopsStageEnvRepository.update(updateHostWrapper);

                LambdaUpdateWrapper<DevopsStageEnv> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(DevopsStageEnv::getSubsystemId, subsystemIds);
                updateWrapper.eq(DevopsStageEnv::getClusterId, env.getHostId());
                updateWrapper.set(DevopsStageEnv::getClusterId, env.getNewHostId());
                devopsStageEnvRepository.update(updateWrapper);
            }
        }


        for (DevopsStageEnv devopsStageEnv : multiple) {
            DevopsSubSystem devopsSubSystem = subSystemMap.get(devopsStageEnv.getSubsystemId());
            if (devopsSubSystem == null) {
                continue;
            }
            String hostId = devopsStageEnv.getHostId();
            List<Integer> hostIds = Splitter.on(",").splitToStream(devopsStageEnv.getHostId()).map(h -> Integer.parseInt(h)).collect(Collectors.toList());
            Long systemId = devopsSubSystem.getSystemId();

            List<Integer> collect1 = hostIds.stream().map(id -> {
                for (SystemEnv env : systemEnv) {
                    if (systemId.equals(env.getSystemId()) && (env.getHostId() != null && env.getHostId().equals(id)) && env.getNewHostId() != null) {
                        return env.getNewHostId();
                    }
                }
                return id;
            }).collect(Collectors.toList());

            String join = Joiner.on(",").join(collect1);
            if (!StringUtils.equals(join, hostId)) {
                LambdaUpdateWrapper<DevopsStageEnv> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(DevopsStageEnv::getId, devopsStageEnv.getId());
                updateWrapper.set(DevopsStageEnv::getHostId, join);
                devopsStageEnvRepository.update(updateWrapper);
            }

        }
        System.out.println(systemEnvList.size());

    }

    @Override
    public void iterationSystemMember() {
        List<ResourceInstance> resources = permissionRepository.resourceList(SystemConstance.AmpResourceTypeCode.SYSTEM);
        List<Long> instanceIds = resources.stream().map(resource -> resource.getResourceInstanceId()).collect(Collectors.toList());
        List<Long> systemIds = systemRepository.listByParams(instanceIds).stream().map(sys -> sys.getId()).collect(Collectors.toList());
        systemIds.forEach(id -> {
            try {
                iterationSystemMember(id);
            }catch (Exception e){
                log.error("systemId:" + id + "  " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void iterationSystemMember(Long id) {
        List<UserVo> userInfoList = iamRepository.resourceMember(SystemConstance.AmpResourceTypeCode.SYSTEM, id);
        if(CollectionUtils.isEmpty(userInfoList)){
            return;
        }
        Integer groupId = systemComponentService.getCodeGroupId(id);
        for (UserVo userVo : userInfoList) {
            if(CollectionUtils.isNotEmpty(userVo.getRoles())){
                Long roleId = userVo.getRoles().get(0).getId();
                if(!roleId.equals(8L) || userVo.getId().equals(1L)){
                    continue;
                }
                try {
                    User userById = iamRepository.getUserById(userVo.getId());
                    codeGroupRepository.deleteGroupMember(groupId, userById.getUsername());
                }catch (Exception e){
                    log.error("systemId:" + id + ",username:" + userVo.getId() + "  " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
    }


    private List<DevopsSubSystem> mapSubsystem(List<Long> subIds) {
        if (CollectionUtils.isEmpty(subIds)) {
            return new ArrayList<>();
        }
        return subsystemRepository.listByIds(subIds);
    }
}
