package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.pojo.entity.MajorVersion;
import cn.harmonycloud.development.pojo.entity.VersionComponents;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.UpdateVersionVo;
import cn.harmonycloud.development.pojo.vo.version.VersionDetails;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemDto;
import cn.harmonycloud.pojo.version.MajorVersionDto;
import cn.harmonycloud.pojo.version.VersionDto;
import cn.harmonycloud.development.pojo.vo.version.VersionVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

@Mapper(componentModel = "spring")
public interface VersionMapstruct {

    VersionMapstruct MAPSTRUCT = Mappers.getMapper(VersionMapstruct.class);



    @Mappings({
            @Mapping(source = "tarList", target = "tarList", ignore = true),
            @Mapping(source = "yamlList", target = "yamlList", ignore = true)
    })
    VersionVo versionManagementToVo(VersionManagement versionManagement);

    VersionDto toVersionDto(VersionManagement data);

    VersionManagement toVersionManagement(UpdateVersionVo request);

    VersionDetails toVersionDetails(VersionManagement version);

    VersionComponents createBeanVersionComponents(Long subsystemId, Long versionId, Long createBy, LocalDateTime createTime);

    VersionSubsystemDto toVersionSubsystemDto(VersionSubsystem vs);

    MajorVersionDto toMajorVersionDto(MajorVersion data);
}
