package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectManagementDto;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectUserDto;
import cn.harmonycloud.issue.model.DevopsIssuesDTO;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import cn.harmonycloud.project.model.dto.UserDTO;
import cn.harmonycloud.project.model.vo.ProjectBaseVO;
import cn.harmonycloud.project.model.vo.ProjectQueryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ProjectMapstruct {


    ProjectManagementDto v2ToProject(ProjectQueryVO project);

    ProjectManagementDto v1ToProject(ProjectQueryVO project);

    ProjectManagementDto v2ToProject(ProjectBaseVO project);

    ProjectManagementDto v1ToProject(ProjectBaseVO project);

    @Mapping(target = "ownerList", ignore = true)
    IssuesDto v1ToIssues(IssuesDTO is);

    ProjectUserDto v2ToUser(UserDTO u);

    ProjectUserDto v1ToUser(UserDTO u);

    @Mapping(target = "ownerList", ignore = true)
    IssuesDto v2ToIssues(DevopsIssuesDTO is);
}
