package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.FeatureTaskRepository;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.ProjectRepository;
import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectManagementDto;
import cn.harmonycloud.development.pojo.dto.project.*;
import cn.harmonycloud.development.pojo.entity.FeatureTask;
import cn.harmonycloud.development.pojo.vo.project.*;
import cn.harmonycloud.development.service.ProjectManagementService;
import cn.harmonycloud.development.service.mapstruct.TestManagementMapstruct;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectManagementServiceImpl implements ProjectManagementService {


    @Autowired
    private ProjectRepository projectRepository;
    //    @Autowired
//    private IssuesRepository issuesRepository;
    @Autowired
    private FeatureTaskRepository featureTaskRepository;
    @Autowired
    private TestManagementMapstruct testManagementMapstruct;
    @Autowired
    private IamRepository iamRepository;

    public static String ISSUES_TYPE_CODE = "pm_demand";

//    @Override
//    public List<IssuesDto> listIssues(List<Long> featureId) {
//        List<Long> featureTasks = featureTaskRepository.listIdByFeatureId(featureId);
//        if(CollectionUtils.isEmpty(featureTasks)){
//            return new ArrayList<>();
//        }
//        return issuesRepository.listTask(featureTasks);
//    }

    @Override
    public Map<Long, List<IssuesDto>> mapIssues(List<Long> featureIds) {
        if (CollectionUtils.isEmpty(featureIds)) {
            return new HashMap<>();
        }
        List<Long> ids = featureIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
        List<FeatureTask> featureTasks = featureTaskRepository.listByFeatureId(ids);
        if (CollectionUtils.isEmpty(featureTasks)) {
            return ids.stream().collect(Collectors.toMap(id -> id, id -> new ArrayList<>()));
        }
        List<Long> collect = featureTasks.stream().map(ft -> Long.parseLong(ft.getTaskKey())).collect(Collectors.toList());
        Map<Long, List<FeatureTask>> mapFeatureTask = featureTasks.stream().collect(Collectors.groupingBy(FeatureTask::getFeatureId));
        //todo
        //  List<IssuesDto> issuesDTOS = issuesRepository.listTask(collect);
        List<IssuesDto> issuesDTOS = new ArrayList<>();
        Map<Long, IssuesDto> issuesMap = issuesDTOS.stream().collect(Collectors.toMap(IssuesDto::getId, i -> i));
        return ids.stream().collect(Collectors.toMap(id -> id, id -> {
            List<FeatureTask> ft = mapFeatureTask.getOrDefault(id, new ArrayList<>());
            List<IssuesDto> records = new ArrayList<>();
            for (FeatureTask featureTask : ft) {
                Long issuesId = Long.parseLong(featureTask.getTaskKey());
                IssuesDto issuesDTO = issuesMap.get(issuesId);
                if (issuesDTO != null) {
                    records.add(issuesDTO);
                }
            }
            return records;

        }));
    }

    @Override
    public List<IssuesWithProjectDto> listIssuesWithProjectName(List<Long> featureId) {
        // todo List<IssuesDto> issuesDTOS = this.listIssues(featureId);
        List<IssuesDto> issuesDTOS = new ArrayList<>();
        List<Long> projectIds = issuesDTOS.stream().map(iss -> iss.getProjectId()).collect(Collectors.toList());
        Map<Long, ProjectManagementDto> projectMap = projectRepository.mapAllProjects(projectIds);
        return issuesDTOS.stream().map(is -> {
            IssuesWithProjectDto ip = testManagementMapstruct.toIssuesProject(is);
            String name = projectMap.getOrDefault(ip.getProjectId(), new ProjectManagementDto()).getName();
            ip.setProjectName(name);
            return ip;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProjectDto> groupProjectIssues(List<Long> featureId) {
        // todo List<IssuesDto> issuesDTOS = this.listIssues(featureId);
        List<IssuesDto> issuesDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(issuesDTOS)) {
            return new ArrayList<>();
        }
        Map<Long, List<IssuesDto>> issues = issuesDTOS.stream().collect(Collectors.groupingBy(IssuesDto::getProjectId));
        List<Long> projectIds = issues.keySet().stream().collect(Collectors.toList());
        Map<Long, ProjectManagementDto> projectMap = projectRepository.mapAllProjects(projectIds);
        return projectIds.stream().map(projectId -> {
            ProjectDto dto = new ProjectDto();
            dto.setProjectId(projectId);
            dto.setProjectName(projectMap.getOrDefault(projectId, new ProjectManagementDto()).getName());
            dto.setIssues(issues.get(projectId));
            return dto;
        }).collect(Collectors.toList());

    }
//
//    @Override
//    public List<IssuesDto> taskList(Long projectId, Integer queryType, List<Long> issuesClassicIdList) {
//        if(queryType == 1){
//            // 我负责的
//            Long id = iamRepository.getCurrentUser().getId();
//            ArrayList<Long> director = Lists.newArrayList(id);
//            return issuesRepository.listTask(projectId, director, issuesClassicIdList, true);
//        }
//        return issuesRepository.listTask(projectId, null, issuesClassicIdList ,true);
//    }

//    @Override
//    public List<IssuesDTO> getCustomIssuesByFeatureIds(List<Long> featureId) {
//        List<Long> ids = featureTaskRepository.listIdByFeatureId(featureId);
//        if(CollectionUtils.isEmpty(ids)){
//            return new ArrayList<>();
//        }
//        List<IssuesDTO> listByIds = issuesRepository.getListByIds(ISSUES_TYPE_CODE, ids);
//        List<Long> projectIds = listByIds.stream().filter(issues -> issues.getProjectId() != null).map(issues -> issues.getProjectId()).collect(Collectors.toList());
//        Map<Long, ProjectManagementDto> longProjectManagementDtoMap = projectRepository.mapBaseProjects(projectIds);
//        for (IssuesDTO issues : listByIds) {
//            Map<String, Object> customFields = issues.getCustomFields();
//            if(customFields == null){
//                customFields = new HashMap<>();
//                issues.setCustomFields(customFields);
//
//            }
//            if(issues.getProjectId() == null){
//               continue;
//            }
//            ProjectManagementDto projectManagementDto = longProjectManagementDtoMap.getOrDefault(issues.getProjectId(), new ProjectManagementDto());
//            customFields.put("projectName", projectManagementDto.getName());
//        }
//        return listByIds;
//    }

}
