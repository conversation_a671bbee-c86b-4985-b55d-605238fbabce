package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.DeployEnvRepository;
import cn.harmonycloud.development.outbound.DevopsSystemRepository;
import cn.harmonycloud.development.outbound.SubsystemRepository;
import cn.harmonycloud.development.outbound.SystemRepoRepository;
import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionNodeDTO;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionStrategyDTO;
import cn.harmonycloud.development.pojo.dto.repository.SystemRepoQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.vo.repository.CreateRepositoryRequest;
import cn.harmonycloud.development.service.RepositoryService;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/4 3:44 下午
 **/
@Service
public class RepositoryServiceImpl implements RepositoryService {

    @Autowired
    private SystemRepoRepository systemRepoRepository;
    @Autowired
    private DevopsSystemRepository systemRepository;
    @Autowired
    private DeployEnvRepository deployEnvRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;

    @Override
    public void createRepository(CreateRepositoryRequest request) {
        DoDeployEnv doDeployEnv = deployEnvRepository.envMap(Lists.newArrayList(request.getEnvId())).get(request.getEnvId());
        if(doDeployEnv == null || StringUtils.isEmpty(doDeployEnv.getEnvCode())){
            throw new SystemException(ExceptionCode.REMOTE_APP_MANAGE_FAIL, "环境编码为空，请设置后重试");
        }
        DevopsSystem devopsSystem = systemRepository.getById(request.getSystemId());
        systemRepoRepository.create(request, devopsSystem);
    }

    @Override
    public List<DevopsRepository> list(Long configId, Long systemId, Long subsystemId, String format, String repoName, Boolean withPublicRepository, Boolean withProdRepository) {
        SystemRepoQuery query = new SystemRepoQuery(systemId, format, repoName);
        if (withPublicRepository != null){
            query.setWithPublicRepository(withPublicRepository);
        }
        if (withProdRepository != null){
            query.setWithProdRepository(withProdRepository);
        }
        if(systemId == null){
            if(subsystemId == null){
                throw new SystemException(ExceptionCode.REQUEST_MESSAGE_NOT_READ, "systemId or subsystemId select at least one");
            }
            DevopsSubSystem devopsSubSystem = subsystemRepository.getById(subsystemId);
            query.setSystemId(devopsSubSystem.getSystemId());
        }
        query.setConfigId(configId);
        return systemRepoRepository.listByParams(query);
    }

    @Override
    public void remove(Long id) {
        systemRepoRepository.delete(id);
    }

    @Override
    public List<DoDeployEnv> envList(Long systemId) {
        List<DevopsRepository> list = this.list(null, systemId, null, "", "", null, null);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        List<Long> envIds = list.stream().filter(repo -> repo.getEnvId() != null).map(repo -> repo.getEnvId()).distinct().collect(Collectors.toList());
        Map<Long, DoDeployEnv> longDoDeployEnvMap = deployEnvRepository.envMap(envIds);

        return envIds.stream().filter(envId -> longDoDeployEnvMap.get(envId) != null).map(envId -> {
            return longDoDeployEnvMap.get(envId);
        }).collect(Collectors.toList());

    }

    @Override
    public List<DevopsRepository> targetRepository(Long systemId, String format, Long sourceRepoId) {
        List<PromotionStrategyDTO> promotionStrategyDTOS = systemRepoRepository.listPromotionStrategy(systemId, format);
        if(CollectionUtils.isEmpty(promotionStrategyDTOS) || CollectionUtils.isEmpty(promotionStrategyDTOS.get(0).getPromotionNodes())){
            // 没有晋级策略
            return this.list(null, systemId, null, format, null, true, true);
        }
        for (int i = 0; i < promotionStrategyDTOS.get(0).getPromotionNodes().size(); i++) {
            PromotionNodeDTO promotionNodeDTO = promotionStrategyDTOS.get(0).getPromotionNodes().get(i);
            if(sourceRepoId.equals(promotionNodeDTO.getRepoId())){
                // 在策略中
                if(i == promotionStrategyDTOS.get(0).getPromotionNodes().size() - 1){
                    // 最后一个
                    return this.list(null, -1L, null, format, null, true, true);
                }else{
                    // 返回下一晋级节点仓库
                    PromotionNodeDTO next = promotionStrategyDTOS.get(0).getPromotionNodes().get(i+1);
                    return systemRepoRepository.listByIds(Lists.newArrayList(next.getRepoId()));
                }
            }
        }
        // 不在策略中
        List<DevopsRepository> list = this.list(null, -1L, null, format, null, true, true);
        PromotionNodeDTO promotionNodeDTO = promotionStrategyDTOS.get(0).getPromotionNodes().get(0);
        if(CollectionUtils.isNotEmpty(list.stream().filter(repo -> repo.getId().equals(promotionNodeDTO.getRepoId())).collect(Collectors.toList()))){
            return list;
        }
        List<DevopsRepository> devopsRepositories = systemRepoRepository.listByIds(Lists.newArrayList(promotionNodeDTO.getRepoId()));
        list.addAll(devopsRepositories);
        return list;
    }
}
