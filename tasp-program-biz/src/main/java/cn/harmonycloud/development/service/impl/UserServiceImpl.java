package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.common.core.constant.CommonConstants;
import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.development.execption.thgn.AppException;
import cn.harmonycloud.development.service.trinasolar.UserService;
import cn.harmonycloud.development.util.RequestUtils;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.trinasolar.UpmsGatewayProvider;
import cn.harmonycloud.trinasolar.model.R;
import cn.harmonycloud.trinasolar.model.User;
import cn.harmonycloud.trinasolar.model.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("userService")
public class UserServiceImpl implements UserService {

    @Autowired
    private UpmsGatewayProvider upmsGatewayProvider;

    @Override
    public User getUser() {
        String token = RequestUtils.getToken();
        if (StringUtils.isEmpty(token)) {
            log.error("token is null");
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "token is null");
        }
        R<User> currents;
        try {
            currents = upmsGatewayProvider.getCurrents(token);
        } catch (Exception e) {
            log.error("token校验失败" + e.getMessage());
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "未登录" + e.getMessage());
        }
        if (!CommonConstants.SUCCESS.equals(currents.getCode())) {
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "获取当前用户信息失败:" + currents.getData());
        }
        if (null == currents.getData()) {
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "获取当前用户信息失败,未获取到任何数据！");
        }
        System.out.println(currents.getData());
        return currents.getData();
    }

    @Override
    public String getUserRealName() {
        User user = getUser();
        return user.getUserRealname();
    }

    @Override
    public UserVO getUserById(Long id) {
        List<Long> ids = List.of(id);
        // 校验用户是否存在
        R<List<UserVO>> result = upmsGatewayProvider.getByIds(ids);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "获取对应用户数据异常！");
        }
        return result.getData().get(0);
    }

    @Override
    public List<UserVO> getUserByIds(List<Long> ids) {
        R<List<UserVO>> result = upmsGatewayProvider.getByIds(ids);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "获取对应用户数据异常！");
        }
        return result.getData();
    }

    @Override
    public Boolean isAdmin(Long userId, Long projectId) {
        R<Boolean> result = upmsGatewayProvider.isAdmin(RequestUtils.getToken(), projectId, userId);
        if (!result.isSuccess()) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "获取对应用户数据异常！");
        }
        return result.getData();
    }
}
