package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.promotion.SavePromotionRelationReq;
import cn.harmonycloud.development.outbound.api.enums.RepositoryKindEnum;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeRequestDto;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.MajorVersion;
import cn.harmonycloud.development.pojo.entity.VersionComponents;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.vo.repository.CreateInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ProductTreeQuery;
import cn.harmonycloud.development.pojo.vo.repository.ProductVersionQuery;
import cn.harmonycloud.development.service.ProductService;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.development.service.mapstruct.DevopsProductMapstruct;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/12 9:40 上午
 **/
@Service
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private DevopsProductMapstruct productMapstruct;
    @Autowired
    private SubSystemService subSystemService;
    @Autowired
    private VersionComponentsRepository versionComponentsRepository;
    @Autowired
    private VersionRepository versionRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private MajorVersionRepository majorVersionRepository;
    @Autowired
    private IamRepository iamRepository;


    @Override
    public List<ProductTreeDto> tree(ProductTreeQuery request) {
        ProductTreeRequestDto productQuery = productMapstruct.toProductTreeRequestDto(request);
        if(request.getRepoId() != null){
            ArrayList<Long> longs = Lists.newArrayList(request.getRepoId());
            productQuery.setRepoIds(longs);;
        }
        ArrayList<Long> systemIds = Lists.newArrayList(request.getSystemId());
        if(request.getSubsystemId() != null){
            ArrayList<Long> longs = Lists.newArrayList(request.getSubsystemId());
            productQuery.setSubsystemIds(longs);
        }else{
            List<Long> subsystemIds = subSystemService.listResourceIds(request.getSystemId());
            subsystemIds.add(0L);
            systemIds.add(0L);
            productQuery.setSubsystemIds(subsystemIds);
        }
        productQuery.setSystemIds(systemIds);
        return productRepository.tree(productQuery);
    }

    @Override
    public List<String> versionList(ProductVersionQuery query) {
        List<Long> repoIds = new ArrayList<>();
        if(query.getRepoId() != null){
            repoIds.add(query.getRepoId());
        }
        List<Long> subsystemIds = null;
        if(query.getSubsystemId() != null){
            subsystemIds = Lists.newArrayList(query.getSubsystemId());
        }else{
            subsystemIds = subSystemService.listResourceIds(query.getSystemId());
            if(CollectionUtils.isEmpty(subsystemIds)){
                return new ArrayList<>();
            }
        }
        // 临时方案使用制品库的版本集合
        return productRepository.versionList(query.getSystemId(), repoIds, subsystemIds, RepositoryKindEnum.ARTIFACT.getType());
    }
    @Transactional
    @Override
    public void savaPromotionRelation(SavePromotionRelationReq savePromotionRelationReq) {
        VersionComponents versionComponents = new VersionComponents();
        Long versionId = savePromotionRelationReq.getVersionId();
        Long productId = savePromotionRelationReq.getProductId();
        VersionManagement versionManagement = versionRepository.getById(versionId);
        DevopsSubSystem subSystem = subsystemRepository.getById(versionManagement.getSubSystemId());
        Long systemId = subSystem.getSystemId();
        CreateInstanceReq createInstanceReq = new CreateInstanceReq();
        createInstanceReq.setProductId(productId);
        createInstanceReq.setSystemId(systemId);
        productRepository.createInstance(createInstanceReq);
        versionComponents.setSubsystemId(subSystem.getId());
        versionComponents.setVersionId(versionId);
        versionComponents.setComponent(SystemConstance.VersionComponent.PRODUCT);
        versionComponents.setComponentKey(productId.toString());
        versionComponents.setCreateTime(LocalDateTime.now());
        versionComponents.setCreateBy(iamRepository.getCurrentUser().getId());
        versionComponentsRepository.save(versionComponents);
    }

    @Override
    public List<DevopsProductMetadataDto> listByVersion(Long subsystemId, String versionNumber, Long repoId, String format) {
        List<MajorVersion> majorVersions = majorVersionRepository.listByParams(subsystemId, versionNumber);
        if(CollectionUtils.isEmpty(majorVersions)){
            return new ArrayList<>();
        }
        List<VersionManagement> versionManagements = versionRepository.listByMajorVersionIds(majorVersions.stream().map(mv -> mv.getId()).collect(Collectors.toList()), null);
        List<String> versionNumbers = versionManagements.stream().map(v -> v.getVersionNumber()).collect(Collectors.toList());
        return productRepository.list(repoId, subsystemId, format, versionNumbers);
    }
}
