package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleBindingDto;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.outbound.util.ThreadPoolUtil;
import cn.harmonycloud.development.pojo.entity.SubSystemPipeline;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.pojo.vo.gallery.PipelineViewUpdateRequest;
import cn.harmonycloud.development.pojo.vo.pipeline.*;
import cn.harmonycloud.development.outbound.api.dto.pipeline.*;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.pmp.constant.DictCons;
import cn.harmonycloud.pmp.model.entity.Role;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.util.LogUtils;
import cn.harmonycloud.util.SystemUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.context.annotation.Lazy;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/25 4:45 下午
 **/
@Slf4j
@Service
public class PipelineServiceImpl implements PipelineService {

    @Autowired
    private SubsystemPipelineRepository subsystemPipelineRepository;
    @Autowired
    private JobRepository jobRepository;
    @Autowired
    private IamRepository iamRepository;

    @Autowired
    @Lazy
    private SubSystemMemberService subSystemMemberService;

    @Autowired
    private SystemDictRepository systemDictRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    private DevopsSystemRepository systemRepository;
    @Autowired
    private PermissionRepository permissionRepository;

    public static String START_PARAM = "START_PARAM";

    @Override
    public List<PipelineVo> getPipelineBySystemId(Long subSystemId, String envCode, String draftStatus) {
        List<SubSystemPipeline> data = subsystemPipelineRepository.listByParams(subSystemId);
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        Map<Long, SubSystemPipeline> pipelineMap = data.stream().collect(Collectors.toMap(SubSystemPipeline::getPipelineJobId, d -> d));
        List<Long> ids = data.stream().map(d -> d.getPipelineJobId()).collect(Collectors.toList());
        List<JobDto> jobDtos = Optional.ofNullable(jobRepository.listJob(ids, true, draftStatus)).orElse(new ArrayList<>());
        if (StringUtils.isNotEmpty(envCode)) {
            jobDtos = jobDtos.stream().filter(job -> job.getJobName().startsWith(envCode)).collect(Collectors.toList());
        }
        return jobDtos.stream().map(jobVo -> {
            PipelineVo pipelineVo = new PipelineVo();
            pipelineVo.setSubSystemId(subSystemId);
            pipelineVo.setPipelineJobId(jobVo.getId());
            pipelineVo.setJobName(jobVo.getJobName());
            pipelineVo.setStatus(jobVo.getStatus());
            Integer viewType = pipelineMap.get(jobVo.getId()).getViewType();
            boolean view = viewType != null && viewType == 1;
            pipelineVo.setViewType(view);
            return pipelineVo;
        }).collect(Collectors.toList());
    }

    @Override
    public Long createPipeline(PipelineCreateRequest request) {
        DevopsSubSystem subSystem = subsystemRepository.getById(request.getSubSystemId());
        DevopsSystem system = systemRepository.getById(subSystem.getSystemId());

        return createPipelineByTemplate(subSystem, system, request);
    }

    @Override
    public Page<JobDto> listByPage(PipelinePageRequest request) {
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<SubSystemPipeline>();
        query.eq(request.getSubSystemId() != null, SubSystemPipeline::getSubSystemId, request.getSubSystemId());
        Page<SubSystemPipeline> page = new Page<>(request.getPageNo(), request.getPageSize());
        List<SubSystemPipeline> records = subsystemPipelineRepository.list(query);
        if (CollectionUtils.isEmpty(records)) {
            page.setTotal(0);
            return PageUtils.exchangeRecordData(page, new ArrayList<>());
        }
        List<Long> jobIds = records.stream().map(p -> p.getPipelineJobId()).collect(Collectors.toList());

        PipelinePageDto pipelinePageDto = new PipelinePageDto();
        pipelinePageDto.setIds(jobIds);
        pipelinePageDto.setEnvId(request.getEnvId());
        pipelinePageDto.setJobName(request.getJobName());
        pipelinePageDto.setLabel(request.getLabel());
        pipelinePageDto.setDraftStatus(request.getDraftStatus());
        return jobRepository.listByPage(request.getPageNo(), request.getPageSize(), pipelinePageDto);
    }


    @Override
    public void pipelineViewUpdate(PipelineViewUpdateRequest request) {
        LambdaUpdateWrapper<SubSystemPipeline> update = new LambdaUpdateWrapper<SubSystemPipeline>()
                .eq(SubSystemPipeline::getSubSystemId, request.getSubSystemId())
                .eq(SubSystemPipeline::getPipelineJobId, request.getPipelineJobId());
        SubSystemPipeline pipeline = new SubSystemPipeline();
        pipeline.setSubSystemId(request.getSubSystemId());
        pipeline.setPipelineJobId(request.getPipelineJobId());
        pipeline.setViewType(request.isViewType() ? 1 : 0);
        subsystemPipelineRepository.update(pipeline, update);
    }

    @Override
    public List<BuildDetailDto> pipelineView(Long subSystemId) {
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<SubSystemPipeline>();
        query.eq(SubSystemPipeline::getSubSystemId, subSystemId);
        query.eq(SubSystemPipeline::getViewType, 1);
        List<SubSystemPipeline> subSystemPipelines = subsystemPipelineRepository.list(query);
        if (CollectionUtils.isEmpty(subSystemPipelines)) {
            return new ArrayList<>();
        }
        List<Long> jobIds = subSystemPipelines.stream().map(subSystemPipeline -> subSystemPipeline.getPipelineJobId()).collect(Collectors.toList());
        List<JobDto> jobDtos = jobRepository.listJob(jobIds, true, "0");
        if (CollectionUtils.isEmpty(subSystemPipelines)) {
            return new ArrayList<>();
        }
        List<Long> ids = jobDtos.stream().map(job -> job.getId()).collect(Collectors.toList());
        List<CompletableFuture<BuildDetailDto>> builds = getCompletable(ids);
        return syncBuildDetail(builds);
    }

    @Override
    public void createPipelineBinding(PipelineBindingRequest request) {
        User currentUser = iamRepository.getCurrentUser();
        SubSystemPipeline subSystemPipeline = subsystemPipelineRepository.getbyrequest(request);
        if(subSystemPipeline != null){
            return;
        }
        this.save(request.getSubsystemId(), request.getJobId(), currentUser.getId());
        subsystemPipelinePermission(request.getSubsystemId(), Lists.newArrayList(request.getJobId()), currentUser.getId());
    }

    @Override
    public void relevanceJobs(RelevanceJobsRequest request) {
        if (CollectionUtils.isEmpty(request.getJobIds())) {
            return;
        }
        DevopsSubSystem subsystem = subsystemRepository.getById(request.getSubsystemId());
        // 调用流水线接口绑定系统子系统关系
        jobRepository.bindingSubsystem(subsystem.getSystemId(), subsystem.getId(), request.getJobIds());
        // 本地保存关联关系
        User currentUser = iamRepository.getCurrentUser();
        subsystemPipelineRepository.saveJobs(request.getSubsystemId(), request.getJobIds(), currentUser);
        // 流水线授权
        subsystemPipelinePermission(request.getSubsystemId(), request.getJobIds(), null);
    }

    /**
     *
     *
     * @param subsystemId
     * @param jobIds
     * @param currentId 操作人，将做为流水线的管理员
     */
    private void subsystemPipelinePermission(Long subsystemId, List<Long> jobIds, Long currentId){
        List<UserVo> userVos = subSystemMemberService.listMember(subsystemId, false).stream().filter(user -> CollectionUtils.isNotEmpty(user.getRoleIds())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userVos)) {
            List<RoleBindingDto> roleBindingDtos = permissionRepository.listRoleBindingInfo(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, SystemConstance.AmpResourceTypeCode.PIPELINE_JOB);
            Map<Long, List<UserVo>> collect = userVos.stream().collect(Collectors.groupingBy(user -> user.getRoleIds().get(0)));
            Map<Long, List<RoleBindingDto>> roleBindingDtoMap = roleBindingDtos.stream().collect(Collectors.groupingBy(RoleBindingDto::getSourceRoleId));
            for (Long sourceRoleId : collect.keySet()) {
                List<RoleBindingDto> orDefault = roleBindingDtoMap.getOrDefault(sourceRoleId, new ArrayList<>());
                if (CollectionUtils.isNotEmpty(orDefault)) {
                    Long targetRoleId = orDefault.get(0).getTargetRoleId();
                    subsystemPipelineRepository.savePermission(jobIds, collect.get(sourceRoleId).stream().map(u -> u.getId()).collect(Collectors.toList()), targetRoleId);
                }
            }
        }
        if (currentId != null) {
            // 将创建人作为流水线的管理员角色
            subsystemPipelineRepository.savePermission(jobIds, Lists.newArrayList(currentId), DictCons.Role.PipelineAdminRole.getId());
        }
    }

    @Override
    public void unBind(PipelineBindingRequest request) {
        jobRepository.deleteBing(Lists.newArrayList(request.getJobId()));
        subsystemPipelineRepository.removeByParams(request.getSubsystemId(), request.getJobId());
    }

    @Override
    public void unBind(List<Long> subsystemIds) {
        List<Long> jobIds = subsystemPipelineRepository.listJobIdByParam(subsystemIds);
        if (CollectionUtils.isEmpty(jobIds)) {
            return;
        }
        jobRepository.deleteBing(jobIds);
        subsystemPipelineRepository.removeByParams(jobIds);
    }

    private List<BuildDetailDto> syncBuildDetail(List<CompletableFuture<BuildDetailDto>> builds) {
        List<BuildDetailDto> result = new ArrayList();
        CompletableFuture buildDetailCompletableFuture = CompletableFuture.allOf(builds.toArray(new CompletableFuture[builds.size()])).whenComplete((v, throwable) -> {
            if (throwable != null) {
                log.error(LogUtils.throwableExceptionString(throwable));
                throw new SystemException(ExceptionCode.INNER_EXCEPTION);
            }
            for (CompletableFuture<BuildDetailDto> build : builds) {
                result.add(build.getNow(null));
            }
        });
        try {
            buildDetailCompletableFuture.get();
        } catch (Exception e) {
            LogUtils.throwableExceptionString(e);
            throw new SystemException(ExceptionCode.INNER_EXCEPTION);
        }
        return result;
    }

    private List<CompletableFuture<BuildDetailDto>> getCompletable(List<Long> jobIds) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        List<CompletableFuture<BuildDetailDto>> builds = new ArrayList<>();
        for (Long jobId : jobIds) {
            builds.add(CompletableFuture.supplyAsync(() -> {
                RequestContextHolder.setRequestAttributes(requestAttributes);
                return jobRepository.getRecentBuildByJobId(jobId);
            }, ThreadPoolUtil.OA_THREAD_POOL_EXECUTOR));
        }
        return builds;
    }

    private Long createPipelineByTemplate(DevopsSubSystem subSystem, DevopsSystem system, PipelineCreateRequest request) {
        List<StartParamDto> startParam = getDefaultStartParam();
        List<EnvParamDto> envParam = getDefaultEnvParam(subSystem, system, request);
        Map<String, Object> metadata = getDefaultMetadata(subSystem, system);
        Long jobId = jobRepository.createJob(subSystem, system, startParam, envParam, request.getTemplateId());
        jobRepository.createMetadata(jobId, metadata);
        save(subSystem.getId(), jobId, iamRepository.getCurrentUser().getId());
        return jobId;
    }

    private Map<String, Object> getDefaultMetadata(DevopsSubSystem subSystem, DevopsSystem system) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("systemId", system.getId());
        metadata.put("systemCode", system.getSysCode());
        metadata.put("subSystemId", subSystem.getId());
        metadata.put("subSystemCode", subSystem.getSubCode());
        metadata.put("subSystemCodeLower", SystemUtils.getLowerSubSystemCode(subSystem.getSubCode()));
        //metadata.put("subSystemCodeAbbr", subSystem.getOutCode().toLowerCase());
        return metadata;
    }

    private void save(Long subSystemId, Long jobId, Long createUserId) {
        SubSystemPipeline subSystemPipeline = new SubSystemPipeline();
        subSystemPipeline.setSubSystemId(subSystemId);
        subSystemPipeline.setPipelineJobId(jobId);
        subSystemPipeline.setViewType(0);
        subSystemPipeline.setCreateBy(createUserId);
        subSystemPipeline.setCreateTime(LocalDateTime.now());
        subsystemPipelineRepository.save(subSystemPipeline);
    }

    private List<EnvParamDto> getDefaultEnvParam(DevopsSubSystem subSystem, DevopsSystem system, PipelineCreateRequest request) {
        List<EnvParamDto> envParam = new ArrayList<>();
        envParam.add(new EnvParamDto("systemCode", system.getSysCode()));
        envParam.add(new EnvParamDto("subSystemCode", subSystem.getSubCode()));
        return envParam;
    }

    private List<StartParamDto> getDefaultStartParam() {
        List<SystemDict> systemDict = systemDictRepository.getByParams(START_PARAM);
        if (CollectionUtils.isEmpty(systemDict)) {
            return new ArrayList<>();
        }
        List<StartParamDto> startParamDto = new ArrayList<>();
        for (SystemDict dict : systemDict) {
            StartParamDto param = new StartParamDto();
            param.setKey(dict.getDictCode());
            param.setValue(dict.getDictValue());
            JSONObject jsonObject = JSONObject.parseObject(dict.getDictCustomizeParam());
            param.setShowFlag(jsonObject.getBoolean("showFlag"));
            startParamDto.add(param);
        }
        return startParamDto;
    }

}
