package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.CodeBranchRepository;
import cn.harmonycloud.development.outbound.FeatureBranchRepository;
import cn.harmonycloud.development.outbound.FeatureRepository;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchDto;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.enums.BranchModelEnum;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.service.CodeRepoService;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.service.FeatureBranchService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.crypto.Data;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【feature_branch(特性关联分支表)】的数据库操作Service实现
 * @createDate 2022-08-03 11:35:24
 */
@Service
public class FeatureBranchServiceImpl implements FeatureBranchService {

    @Autowired
    private CodeRepoService codeRepoService;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    private CodeBranchRepository codeBranchRepository;
    @Autowired
    private FeatureBranchRepository featureBranchRepository;
    @Autowired
    private FeatureRepository featureRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FeatureBranch create(DevopsFeature devopsFeature, String sourceBranch, Long issueId) {
        Integer featureNumber = devopsFeature.getFeatureNumber();
        if (featureNumber == null) {
            featureNumber = 1;
        }
        Long subSystemId = devopsFeature.getSubSystemId();
        String branchDesc = devopsFeature.getBranchDesc();
        String branchName = getBranchName(featureNumber, devopsFeature.getFeatureType(), branchDesc, issueId);
        Integer gitlabId = codeRepoService.getScmIdBySystemId(subSystemId);
        // 保存数据
        BranchDto gitlabBranch = createGitlabBranch(branchName, gitlabId, sourceBranch);
        String devBranchName = branchName;
        if (StringUtils.equals(devopsFeature.getBranchModel(), BranchModelEnum.CONTROLLED.getCode())) {
            devBranchName = getDevBranchName(featureNumber, devopsFeature.getFeatureType(), branchDesc, issueId);
            createGitlabBranch(devBranchName, gitlabId, sourceBranch);
        }
        FeatureBranch defaultBranch = new FeatureBranch();
        defaultBranch.setBranchModel(devopsFeature.getBranchModel());
        defaultBranch.setFeatureId(devopsFeature.getId());
        defaultBranch.setBranchName(branchName);
        defaultBranch.setBranchDevName(devBranchName);
        defaultBranch.setCreateBy(iamRepository.getCurrentUser().getId());
        defaultBranch.setCreateTime(LocalDateTime.now());
        defaultBranch.setDelFlag(0);
        defaultBranch.setSubSystemId(subSystemId);
        defaultBranch.setSourceRef(sourceBranch);
        defaultBranch.setIsClear(false);
        defaultBranch.setSourceCommit(gitlabBranch.getCommit().getId());
        featureBranchRepository.save(defaultBranch);
        return defaultBranch;
    }

    private BranchDto createGitlabBranch(String branchName, Integer gitlabId, String sourceBranch) {
        if (StringUtils.isEmpty(sourceBranch)) {
            sourceBranch = codeRepoService.getBaseBranch();
        }
        return codeBranchRepository.createBranch(branchName, gitlabId, sourceBranch, false);
    }


    private String getBranchName(int number, Integer type, String branchDesc, Long issueId) {
        if (issueId != null) {
            return  "feature-" + issueId.toString();
        }
        switch (type) {
            case 0:
                return "feature-" + number + "-" + branchDesc;
            case 1:
                return "bugfix-" + number + "-" + branchDesc;
            default:
                return "";
        }
    }

    private String getDevBranchName(int number, Integer type, String branchDesc, Long issueId) {
        if(issueId != null){
            return "feature-dev-" + issueId.toString();
        }
        switch (type) {
            case 0:
                return "feature-dev-" + number + "-" + branchDesc;
            case 1:
                return "bugfix-dev-" + number + "-" + branchDesc;
            default:
                return "";
        }
    }

    @Override
    public void createBranchCheck(long subSystemId, String sourceBranch) {
        Integer gitlabId = codeRepoService.getScmIdBySystemId(subSystemId);
        String baseBranch = codeRepoService.getBaseBranch();
        if (StringUtils.isNotEmpty(sourceBranch)) {
            baseBranch = sourceBranch;
        }
        BranchDto branchDto = codeBranchRepository.detailsBranch(gitlabId, baseBranch);
        if(branchDto == null){
            String format = String.format("代码库 %s 主分支不存在，请创建主分支后重试", baseBranch);
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, format);
        }

    }

    @Override
    public List<String> listAllBranch(List<FeatureBranch> records, boolean withClear, boolean withDevBranch) {
        if(CollectionUtils.isEmpty(records)){
            return new ArrayList<>();
        }
        List<FeatureBranch> filterRecords = records.stream().filter(featureBranch -> withClear || featureBranch.getIsClear() == null || !featureBranch.getIsClear()).collect(Collectors.toList());
        if(filterRecords.size() == 0){
            return new ArrayList<>();
        }
        List<String> branches = filterRecords.stream().map(fb -> fb.getBranchName()).collect(Collectors.toList());
        if(withDevBranch){
            branches.addAll(filterRecords.stream().map(fb -> fb.getBranchDevName()).collect(Collectors.toList()));
        }
        return branches;
    }


}




