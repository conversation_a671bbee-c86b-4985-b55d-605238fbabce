package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.api.dto.promotion.ProductMetadataInfoDto;
import cn.harmonycloud.development.pojo.FeatureConstance;
import cn.harmonycloud.development.pojo.dto.feature.FeatureFeatureBranchDTO;
import cn.harmonycloud.development.pojo.dto.feature.FeatureTaskDTO;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.vo.feature.DevopsFeatureDto;
import cn.harmonycloud.development.pojo.vo.feature.FeatureListRequest;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineMergeNotify;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.outbound.api.dto.promotion.ProductMetadataDto;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineNotifyReq;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineProductBuildReq;
import cn.harmonycloud.development.pojo.vo.repository.CreateInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ListInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ProductPageQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionIterationRequest;
import cn.harmonycloud.development.service.mapstruct.VersionMapstruct;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsFactory;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.development.*;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageDto;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.dto.devopsstage.SystemStage;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.development.pojo.vo.feature.FeatureBranchRequestDto;
import cn.harmonycloud.development.pojo.vo.project.ProjectDto;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import cn.harmonycloud.development.outbound.api.dto.pipeline.BuildDetailDto;
import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.development.service.mapstruct.DevelopmentMapstruct;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.version.VersionDto;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import cn.harmonycloud.tenant.TenantContextHolder;
import cn.harmonycloud.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.harmonycloud.constants.DevelopmentConstance.ChecklistStatus.NOT_DETECTED;
import static cn.harmonycloud.constants.DevelopmentConstance.ChecklistStatus.SUSS;
import static cn.harmonycloud.constants.DevelopmentConstance.StageType.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/10 2:38 下午
 **/
@Service
public class DevelopmentServiceImpl implements DevelopmentService {

    @Autowired
    private FeatureService featureService;
    @Autowired
    private DevopsStageEnvFeatureRepository stageEnvFeatureRepository;
    @Autowired
    private DevopsStageEnvRepository stageEnvRepository;
    @Autowired
    private BuildInstanceRepository buildInstanceRepository;
    @Autowired
    private DevopsStageService devopsStageService;
    @Autowired
    private CodeRepoService codeRepoService;
    @Autowired
    private SubSystemService subSystemService;
    @Autowired
    private OnlineOrderRepository onlineOrderRepository;
    @Autowired
    private DevelopmentMapstruct developmentMapstruct;
    @Autowired
    private OnlineOrderSubsystemRepository onlineOrderSubsystemRepository;
    @Autowired
    private ProjectManagementService projectManagementService;
    @Autowired
    private VersionRepository versionRepository;
    @Autowired
    private VersionService versionService;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    private JobRepository jobRepository;
    @Autowired
    private SubsystemPipelineRepository subsystemPipelineRepository;
    @Autowired
    private CodeMergeRepository codeMergeRepository;
    @Autowired
    private CodeProjectRepository codeProjectRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private SystemDictRepository systemDictRepository;
    @Autowired
    private MergeTaskRepository mergeTaskRepository;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private RunStartParamsFactory runStartParamsFactory;
    @Autowired
    private FeatureTaskRepository featureTaskRepository;
    @Autowired
    private VersionMapstruct versionMapstruct;
    @Autowired
    private MajorVersionRepository majorVersionRepository;
    @Autowired
    private FeatureBranchRepository featureBranchRepository;
    @Autowired
    private DevopsSystemRepository devopsSystemRepository;
    @Autowired
    private VersionComponentsRepository versionComponentsRepository;


    @Value("${biz.codeScanDetailUrl:/efficiency/codeManage/quality?serverId=%s&id=%s&historyId=%s&view=history_detail&only=true&activeKey=vulnerability}")
    private String codeScanDetailUrl;

    @Override
    public List<FeatureTaskDTO> featureQuery(QueryFeatureRequest requestVo) {
        FeatureBranchRequestDto requestDto = featureBranchRequestDto(requestVo);
        requestDto.setFeatureStatusFilter(Lists.newArrayList(FeatureConstance.FeatureStatus.CLEAR));
        List<FeatureTaskDTO> featureTasks = featureService.listFeatureTask(requestDto);
        return featureTasks;
    }

    @Override
    public List<FeatureTaskDTO> featureIntegration(Long stageEnvId, Long versionId) {
        List<Long> featureIds = stageEnvFeatureRepository.selectFeatureList(stageEnvId, versionId);
        if (CollectionUtils.isEmpty(featureIds)) {
            return new ArrayList<>();
        }
        List<FeatureTaskDTO> features = featureService.listFeatureTask(featureIds);
        if (CollectionUtils.isNotEmpty(features)) {
            DevopsStageEnv stageEnv = stageEnvRepository.getById(stageEnvId);
            Integer codeRepoId = codeRepoService.getCodeRepoIdBySystemId(stageEnv.getSubsystemId());
            features.forEach(feature -> {
                feature.setGitlabId(codeRepoId);
            });
        }
        return features;
    }

    @Override
    public void pipelineBuild(PipelineBuildRequest request) {
        DevopsStageEnvDto devopsStageEnvDto = devopsStageService.envInfo(request.getStageEnvId());
        List<Long> featureIds = stageEnvFeatureRepository.selectFeatureList(request.getStageEnvId(), request.getVersionId());
        User currentUser = iamRepository.getCurrentUser();
        List<JenkinsFileStartParameter> startParameters = getStartParam(request, devopsStageEnvDto);
        Long buildId = jobRepository.buildJob(startParameters, request.getJobId(), currentUser, request.getRunDescribe());
        VersionManagement opened = versionRepository.getOpened(request.getVersionId());
        if(opened == null){
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "请选择版本");
        }
        BuildInstance instance = new BuildInstance();
        instance.setJobId(request.getJobId());
        instance.setBuildId(buildId);
        instance.setCreateBy(currentUser.getId());
        instance.setStageEnvId(request.getStageEnvId());
        instance.setCreateTime(LocalDateTime.now());
        instance.setSubsystemId(devopsStageEnvDto.getSubsystemId());
        instance.setFeatures(Joiner.on(",").join(featureIds));
        instance.setVersionId(opened.getId());
        instance.setMajorVersionId(request.getVersionId());
        buildInstanceRepository.save(instance);
        if (request.getVersionId() != null && request.getVersionId() != 0L) {
            versionService.saveVersionInstance(devopsStageEnvDto.getSubsystemId(), request.getJobId(), buildId, request.getVersionId());
        }
    }

    @Override
    public OnlineDetails online(OnlineRequest request) {
        BuildInstance lastOneByParam = buildInstanceRepository.getLastOneByParam(request.getStageEnvId(), request.getJobId());
        OnlineDetails details = details(lastOneByParam);
        details.setCreateByName(iamRepository.getCurrentUser().getName());
        return details;
    }

    private OnlineDetails details(BuildInstance buildInstance) {
        if (buildInstance == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "当前环境不存在构建实例,请运行流水线后重试");
        }
        DevopsSubSystem devopsSubSystem = subsystemRepository.getById(buildInstance.getSubsystemId());
        OnlineDetails details = new OnlineDetails();
        // 获取特性列表
        if (StringUtils.isNotBlank(buildInstance.getFeatures())) {
            List<String> strings = Splitter.on(",").splitToList(buildInstance.getFeatures());
            List<Long> featureIds = strings.stream().map(id -> Long.parseLong(id)).collect(Collectors.toList());
            List<FeatureTaskDTO> features = featureService.listFeatureTask(featureIds);
            details.setFeatures(features);
        }
        // checklist
        List<CheckListDto> checkList = getCheckList(buildInstance);
        details.setCheckLists(checkList);

        // details基础信息
        details.setSubsystemId(buildInstance.getSubsystemId());
        details.setSubsystemName(devopsSubSystem.getFullNameCn());
        details.setBuildInstanceId(buildInstance.getId());
        return details;
    }

    private List<CheckListDto> getCheckList(BuildInstance buildInstance) {

        // todo 先写死，后续考虑拓展
        List<CheckListDto> checkList = new ArrayList<>();

        //
        CheckListDto version = new CheckListDto();
        version.setStatus(SUSS);
        version.setName("版本测试通过");
        checkList.add(version);

        CheckListDto code = new CheckListDto();
        code.setStatus(SUSS);
        code.setName("安全检查通过");
        checkList.add(code);

        CheckListDto codeScan = getCodeScanChecklist(buildInstance);
        checkList.add(codeScan);

        CheckListDto test = new CheckListDto();
        test.setStatus(SUSS);
        test.setName("单元测试通过");
        checkList.add(test);

        return checkList;
    }

    private CheckListDto getCodeScanChecklist(BuildInstance buildInstance) {
        BuildDetailDto recentBuildByBuildId = jobRepository.getRecentBuildByBuildId(buildInstance.getJobId(), buildInstance.getBuildId());
        String gitBranch = recentBuildByBuildId.getGitBranch();
        Integer scmIdBySystemId = codeRepoService.getScmIdBySystemId(buildInstance.getSubsystemId());
        ScanIssueWithHistoryDetailVO scanIssueWithHistoryDetailVO = codeProjectRepository.scanIssues(scmIdBySystemId, gitBranch);

        CheckListDto codeScan = new CheckListDto();
        if (scanIssueWithHistoryDetailVO == null || scanIssueWithHistoryDetailVO.getHistory() == null || scanIssueWithHistoryDetailVO.getHistory().getId() == null) {
            codeScan.setStatus(NOT_DETECTED);
            codeScan.setName("未扫描");
            return codeScan;
        }
        int critical = scanIssueWithHistoryDetailVO.getHistory().getCritical();
        int heightError = scanIssueWithHistoryDetailVO.getHistory().getBlocker();
        codeScan.setStatus(SUSS);
        codeScan.setName("代码扫描通过");
        codeScan.setDetails(String.format("错误 %d 个，致命 %d 个", critical, heightError));
        codeScan.setRedirect(String.format(codeScanDetailUrl, scanIssueWithHistoryDetailVO.getServerId(), scanIssueWithHistoryDetailVO.getGitlabId(), scanIssueWithHistoryDetailVO.getHistory().getId()));
        return codeScan;
    }

    private FeatureBranchRequestDto featureBranchRequestDto(QueryFeatureRequest requestVo) {
        //根据传入参数的StageEnvId,获取DevopsStageEnv数据
        DevopsStageEnv stageEnv = stageEnvRepository.getById(requestVo.getStageEnvId());
        //如果查询结果为空，抛出date is not found异常
        if (stageEnv == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND);
        }
        //根据传入参数的StageEnvId,查询并获取featureId集合
        List<Long> featureIds = stageEnvFeatureRepository.selectFeatureList(requestVo.getStageEnvId(), requestVo.getVersionId());
        //创建FeatureBranchRequestDto类的requestDto对象
        FeatureBranchRequestDto requestDto = new FeatureBranchRequestDto();
        //设置相应数据
        requestDto.setFeatureIdsFilter(featureIds);
        requestDto.setProjectId(requestVo.getProjectId());
        requestDto.setSubsystemId(stageEnv.getSubsystemId());
        requestDto.setFeatureName(requestVo.getFeatureName());
        requestDto.setFeatureStatus(requestVo.getFeatureStatus());
        requestDto.setDirector(requestVo.getDirector());
        //根据stageEnv.getStageId设置DevopsStageDto info
        DevopsStageDto info = devopsStageService.info(stageEnv.getStageId());
        //如果查询结果为空，抛出date is not found异常
        if (info == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "阶段数据异常");
        }
        if (CollectionUtils.isNotEmpty(info.getAccessFeatureStatus())){
            requestDto.setFeatureStatusList(info.getAccessFeatureStatus());
        }
        //根据阶段类型，设置requestDto的开发分支名或分支名
        if (info.getType() == DEV) {
            requestDto.setBranchDevName(requestVo.getBranch());
        } else {
            requestDto.setBranchName(requestVo.getBranch());
        }
        return requestDto;
    }

    @Override
    public Page<FeatureTaskDTO> featurePage(QueryFeatureRequest requestVo) {
        //根据参数,设置FeatureBranchRequestDto requestDto对象,并设置分页的页码和每页数据
        FeatureBranchRequestDto requestDto = featureBranchRequestDto(requestVo);
        requestDto.setPageNo(requestVo.getPageNo());
        requestDto.setPageSize(requestVo.getPageSize());
        //调用pageFeatureTask方法获取返回值
        if(CollectionUtils.isEmpty(requestDto.getFeatureStatusList())){
            return new Page<>(requestVo.getPageNo(), requestDto.getPageSize());
        }
        requestDto.setFeatureStatusFilter(Lists.newArrayList(FeatureConstance.FeatureStatus.CLEAR));
        return featureService.pageFeatureTask(requestDto);
    }

    @Override
    public void addIntegration(StageEnvFeatureRequest request) {
        removeIntegration(request);
        stageEnvFeatureRepository.add(request.getStageEnvId(), request.getVersionId(), request.getFeatureIds());
    }

    @Override
    public void removeIntegration(StageEnvFeatureRequest request) {
        stageEnvFeatureRepository.deleteByParam(request.getStageEnvId(), request.getVersionId(), request.getFeatureIds());
    }

    @Override
    public Page<OnlineDto> onlinePage(OnlineQuery request) {

        LambdaQueryWrapper<OnlineOrder> query = new LambdaQueryWrapper<>();
        query.eq(OnlineOrder::getSubsystemId, request.getSubsystemId());
        query.eq(request.getApplicant() != null, OnlineOrder::getCreateBy, request.getApplicant());
        query.eq(request.getOnlineStatus() != null, OnlineOrder::getOnlineStatus, request.getOnlineStatus());
        query.eq(request.getApplyStatus() != null, OnlineOrder::getApplyStatus, request.getApplyStatus());
        query.eq(OnlineOrder::getDelFlag, SystemConstance.NOT_DELETE);
        query.and(StringUtils.isNotEmpty(request.getSearch()),
                wrapper -> wrapper.eq(OnlineOrder::getOnlineName, request.getSearch()).or().eq(OnlineOrder::getOnlineCode, request.getSearch()));
        Page<OnlineOrder> page = new Page<>(request.getPageNo(), request.getPageSize());
        Page<OnlineOrder> record = onlineOrderRepository.page(page, query);
        if (CollectionUtils.isEmpty(record.getRecords())) {
            return PageUtils.exchangeRecordData(record, new ArrayList<>());
        }
        List<Long> userIds = record.getRecords().stream().map(order -> order.getCreateBy()).collect(Collectors.toList());
        Map<Long, User> longUserInfoDtoMap = iamRepository.listUserByIdsForMap(userIds);
        return PageUtils.exchangeRecord(record, order -> {
            OnlineDto onlineDto = developmentMapstruct.onlineDto(order);
            onlineDto.setOnlineTitle(order.getOnlineName());
            onlineDto.setOnlineTime(DateUtils.getScopeString(order.getStartTime(), order.getEndTime()));
            onlineDto.setApplyUserId(order.getCreateBy());
            onlineDto.setApplyUserName(longUserInfoDtoMap.get(order.getCreateBy()).getName());
            return onlineDto;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onlineSave(OnlineCreateRequest request) {

        Long userId = iamRepository.getCurrentUser().getId();
        LocalDateTime now = LocalDateTime.now();
        String fmt = "yyyy-MM-dd HH:mm";
        OnlineOrder record = new OnlineOrder();
        record.setId(request.getId());
        record.setOnlineName(request.getOnlineName());
        record.setStartTime(DateUtils.getTime(request.getOnlineStartTime(), fmt));
        record.setEndTime(DateUtils.getTime(request.getOnlineEndTime(), fmt));
        record.setInfluenceScope(request.getInfluenceScope());
        record.setDescription(request.getDescription());
        record.setApplyStatus(request.getApplyStatus());
        record.setOnlineStatus(0);
        record.setUpdateBy(userId);
        record.setUpdateTime(now);
        if (request.getId() == null) {
            record.setSubsystemId(request.getSubsystemId());
            record.setBuildInstanceId(request.getBuildInstanceId());
            record.setDelFlag(SystemConstance.NOT_DELETE);
            record.setCreateBy(userId);
            record.setCreateTime(now);
        }
        onlineOrderRepository.saveOrUpdate(record);
        if (request.getId() == null) {
            // 保存后更新编码
            onlineOrderRepository.updateCode(record.getId(), String.valueOf(record.getId()));
        }
        onlineOrderSubsystemRepository.saveOrderSystem(record.getId(), request.getSubsystemIds());


    }

    @Override
    public OnlineDetails onlineDetails(Long orderId) {
        OnlineOrder onlineOrder = onlineOrderRepository.getById(orderId);
        BuildInstance buildInstance = buildInstanceRepository.getById(onlineOrder.getBuildInstanceId());
        OnlineDetails details = details(buildInstance);
        details.setId(onlineOrder.getId());
        details.setOnlineName(onlineOrder.getOnlineName());
        details.setInfluenceScope(onlineOrder.getInfluenceScope());
        details.setDescription(onlineOrder.getDescription());
        details.setStartTime(onlineOrder.getStartTime());
        details.setEndTime(onlineOrder.getEndTime());
        details.setCreateBy(onlineOrder.getCreateBy());
        details.setCreateByName(iamRepository.getUserById(onlineOrder.getCreateBy()).getName());
        List<Long> subsystemIdByOrderId = onlineOrderSubsystemRepository.getSubsystemIdByOrderId(orderId);
        details.setSubsystemIds(subsystemIdByOrderId);
        return details;
    }

    @Override
    public List<ProjectDto> onlineIssues(Long buildInstanceId) {
        BuildInstance buildInstance = buildInstanceRepository.getById(buildInstanceId);
        List<Long> featureId = buildInstance.getFeatureId();
        return projectManagementService.groupProjectIssues(featureId);
    }

    @Override
    public ConflictPlanDto conflictPlan(Long buildId) {
        BuildInstance buildInstance = buildInstanceRepository.getByBuildId(buildId);
        if (buildInstance == null) {
            return new ConflictPlanDto();
        }
        MergeTaskDto mergeTaskByTaskId = codeMergeRepository.getMergeTaskByTaskId(buildInstance.getMergeTaskId());
        if (mergeTaskByTaskId.getStatus().equals("BLOCK")) {
            List<MergeTaskBranchDto> branches = mergeTaskByTaskId.getBranches();
            for (MergeTaskBranchDto branch : branches) {
                if ("CONFLICT".equals(branch.getStatus())) {
                    ConflictPlanDto result = new ConflictPlanDto();
                    result.setSourceBranch(branch.getName());
                    result.setTargetBranch(mergeTaskByTaskId.getTargetBranch());
                    result.setId(branch.getId());
                    return result;
                }
            }
        }
        return new ConflictPlanDto();
    }

    @Override
    public void onlineCancel(Long id) {
        onlineOrderRepository.cancel(id);
    }

    @Override
    public void onlineDel(Long id) {
        onlineOrderRepository.deleteLogic(id, false);
    }

    @Override
    public Map getPipelineVar(Long jobId, Long buildId) {
        Long subsystemId = subsystemPipelineRepository.getSubsystemId(jobId);
        if (subsystemId == null) {
            return new HashMap();
        }
        Map var = new HashMap();
        var.put("subsystemId", subsystemId);
        DevopsSubSystem devopsSubSystem = subsystemRepository.getById(subsystemId);
        var.put("systemId", devopsSubSystem.getSystemId());
        var.put("subSystemCode", devopsSubSystem.getSubCode());
        BuildInstance byBuildId = buildInstanceRepository.getByBuildId(buildId);
        if (byBuildId == null) {
            return var;
        }
        DevopsStageEnvDto devopsStageEnvDto = devopsStageService.envInfo(byBuildId.getStageEnvId());
        var.put("stageEnvCode", devopsStageEnvDto.getStageEnvCode());
        // 添加制品库信息
        addRepository(byBuildId, devopsStageEnvDto, var);
        // 添加版本信息
        addProductVersion(byBuildId, devopsStageEnvDto, var);
        // 填充代码仓库信息
        addCodeRepository(byBuildId, devopsStageEnvDto, var);
        // 填充分支合并器参数
        addMergeParams(byBuildId, devopsStageEnvDto, var);
        // 填充部署参数
        if (devopsStageEnvDto.getDeployType() == DevelopmentConstance.EnvDeployType.k8s) {
            var.put("clusterId", devopsStageEnvDto.getClusterId());
            var.put("namespace", devopsStageEnvDto.getNamespace());
        } else if (devopsStageEnvDto.getDeployType() == DevelopmentConstance.EnvDeployType.virtually) {
            var.put("hostId", Joiner.on(",").join(devopsStageEnvDto.getHostId()));
        } else if (devopsStageEnvDto.getDeployType() == DevelopmentConstance.EnvDeployType.host) {
            var.put("hostId", Joiner.on(",").join(devopsStageEnvDto.getHostId()));
        }
        return var;
    }

    private void addRepository(BuildInstance byBuildId, DevopsStageEnvDto devopsStageEnvDto, Map var) {
        if(devopsStageEnvDto.getRawRepoId() != null){
            var.put("rawRepoId", devopsStageEnvDto.getRawRepoId());
        }
        if(devopsStageEnvDto.getContainerRepoId() != null){
            var.put("containerRepoId", devopsStageEnvDto.getContainerRepoId());
        }
    }

    private void addProductVersion(BuildInstance byBuildId, DevopsStageEnvDto devopsStageEnvDto, Map var) {
        if (byBuildId.getVersionId() == null) {
            return;
        }
        VersionManagement byId = versionRepository.getById(byBuildId.getVersionId());
        if (byId == null) {
            return;
        }
        var.put("productVersion", byId.getVersionString());
    }

    private void addCodeRepository(BuildInstance buildInstance, DevopsStageEnvDto devopsStageEnvDto, Map var) {
        Long subsystemId = buildInstance.getSubsystemId();
        GitProjectDto project = codeRepoService.getProject(subsystemId, false);
        if (project == null) {
            return;
        }
        var.put("serverId", project.getServerId());
        var.put("gitlabId", project.getId());
        var.put("gitlabRepo", project.getInternalUrl());
    }

    private void addMergeParams(BuildInstance byBuildId, DevopsStageEnvDto devopsStageEnvDto, Map var) {
        if (StringUtils.isBlank(byBuildId.getFeatures())) {
            return;
        }
        SystemStage systemStageByEnvId = devopsStageService.getSystemStageByEnvId(byBuildId.getStageEnvId());
        List<String> strings = Splitter.on(",").splitToList(byBuildId.getFeatures());
        List<Long> featureIds = strings.stream().map(id -> Long.parseLong(id)).collect(Collectors.toList());
        List<FeatureFeatureBranchDTO> featureBranch = featureService.listFeatureBranch(featureIds, false);
        if (CollectionUtils.isEmpty(featureBranch)) {
            return;
        }
        List<String> sourceBranches = featureBranch.stream().map(fb -> fb.getBranch(systemStageByEnvId.getType())).collect(Collectors.toList());
        Long mergeTaskId = mergeTaskRepository.getTaskIdByParams(byBuildId.getSubsystemId(), systemStageByEnvId.getCode() + "_" + devopsStageEnvDto.getId());
        if (mergeTaskId == null) {
            mergeTaskId = IdWorker.getId();
        }
        String groupId = mergeTaskId.toString();
        String targetBranch = systemStageByEnvId.getCode() + "_" + byBuildId.getStageEnvId();
        if(StringUtils.isNotEmpty(devopsStageEnvDto.getStageEnvCode())){
            // 2.2版本以后的数据才有stageEnvCode编码
            targetBranch = systemStageByEnvId.getCode() + "_" + devopsStageEnvDto.getStageEnvCode();
        }
        String isTargetDynamic = Boolean.TRUE.toString();
        var.put("sourceBranches", Joiner.on(",").join(sourceBranches));
        var.put("groupId", groupId);
        var.put("targetBranch", targetBranch);
        var.put("isTargetDynamic", isTargetDynamic);

    }

    @Override
    public BuildDetailDto pipelineBuildInfo(Long stageEnvId, Long jobId, Long versionId) {
        BuildInstance buildInstance = buildInstanceRepository.getLastOneByParam(stageEnvId, jobId, versionId);
        Long buildId = null;
        if (buildInstance != null) {
            buildId = buildInstance.getBuildId();
        }
        return jobRepository.getRecentBuildByBuildId(jobId, buildId);
    }

    @Override
    @Transactional
    public void notifyResult(Long buildId, PipelineNotifyReq notifyReq) {

        //更新制品元数据
        BuildInstance buildInstance = buildInstanceRepository.getByBuildId(buildId);
        updateProductMetadata(buildId, notifyReq, buildInstance);

        // 不在研发工作台构建的研发协同流水线
        CreateInstanceReq createInstanceReq = new CreateInstanceReq();
        ProductPageQuery productPageQuery = new ProductPageQuery();
        productPageQuery.setBuildId(buildId);
        List<DevopsProductMetadataDto> products = productRepository.list(productPageQuery);
        for (DevopsProductMetadataDto product : products) {
            if (notifyReq.getSubSystemId() == null){
                continue;
            }
            String totalVersionNumber = product.getVersion();
            VersionManagement versionManagement = versionRepository.getByParams(totalVersionNumber , notifyReq.getSubSystemId());
            if(versionManagement == null){
                continue;
            }
            DevopsSubSystem devopsSubSystem = subsystemRepository.getById(notifyReq.getSubSystemId());
            Long systemId = devopsSubSystem.getSystemId();
            Long productId = product.getId();
            createInstanceReq.setProductId(productId);
            createInstanceReq.setSystemId(systemId);
            productRepository.createInstance(createInstanceReq);
            VersionComponents versionComponent = new VersionComponents();
            versionComponent.setComponent(SystemConstance.VersionComponent.PRODUCT);
            versionComponent.setVersionId(versionManagement.getId());
            versionComponent.setComponentKey(productId.toString());
            versionComponent.setCreateTime(LocalDateTime.now());
            versionComponent.setCreateBy(0L);
            versionComponent.setSubsystemId(devopsSubSystem.getId());
            versionComponent.setCreateBy(product.getCreateBy());
            versionComponentsRepository.save(versionComponent);
            if(buildInstance != null && CollectionUtils.isNotEmpty(buildInstance.getFeatureId())){
                // 本次构建存在合并特性
                List<Long> featureIds = versionComponentsRepository.listFeatureIdByParams(versionManagement.getId());
                List<Long> filterFeatureIds = buildInstance.getFeatureId().stream().filter(id -> !featureIds.contains(id)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(filterFeatureIds)){
                    List<VersionComponents> featureComponents = versionService.getFeatureComponents(versionManagement, filterFeatureIds, null);
                    versionComponentsRepository.saveBatch(featureComponents);
                }
            }
        }


    }

    private void updateProductMetadata(Long buildId, PipelineNotifyReq notifyReq, BuildInstance buildInstance) {

        Long subsystemId = notifyReq.getSubSystemId();
        DevopsSubSystem subsystem = subsystemRepository.getById(subsystemId);
        DevopsSystem devopsSystem = devopsSystemRepository.getById(subsystem.getSystemId());

        ProductMetadataDto productMetadataDto = new ProductMetadataDto();
        productMetadataDto.setBuildId(buildId);
        List<DevopsFeatureDto> featureDtoList = null;
        List<IssuesDTO> issuesDTOList = null;
        if(buildInstance != null){
            List<Long> featureIds = buildInstance.getFeatureId();
            List<Long> taskIds = featureTaskRepository.listByFeatureId(featureIds).stream().map(ft -> Long.parseLong(ft.getTaskKey())).collect(Collectors.toList());
            if(devopsSystem.getTenantId() != null) {
                TenantContextHolder.setTenantId(devopsSystem.getTenantId().toString());
            }
           //todo
          //  issuesDTOList = projectManagementService.getCustomIssuesByFeatureIds(featureIds);
         //   List<String> issuesDtos =issuesDTOList.stream().map(issues -> issues.getTitle()).collect(Collectors.toList()); ;
            FeatureListRequest request = new FeatureListRequest();
            request.setIds(featureIds);
            featureDtoList = featureService.list(request);
            List<FeatureFeatureBranchDTO> featureBranch = featureService.listFeatureBranchDto(featureDtoList , false);
            List<String> featureName = new ArrayList<>();
            for (FeatureFeatureBranchDTO branch : featureBranch) {
                featureName.add(branch.getFeatureName());
            }
            productMetadataDto.setFeatures(Joiner.on(",").join(featureName));
            productMetadataDto.setFeatureBranches(buildInstance.getSourceBranches());
            productMetadataDto.setFeatureIds(Joiner.on(",").join(featureIds));
            productMetadataDto.setIssuesIds(Joiner.on(",").join(taskIds));
        //    productMetadataDto.setIssuesNames(Joiner.on(",").join(issuesDtos));
        }
        List<ProductMetadataInfoDto> productMetadataInfoDtoList = getproductMetadataInfoDtoList(buildId, subsystem, devopsSystem, featureDtoList, issuesDTOList);
        productMetadataDto.setMetadataInfos(productMetadataInfoDtoList);
        productRepository.updateMetadataByBuildId(productMetadataDto);
    }

    private List<ProductMetadataInfoDto> getproductMetadataInfoDtoList(Long buildId, DevopsSubSystem subsystem, DevopsSystem devopsSystem,List<DevopsFeatureDto> featureDtoList,List<IssuesDTO> issuesDTOList) {
        List<ProductMetadataInfoDto> productMetadataInfoDtoList = new ArrayList<>();
        ProductMetadataInfoDto productSource = new ProductMetadataInfoDto();
        productSource.setBuildId(buildId);
        productSource.setProductMetadataType("productSource");
        Map<String,Object> productsourcemap = new HashMap<>();
        productsourcemap.put("systemId", devopsSystem.getId());
        productsourcemap.put("systemName", devopsSystem.getSubFullNameCn());
        productsourcemap.put("subsystem", subsystem.getId());
        productsourcemap.put("subsystemName", subsystem.getFullNameCn());
        productSource.setUpdateInfo(productsourcemap);
        productMetadataInfoDtoList.add(productSource);

        if(CollectionUtils.isNotEmpty(featureDtoList)){
            ProductMetadataInfoDto features = new ProductMetadataInfoDto();
            features.setBuildId(buildId);
            features.setProductMetadataType("features");
            Map<String,Object> featuresMap = new HashMap<>();
            featuresMap.put("data",featureDtoList);
            features.setUpdateInfo(featuresMap);
            productMetadataInfoDtoList.add(features);
        }

        if(CollectionUtils.isNotEmpty(issuesDTOList)){
            ProductMetadataInfoDto issues = new ProductMetadataInfoDto();
            Map<String,Object> issuesMap = new HashMap<>();
            issuesMap.put("data",issuesDTOList);
            issues.setBuildId(buildId);
            issues.setProductMetadataType("issues");
            issues.setUpdateInfo(issuesMap);
            productMetadataInfoDtoList.add(issues);
        }
        return  productMetadataInfoDtoList;
    }


    @Override
    public void mergeNotify(PipelineMergeNotify notify) {
        if(notify.getSubSystemId() == null){
            return ;
        }
        BuildInstance buildInstance = buildInstanceRepository.getByBuildId(notify.getBuildId());
        if (buildInstance == null && notify.getSubSystemId() != null) {
            buildInstance = new BuildInstance();
            buildInstance.setBuildId(notify.getBuildId());
            buildInstance.setJobId(notify.getJobId());
            buildInstance.setSubsystemId(notify.getSubSystemId());
            buildInstanceRepository.save(buildInstance);
        }else {
            SystemStage systemStageByEnvId = devopsStageService.getSystemStageByEnvId(buildInstance.getStageEnvId());
            String groupName = systemStageByEnvId.getCode() + "_" + buildInstance.getStageEnvId();
            // 保存阶段的分支合并分组
            codeRepoService.saveMergeTask(buildInstance.getSubsystemId(), groupName, notify.getGroupId(), null);
        }
        // 本地构建的合并信息，实例id  （mergeTaskId）
        List<String> branches = Arrays.asList(notify.getSourceBranches().split(","));
        List<Long> featureIds = featureBranchRepository.listByBranches(buildInstance.getSubsystemId(), branches).stream().map(fb -> fb.getFeatureId()).collect(Collectors.toList());
        buildInstanceRepository.saveMergeTask(buildInstance.getId(), notify.getGroupId(), notify.getTaskId(), featureIds, branches);
    }

    @Override
    public List<JenkinsFileStartParameter> runStartParams(Long jobId, Long stageEnvId, Long marjoVersionId, Boolean lastFlag) {

        List<JenkinsFileStartParameter> runStartParams = jobRepository.getRunStartParams(jobId, lastFlag);
        DevopsStageEnvDto devopsStageEnvDto = devopsStageService.envInfo(stageEnvId);
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(devopsStageEnvDto.getSubsystemId());

        RunStartParamsContext context = new RunStartParamsContext(devopsSubsystem, devopsStageEnvDto, runStartParams);
        if(marjoVersionId != null){
            VersionManagement opened = versionRepository.getOpened(marjoVersionId);
            if(opened == null){
                throw new SystemException(ExceptionCode.PROMPT, "当前版本无开启的子版本，请联系管理员处理");
            }
            context.setVersionManagement(opened);
        }
        List<JenkinsFileStartParameter> allParams = addAllRunStartParams(context);
        for (JenkinsFileStartParameter startParameter : allParams) {
            RunStartParamsHandler handler = runStartParamsFactory.getHandler(startParameter.getParamName());
            if (handler == null){
                continue;
            }
            RunStartParamsEnum params = handler.getParams();
            if (params.getUpdateFLag() != null){
                startParameter.setUpdateFlag(params.getUpdateFLag());
            }
            if (params.getShowFlag() != null){
                startParameter.setShowFlag(params.getShowFlag());
            }
            handler.setValue(startParameter, context);
        }
        List<SystemDict> allDict = systemDictRepository.getAllDict(false).getOrDefault(SystemConstance.SystemDictSubject.FILTER_START_PARAM, new ArrayList<>());
        List<String> collect = allDict.stream().map(dict -> dict.getDictCode()).collect(Collectors.toList());
        return runStartParams.stream().filter(p -> !collect.contains(p.getParamName())).collect(Collectors.toList());
    }

    private List<JenkinsFileStartParameter> addAllRunStartParams(RunStartParamsContext context) {
        Map<String, RunStartParamsHandler> developmentHandler = runStartParamsFactory.getDevelopmentHandler();
        List<JenkinsFileStartParameter> runStartParams = context.getRunStartParams();
        if (CollectionUtils.isEmpty(developmentHandler.keySet())){
            return runStartParams;
        }
        Map<String, JenkinsFileStartParameter> collect = runStartParams.stream().collect(Collectors.toMap(JenkinsFileStartParameter::getParamName, p -> p));
        for (String key : developmentHandler.keySet()) {
            RunStartParamsHandler handler = developmentHandler.get(key);
            if(!collect.containsKey(handler.getParams().getParamsName()) && handler.condition(context)){
                runStartParams.add(handler.getDefaultModel());
            }
        }
        return runStartParams;
    }

    @Override
    public void pipelineReplay(PipelineReplayRequest request) {
        DevopsStageEnvDto devopsStageEnvDto = devopsStageService.envInfo(request.getStageEnvId());
        User currentUser = iamRepository.getCurrentUser();
        Long buildId = jobRepository.replayJob(request.getBuildId(), request.getRunDescribe());
        BuildInstance last = buildInstanceRepository.getByBuildId(request.getBuildId());
        BuildInstance instance = new BuildInstance();
        instance.setJobId(last.getJobId());
        instance.setBuildId(buildId);
        instance.setCreateBy(currentUser.getId());
        instance.setStageEnvId(request.getStageEnvId());
        instance.setCreateTime(LocalDateTime.now());
        instance.setSubsystemId(devopsStageEnvDto.getSubsystemId());
        instance.setFeatures(last.getFeatures());
        instance.setVersionId(last.getVersionId());
        instance.setMajorVersionId(last.getMajorVersionId());
        buildInstanceRepository.save(instance);

    }

    @Override
    public BuildInstanceDto lastBuildInstance(Long stageEnvId, Long maJorVersionId) {
        BuildInstance lastOneByParamOrNull = buildInstanceRepository.getLastOneByParamOrNull(stageEnvId, maJorVersionId);
        if(lastOneByParamOrNull == null){
            return new BuildInstanceDto();
        }
        BuildInstanceDto buildInstanceDto = developmentMapstruct.toBuildInstanceDto(lastOneByParamOrNull);
        if(lastOneByParamOrNull.getVersionId() != null){
            Long majorVersionId = versionRepository.getById(lastOneByParamOrNull.getVersionId()).getMajorVersionId();
            buildInstanceDto.setVersionId(majorVersionId);
        }
        return buildInstanceDto;
    }

    @Override
    public VersionDto checkSubversion(PipelineProductBuildReq req) {
        List<VersionManagement> versionManagements = versionRepository.listByStatus(req.getSubSystemId(), req.getVersionNumber(), 0);
        if(CollectionUtils.isEmpty(versionManagements)){
            throw new SystemException(ExceptionCode.PROMPT, "当前版本没有开启的子版本号");
        }
        VersionManagement versionManagement = versionManagements.get(0);
        Long versionId = versionManagement.getId();
        List<VersionComponents> versionComponents = versionComponentsRepository.listByParams(versionId, SystemConstance.VersionComponent.PRODUCT, null);
        if(CollectionUtils.isEmpty(versionComponents)){
            return versionMapstruct.toVersionDto(versionManagement);
        }
        List<Long> productIds = versionComponents.stream().map(vc -> Long.parseLong(vc.getComponentKey())).collect(Collectors.toList());
        List<DevopsProductMetadataDto> devopsProductMetadataDtos = productRepository.listByIds(productIds);
        DevopsSubSystem subsystem = subsystemRepository.getById(req.getSubSystemId());
        for (DevopsProductMetadataDto devopsProductMetadataDto : devopsProductMetadataDtos) {
            if(devopsProductMetadataDto.getRepoId().equals(req.getRepoId())){
                if(!checkProductExist(devopsProductMetadataDto, req, versionManagement)){
                    // 不存在同名制品
                    continue;
                }
                // 存在晋级策略
                if(req.getOverridePolicy() == 2){
                    // 不覆盖，直接生成新版本
                    VersionIterationRequest versionIterationRequest = new VersionIterationRequest();
                    versionIterationRequest.setSubVersionId(versionManagement.getId());
                    versionIterationRequest.setUserId(0L);
                    return versionMapstruct.toVersionDto(versionService.versionIteration(versionIterationRequest));
                }
                // 查询制品是否晋级
                ListInstanceReq listInstanceReq = new ListInstanceReq();
                listInstanceReq.setSystemId(subsystem.getSystemId());
                listInstanceReq.setProductIdList(Lists.newArrayList(devopsProductMetadataDto.getId()));
                List<PromotionInstanceDto> promotionInstanceDtos = productRepository.listPromotionInstance(listInstanceReq);
                if(CollectionUtils.isNotEmpty(promotionInstanceDtos) && promotionInstanceDtos.get(0).getPromotionNodeInstanceList().get(0).getPromotionStatus() == 3){
                    // 已晋级
                    // 不覆盖，直接生成新版本
                    VersionIterationRequest versionIterationRequest = new VersionIterationRequest();
                    versionIterationRequest.setSubVersionId(versionManagement.getId());
                    versionIterationRequest.setUserId(0L);
                    return versionMapstruct.toVersionDto(versionService.versionIteration(versionIterationRequest));
                }
                // 放回源对象
                return versionMapstruct.toVersionDto(versionManagements.get(0));
            }
        }
        // 不存在同名
        return versionMapstruct.toVersionDto(versionManagements.get(0));
    }

    private boolean checkProductExist(DevopsProductMetadataDto devopsProductMetadataDto, PipelineProductBuildReq req, VersionManagement versionManagement) {
        if(!devopsProductMetadataDto.getRepoId().equals(req.getRepoId())){
            return false;
        }
        if(devopsProductMetadataDto.getFormat().equals("docker")){
            return devopsProductMetadataDto.getName().equals(req.getProductName()) && devopsProductMetadataDto.getProductVersion().equals(versionManagement.getTotalVersionNumber());
        }else if (devopsProductMetadataDto.getFormat().equals("raw")){
            Map<String, String> param = new HashMap<>();
            param.put("version", versionManagement.getTotalVersionNumber());
            String productName = req.getProductName();
            StringSubstitutor ss = new StringSubstitutor(param, "{" , "}");
            return ss.replace(productName).equals(devopsProductMetadataDto.getName()) && devopsProductMetadataDto.getVersion().equals(versionManagement.getTotalVersionNumber());
        }
        return true;
    }

    private String getTargetBranch(SystemStage systemStage, DevopsStageEnvDto devopsStageEnvDto) {

        return systemStage.getCode() + "_" + devopsStageEnvDto.getId();
//        // todo 临时方案，先写死
//        switch (systemStage.getType()) {
//            case DEV:
//                return "dev" + devopsStageEnvDto.get;
//            case TEST:
//                return "test";
//            case MAIN:
//                return "bate";
//            default:
//                return "";
//        }
    }


    private List<JenkinsFileStartParameter> getStartParam(PipelineBuildRequest request, DevopsStageEnvDto devopsStageEnvDto) {
        List<JenkinsFileStartParameter> result = request.getStartParams();
        Map<String, JenkinsFileStartParameter> mapParams = result.stream().collect(Collectors.toMap(JenkinsFileStartParameter::getParamName, p -> p));
        VersionManagement versionManagement = null;
        if(request.getVersionId() != null){
            versionManagement = versionRepository.getOpened(request.getVersionId());
        }
        Map<String, RunStartParamsHandler> runBuildHandler = runStartParamsFactory.getRunBuildHandler();
        RunStartParamsContext context = new RunStartParamsContext(versionManagement);
        context.setDevopsStageEnvDto(devopsStageEnvDto);
        context.setRunStartParams(result);
        for (String paramName : runBuildHandler.keySet()) {
            RunStartParamsHandler handler = runBuildHandler.get(paramName);
            JenkinsFileStartParameter defaultModel = null;
            if(!mapParams.containsKey(paramName)){
                defaultModel = handler.getDefaultModel();
                result.add(defaultModel);
            }else {
                defaultModel = mapParams.get(paramName);
            }
            handler.setRunValue(defaultModel, context);
        }
        return result;
    }
}
