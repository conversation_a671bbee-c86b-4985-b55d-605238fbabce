package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.pojo.dto.development.BuildInstanceDto;
import cn.harmonycloud.development.pojo.dto.development.OnlineDto;
import cn.harmonycloud.development.pojo.entity.BuildInstance;
import cn.harmonycloud.development.pojo.entity.OnlineOrder;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface DevelopmentMapstruct {
    OnlineDto onlineDto(OnlineOrder order);

    BuildInstanceDto toBuildInstanceDto(BuildInstance lastOneByParamOrNull);
}
