package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchDto;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectManagementDto;
import cn.harmonycloud.development.outbound.util.PageUtils;
import cn.harmonycloud.development.pojo.FeatureConstance;
import cn.harmonycloud.development.pojo.IamConstance;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageDto;
import cn.harmonycloud.development.pojo.dto.feature.FeatureFeatureBranchDTO;
import cn.harmonycloud.development.pojo.dto.feature.FeatureTaskDTO;
import cn.harmonycloud.development.pojo.dto.feature.FeatureIssuesDto;
import cn.harmonycloud.development.pojo.entity.*;
import cn.harmonycloud.development.pojo.vo.feature.*;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.service.*;
import cn.harmonycloud.development.service.mapstruct.FeatureMapstruct;
import cn.harmonycloud.enums.*;
import cn.harmonycloud.exception.SystemException;

import cn.harmonycloud.issue.model.dto.v2.IssuesDTO;
import cn.harmonycloud.issue.provider.IssuesProvider;
import cn.harmonycloud.pmp.model.entity.Tag;
import cn.harmonycloud.pmp.model.entity.User;

import cn.harmonycloud.project.model.dto.IssuesQueryDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【feature(特性表)】的数据库操作Service实现
 * @createDate 2022-08-03 11:35:09
 */
@Slf4j
@Service
public class FeatureServiceImpl implements FeatureService {

    @Autowired
    private CodeRepoService codeRepoService;
    @Autowired
    private ProjectManagementService projectManagementService;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private FeatureTaskRepository featureTaskRepository;
    @Autowired
    private DevopsLabelRepository devopsLabelRepository;
    @Autowired
    private FeatureMapstruct featureMapstruct;
    @Autowired
    private IamRepository iamRepository;
    @Autowired
    private IssuesRepository issuesRepository;
    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    private CodeBranchRepository codeBranchRepository;
    @Autowired
    private FeatureBranchRepository featureBranchRepository;
    @Autowired
    private DevopsStageService devopsStageService;
    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private DevopsSystemRepository systemRepository;
    @Autowired
    private FeatureBranchService featureBranchService;
    @Autowired
    private VersionComponentsRepository versionComponentsRepository;
    @Autowired
    private IssuesProvider issuesProvider;
    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    public FeatureDetailsDto getById(Long id) {
        DevopsFeature devopsFeature = featureRepository.getById(id);
        if (devopsFeature == null || devopsFeature.getDelFlag() == 1){
            return null;
        }
        FeatureBranch featureBranch = featureBranchRepository.getByParam(id);
        List<Long> userIds = new ArrayList<>();
        userIds.add(devopsFeature.getCreateBy());
        if (devopsFeature.getDirector() != null) {
            userIds.add(devopsFeature.getDirector());
        }
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(userIds);
        return featureDetailsDtoBuilder(devopsFeature, featureBranch, userMap);
    }

    @Override
    public FeatureDetailsDto getByIdWithGitlab(Long id) {
        FeatureDetailsDto featureDetailsDto = getById(id);
        Integer codeRepoId = codeRepoService.getCodeRepoIdBySystemId(featureDetailsDto.getSubSystemId());
        featureDetailsDto.setGitlabId(codeRepoId);
        return featureDetailsDto;
    }

    private FeatureDetailsDto featureDetailsDtoBuilder(DevopsFeature devopsFeature, FeatureBranch featureBranch, Map<Long, User> userMap) {
        FeatureDetailsDto devopsFeatureDto = featureMapstruct.toFeatureDetailsDto(devopsFeature);
        devopsFeatureDto.setCompleteTime(devopsFeature.getCompleteData());
        devopsFeatureDto.setCreateName(userMap.getOrDefault(devopsFeature.getCreateBy(), new User()).getName());
        if (devopsFeature.getDirector() != null) {
            devopsFeatureDto.setDirectorName(userMap.getOrDefault(devopsFeature.getDirector(), new User()).getName());
            devopsFeatureDto.setDirector(devopsFeature.getDirector());
        }
        DevopsSubSystem subSystemId = subsystemRepository.getById(devopsFeature.getSubSystemId());
        DevopsSystem systemId = systemRepository.getById(subSystemId.getSystemId());
        devopsFeatureDto.setSubSystemName(subSystemId.getFullNameCn());
        devopsFeatureDto.setSystemName(systemId.getSubFullNameCn());
        ProjectManagementDto byId = projectRepository.getById(devopsFeature.getProjectId());
        if (byId != null) {
            devopsFeatureDto.setProjectName(byId.getName());
            devopsFeatureDto.setOrganId(byId.getBelongOrgId());
        }
        if(featureBranch != null){
            devopsFeatureDto.setBranchDevName(featureBranch.getBranchDevName());
            devopsFeatureDto.setBranchName(featureBranch.getBranchName());
            devopsFeatureDto.setSourceRef(featureBranch.getSourceRef());
            devopsFeatureDto.setFeatureBranch(featureMapstruct.toFeatureBranchDTO(featureBranch));
        }
        return devopsFeatureDto;
    }

    @Override
    public Page<DevopsFeatureDto> pageFeature(FeaturePageRequest request) {
        FeaturePageQuery query = featureMapstruct.toFeaturePageQuery(request);
        if (request.getFilterByVersionId() != null){
            List<Long> featureIds = versionComponentsRepository.listFeatureIdByParams(request.getFilterByVersionId());
            query.setIdsFilter(featureIds);
        }
        if (CollectionUtils.isNotEmpty(request.getLabels())){
            List<Long> longs = listFeatureIdByLabel(request.getLabels());
            if (CollectionUtils.isEmpty(longs)){
                return new Page<>(request.getPageNo(), request.getPageSize());
            }
            query.setIds(longs);
        }
        User currentUser = iamRepository.getCurrentUser();
        if (request.getQueryType() != null){
            if (request.getQueryType().equals("1")){
                query.setCreateBy(currentUser.getId());
            }else if (request.getQueryType().equals("2")){
                query.setDirector(currentUser.getId());
            }
        }
        query.setBottomFeatureStatus(Lists.newArrayList(FeatureConstance.FeatureStatus.CLEAR));
        Page<DevopsFeature> page = featureRepository.page(query);
        List<DevopsFeatureDto> collect = featureToDto(page.getRecords());
        addDetails(collect, request.isWithBranch(), request.isWithLabel());
        if(request.isWithSubsystemName()){
            addSubsystem(collect);
        }
        if(request.isWithSystemName()){
            addSystem(collect);
        }
        return PageUtils.exchangeRecordData(page, collect);
    }

    private void addSystem(List<DevopsFeatureDto> collect) {
        if (CollectionUtils.isEmpty(collect)){
            return;
        }
        List<Long> ids = collect.stream().map(f -> f.getSystemId()).collect(Collectors.toList());
        Map<Long, DevopsSystem> longDevopsSubSystemMap = systemRepository.listByIds(ids).stream().collect(Collectors.toMap(DevopsSystem::getId, s -> s));
        for (DevopsFeatureDto devopsFeatureDto : collect) {
            DevopsSystem devopsSystem = longDevopsSubSystemMap.get(devopsFeatureDto.getSystemId());
            if(devopsSystem != null){
                devopsFeatureDto.setSystemName(devopsSystem.getSubFullNameCn());
            }
        }
    }

    private void addSubsystem(List<DevopsFeatureDto> collect) {
        if (CollectionUtils.isEmpty(collect)){
            return;
        }
        List<Long> ids = collect.stream().map(f -> f.getSubSystemId()).collect(Collectors.toList());
        Map<Long, DevopsSubSystem> longDevopsSubSystemMap = subsystemRepository.mapByIds(ids);
        for (DevopsFeatureDto devopsFeatureDto : collect) {
            DevopsSubSystem devopsSubSystem = longDevopsSubSystemMap.get(devopsFeatureDto.getSubSystemId());
            if(devopsSubSystem != null){
                devopsFeatureDto.setSubSystemName(devopsSubSystem.getFullNameCn());
            }
        }
    }

    private void addDetails(List<DevopsFeatureDto> collect, boolean withBranch, boolean withLabel) {
        if (CollectionUtils.isEmpty(collect)){
            return;
        }
        List<Long> ids = collect.stream().map(f -> f.getId()).collect(Collectors.toList());
        Map<Long, List<Tag>> longListMap = new HashMap<>();
        Map<Long, FeatureBranch> featureBranchMap = new HashMap<>();
        if (withLabel){
            longListMap = devopsLabelRepository.mapLabelByInstanceId(DevopsLabelEnum.FEATURE.getClassificationCode(), ids);
        }
        if (withBranch){
            List<FeatureBranch> featureBranches = featureBranchRepository.listByParam(ids);
            featureBranchMap = featureBranches.stream().collect(Collectors.toMap(FeatureBranch::getFeatureId, f -> f));
        }
        Integer codeRepoId = codeRepoService.getCodeRepoIdBySystemId(collect.get(0).getSubSystemId());
        for (DevopsFeatureDto devopsFeatureDto : collect) {
            devopsFeatureDto.setGitlabId(codeRepoId);
            if (withLabel){
                List<Tag> orDefault = longListMap.get(devopsFeatureDto.getId());
                if(orDefault == null){
                    orDefault = new ArrayList<>();
                }
                devopsFeatureDto.setLabels(orDefault);
            }
            if (withBranch){
                FeatureBranch featureBranch = featureBranchMap.getOrDefault(devopsFeatureDto.getId(), new FeatureBranch());
                devopsFeatureDto.setFeatureBranch(featureBranch.getBranchName());
                devopsFeatureDto.setFeatureDevBranch(featureBranch.getBranchDevName());
                devopsFeatureDto.setSourceRef(featureBranch.getSourceRef());
                devopsFeatureDto.setSourceCommit(featureBranch.getSourceCommit());
            }
        }
    }


    private List<DevopsFeatureDto> featureToDto(List<DevopsFeature> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<Long> directors = records.stream().map(feature -> feature.getDirector()).collect(Collectors.toList());
        List<Long> featureProjectId = records.stream().map(feature -> feature.getProjectId()).collect(Collectors.toList());
        Map<Long, ProjectManagementDto> projectMap = projectRepository.mapBaseProjects(featureProjectId);
        Map<Long, User> userMap = iamRepository.listUserByIdsForMap(directors);
        return records.stream().map(feature -> {
            DevopsFeatureDto devopsFeatureDto = featureMapstruct.toDevopsFeatureDto(feature);
            devopsFeatureDto.setProjectName(projectMap.getOrDefault(feature.getProjectId(), new ProjectManagementDto()).getName());
            devopsFeatureDto.setFeatureStatus(feature.getFeatureStatus().toString());
            devopsFeatureDto.setFeatureType(feature.getFeatureType().toString());
            Long director = feature.getDirector();
            if (director != null) {
                devopsFeatureDto.setDirectorId(director.toString());
                devopsFeatureDto.setDirectorName(userMap.getOrDefault(director, new User()).getName());
            }
            // 兼容1.8数据
            if (StringUtils.isEmpty(feature.getFeatureCode())) {
                devopsFeatureDto.setFeatureCode(feature.getFeatureNumber().toString());
            }
            return devopsFeatureDto;
        }).collect(Collectors.toList());
    }

    public List<FeatureFeatureBranchDTO> listFeatureBranchDto(List<DevopsFeatureDto> listFeature, boolean withBranchStage) {
        if (CollectionUtils.isEmpty(listFeature)) {
            return new ArrayList<>();
        }
        List<Long> featureIds = listFeature.stream().map(feature -> feature.getId()).collect(Collectors.toList());
        List<FeatureBranch> featureBranches = featureBranchRepository.listByParam(featureIds);
        Map<Long, FeatureBranch> featureBranchMap = featureBranches.stream().collect(Collectors.toMap(FeatureBranch::getFeatureId, f -> f));

        Map<String, BranchStageDto> branchStageMap = new HashMap<>();
        String baseBranch = codeRepoService.getBaseBranch();
        if (withBranchStage) {
            Long subSystemId = listFeature.get(0).getSubSystemId();
            Integer codeRepoId = codeRepoService.getScmIdBySystemId(subSystemId);
            List<String> branches = featureBranches.stream().map(fb -> fb.getBranchName()).collect(Collectors.toList());
            List<String> branchDev = featureBranches.stream().map(fb -> fb.getBranchDevName()).collect(Collectors.toList());
            branches.addAll(branchDev);
            branchStageMap = codeBranchRepository.listBranchStage(codeRepoId, branches, baseBranch);
        }
        Map<String, BranchStageDto> finalBranchStageMap = branchStageMap;
        return listFeature.stream().map(feature -> {
            FeatureBranch featureBranch = featureBranchMap.getOrDefault(feature.getId(), new FeatureBranch());
            FeatureFeatureBranchDTO featureFeatureBranchDTO = featureMapstruct.toFeatureBranchDto(feature);
            featureFeatureBranchDTO.setBranchName(featureBranch.getBranchName());
            featureFeatureBranchDTO.setDevBranch(featureBranch.getBranchDevName());
            if (withBranchStage) {
                BranchStageDto branchStage = finalBranchStageMap.getOrDefault(featureFeatureBranchDTO.getBranchName(), new BranchStageDto());
                BranchStageDto devBranchStage = finalBranchStageMap.getOrDefault(featureFeatureBranchDTO.getDevBranch(), new BranchStageDto());
                featureFeatureBranchDTO.setBranchStage(branchStage);
                featureFeatureBranchDTO.setDevBranchStage(devBranchStage);
                featureFeatureBranchDTO.setBaseBranch(baseBranch);
            }
            return featureFeatureBranchDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public List<FeatureFeatureBranchDTO> listFeatureBranch(List<Long> featureIds, boolean withBranchStage) {
        FeatureListQuery query = new FeatureListQuery();
        query.setIds(featureIds);
        List<DevopsFeature> devopsFeatures = featureRepository.list(query);
        List<DevopsFeatureDto> devopsFeatureDtos = featureToDto(devopsFeatures);
        return listFeatureBranchDto(devopsFeatureDtos, withBranchStage);
    }

    @Override
    public List<FeatureFeatureBranchDTO> listFeatureBranch(FeatureBranchRequestDto requestDto) {
        List<Long> featureIds = listFeatureIdByBranchName(requestDto);
        FeatureListQuery featureQuery = featureMapstruct.toFeatureListQuery(requestDto);
        featureQuery.setSubSystemId(requestDto.getSubsystemId());
        featureQuery.setFeatureStatus(requestDto.getFeatureStatus());
        featureQuery.setFeatureName(requestDto.getFeatureName());
        featureQuery.setIds(featureIds);
        featureQuery.setFeatureStatusFilter(requestDto.getFeatureStatusFilter());
        List<DevopsFeature> listFeature = featureRepository.list(featureQuery);
        List<DevopsFeatureDto> devopsFeatures = featureToDto(listFeature);
        return listFeatureBranchDto(devopsFeatures, false);
    }

    private List<Long> listFeatureIdByBranchName(FeatureBranchRequestDto requestDto) {
        // 根据子系统，分支名称查询特性分支信息
        FeatureBranchQuery featureBranchQuery = new FeatureBranchQuery();
        featureBranchQuery.setSubSystemId(requestDto.getSubsystemId());
        featureBranchQuery.addBranch(requestDto.getBranchName());
        featureBranchQuery.addBranchDev(requestDto.getBranchDevName());
        //调用listBranchByQuery方法
        List<FeatureBranch> featureBranches = featureBranchRepository.listBranchByQuery(featureBranchQuery);
        if (CollectionUtils.isEmpty(featureBranches)) {
            return new ArrayList<>();
        }
        //返回featureBranches中由FeatureId构成的集合
        return featureBranches.stream().map(fb -> fb.getFeatureId()).collect(Collectors.toList());
    }

    @Override
    public List<FeatureTaskDTO> listFeatureTask(List<Long> featureIds) {
        List<FeatureFeatureBranchDTO> featureFeatureBranchDTOS = this.listFeatureBranch(featureIds, false);
        return addTask(featureFeatureBranchDTOS);
    }

    private List<FeatureTaskDTO> addTask(List<FeatureFeatureBranchDTO> featureBranches) {
        if (CollectionUtils.isEmpty(featureBranches)) {
            return new ArrayList<>();
        }
        return featureBranches.stream().map(fb -> featureMapstruct.toFeatureTaskDto(fb)).collect(Collectors.toList());
    }

    private List<FeatureIssuesDto> addIssues(List<DevopsFeatureDto> devopsFeatureDtoList) {
        if (CollectionUtils.isEmpty(devopsFeatureDtoList)) {
            return new ArrayList<>();
        }
        List<Long> featureIds = devopsFeatureDtoList.stream().map(fb -> fb.getId()).collect(Collectors.toList());
        Map<Long, List<IssuesDto>> longListMap = projectManagementService.mapIssues(featureIds);
        return devopsFeatureDtoList.stream().map(fb -> {
            FeatureIssuesDto dto = featureMapstruct.FeatureIssuesDto(fb);
            List<IssuesDto> orDefault = longListMap.getOrDefault(fb.getId(), new ArrayList<>());
            dto.setIssues(orDefault);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FeatureTaskDTO> listFeatureTask(FeatureBranchRequestDto requestDto) {
        List<FeatureFeatureBranchDTO> featureBranches = this.listFeatureBranch(requestDto);
        return addTask(featureBranches);
    }

    @Override
    public Page<FeatureTaskDTO> pageFeatureTask(FeatureBranchRequestDto requestDto) {
        //调用listFeatureIdByBranchName方法创造featureIds集合
        List<Long> featureIds = listFeatureIdByBranchName(requestDto);
        if (CollectionUtils.isEmpty(featureIds)) {
            new Page<FeatureTaskDTO>(requestDto.getPageNo(), requestDto.getPageSize());
        }
        //调用toFeaturePageQuery方法，构建query
        FeaturePageQuery query = featureMapstruct.toFeaturePageQuery(requestDto);
        //设置query id为featureIds
        query.setIds(featureIds);
        //调用page方法
        Page<DevopsFeature> page = featureRepository.page(query);
        List<DevopsFeatureDto> devopsFeatureDtos = featureToDto(page.getRecords());
        List<FeatureFeatureBranchDTO> featureBranches = listFeatureBranchDto(devopsFeatureDtos, false);
        List<FeatureTaskDTO> featureTasks = addTask(featureBranches);
        return PageUtils.exchangeRecordData(page, featureTasks);
    }


    @Override
    public List<WorkItem> task(Long featureId) {
        List<FeatureTask> featureTasks = listTaskByFeatureId(featureId);
        if (CollectionUtils.isEmpty(featureTasks)) {
            return new ArrayList<>();
        }
        List<Long> ids = featureTasks.stream().map(fw -> Long.parseLong(fw.getTaskKey())).collect(Collectors.toList());
        List<IssuesDto> projectTaskDtos = issuesRepository.listTask(ids);
        return Optional.ofNullable(projectTaskDtos).orElse(new ArrayList<>())
                .stream().map(issues -> FeatureMapstruct.MAPSTRUCT.issuesToWorkItem(issues)).collect(Collectors.toList());
    }

    @Override
    public Page<WorkItem> taskPage(FeatureTaskQueryReq req) {
        List<Long> ids = new ArrayList<>();
        if (req.getFeatureId()!=null) {
            List<FeatureTask> featureTasks = listTaskByFeatureId(req.getFeatureId());
            ids = featureTasks.stream().map(fw -> Long.parseLong(fw.getTaskKey())).collect(Collectors.toList());
        }
        Page<IssuesDto> projectTaskDtoPage = issuesRepository.pageTask(req.getProjectId(), req.getTitle(),req.getSprintId(), req.getVersionId(), req.getIssuesTypeId(), req.getStatusId(), req.getPriorityId(), req.getPrincipalIdList(), ids, req.getPageNo(), req.getPageSize());
        return PageUtils.exchangeRecord(projectTaskDtoPage, issues -> FeatureMapstruct.MAPSTRUCT.issuesToWorkItem(issues));
    }

    @Override
    public void addTask(FeatureTaskAddReq req) {
        if (CollectionUtils.isEmpty(req.getTaskIds())) {
            return;
        }
        List<FeatureTask> featureTasks = Optional.ofNullable(listTaskByFeatureId(req.getFeatureId())).orElse(new ArrayList<>());
        FeatureDetailsDto feature = getById(req.getFeatureId());
        Map<String, FeatureTask> collect = featureTasks.stream().collect(Collectors.toMap(FeatureTask::getTaskKey, f -> f));
        List<FeatureTask> featureTaskList = req.getTaskIds().stream()
                .filter(task -> !collect.containsKey(task.toString()))
                .map(task -> {
                    FeatureTask featureTask = new FeatureTask();
                    featureTask.setFeatureId(req.getFeatureId());
                    featureTask.setSubSystemId(feature.getSubSystemId());
                    featureTask.setTaskKey(task.toString());
                    return featureTask;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(featureTaskList)) {
            featureTaskRepository.saveBatch(featureTaskList);
        }
    }

    @Override
    public void delTask(FeatureTaskDelReq req) {
        LambdaUpdateWrapper<FeatureTask> delete = new LambdaUpdateWrapper<>();
        delete.eq(FeatureTask::getFeatureId, req.getFeatureId());
        delete.eq(FeatureTask::getTaskKey, req.getTaskId().toString());
        featureTaskRepository.remove(delete);
    }

    /**
     * 特性编码生成规则
     * 特性FT00000001、缺陷FB000000001
     *
     * @param featureId
     * @param type
     * @return
     */
    @Override
    public String getFeatureCode(Long featureId, Integer type) {
        String number = featureId.toString();
        while (number.length() < 9) {
            number = "0" + number;
        }
        switch (type) {
            case 0:
                return "FT" + number;
            case 1:
                return "FB" + number;
        }
        return featureId.toString();
    }

    @Override
    public List<Long> listFeatureIdByLabel(List<Long> labelId) {
        if (CollectionUtils.isEmpty(labelId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DevopsLabel> query = new LambdaQueryWrapper<>();
        query.in(DevopsLabel::getLabelId, labelId);
        query.eq(DevopsLabel::getClassificationCode, DevopsLabelEnum.FEATURE.getClassificationCode());
        List<DevopsLabel> devopsLabels = Optional.ofNullable(devopsLabelRepository.list(query)).orElse(new ArrayList<>());
        return devopsLabels.stream().map(f -> f.getInstanceId()).collect(Collectors.toList());
    }

    @Override
    public void featureTag(FeatureTagRequest request) {
        if (CollectionUtils.isEmpty(request.getLabelIds())) {
            return;
        }
        LambdaQueryWrapper<DevopsLabel> query = new LambdaQueryWrapper<>();
        query.eq(DevopsLabel::getClassificationCode, DevopsLabelEnum.FEATURE.getClassificationCode());
        query.eq(DevopsLabel::getInstanceId, request.getFeatureId());
        query.in(DevopsLabel::getLabelId, request.getLabelIds());
        List<DevopsLabel> devopsLabels = devopsLabelRepository.list(query);
        if (CollectionUtils.isNotEmpty(devopsLabels)) {
            for (DevopsLabel devopsLabel : devopsLabels) {
                request.getLabelIds().remove(devopsLabel.getLabelId());
            }
        }
        if (CollectionUtils.isEmpty(request.getLabelIds())) {
            return;
        }
        List<DevopsLabel> collect = request.getLabelIds().stream().map(labelId -> {
            DevopsLabel devopsLabel = new DevopsLabel();
            devopsLabel.setInstanceId(request.getFeatureId());
            devopsLabel.setLabelId(labelId);
            devopsLabel.setClassificationCode(DevopsLabelEnum.FEATURE.getClassificationCode());
            return devopsLabel;
        }).collect(Collectors.toList());
        devopsLabelRepository.saveBatch(collect);
    }

    @Override
    public List<DevopsFeatureDto> list(FeatureListRequest request) {
        FeatureListQuery featureListQuery = featureMapstruct.toFeatureListQuery(request);
        if (CollectionUtils.isNotEmpty(request.getLabels())){
            List<Long> longs = listFeatureIdByLabel(request.getLabels());
            if (CollectionUtils.isEmpty(longs)){
                return new ArrayList<>();
            }
            if(CollectionUtils.isEmpty(featureListQuery.getIds())){
                featureListQuery.setIds(longs);
            }else {
                featureListQuery.getIds().addAll(longs.stream().filter(id -> !featureListQuery.getIds().contains(id)).collect(Collectors.toList()));
            }
        }
        List<DevopsFeature> list = featureRepository.list(featureListQuery);
        return featureToDto(list);
    }


    @Override
    public void updateStatus(Long featureId, Integer devSus) {
        User currentUser = iamRepository.getCurrentUser();
        DevopsFeature devopsFeature = new DevopsFeature();
        devopsFeature.setId(featureId);
        devopsFeature.setFeatureStatus(devSus);
        devopsFeature.setUpdateBy(currentUser.getId());
        devopsFeature.setUpdateTime(LocalDateTime.now());
        featureRepository.updateById(devopsFeature);
    }

    @Override
    public Map<Long, BranchStageDto> mapBranchStage(List<Long> ids, Long stageId) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<DevopsFeature> devopsFeatures = featureRepository.listByIds(ids);
        Map<Long, List<DevopsFeature>> groupBySub = devopsFeatures.stream().collect(Collectors.groupingBy(DevopsFeature::getSubSystemId));
        Map<Long, BranchStageDto> result = new HashMap<>();
        for (Long subId : groupBySub.keySet()) {
            List<DevopsFeature> subFeature = groupBySub.get(subId);
            List<Long> collect = subFeature.stream().map(fb -> fb.getId()).collect(Collectors.toList());
            List<FeatureBranch> featureBranches = featureBranchRepository.listByParam(collect).stream().filter(fb -> !fb.getIsClear()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(featureBranches)) {
                result.putAll(defaultMapBranchStage(collect));
                continue;
            }
            String baseBranch = codeRepoService.getBaseBranch();
            Long subSystemId = featureBranches.get(0).getSubSystemId();
            Integer codeRepoId = codeRepoService.getCodeRepoIdBySystemId(subSystemId);
            if (codeRepoId == null){
                result.putAll(defaultMapBranchStage(collect));
                continue;
            }
            List<String> branches = featureBranches.stream().map(fb -> fb.getBranchName()).collect(Collectors.toList());
            DevopsStageDto systemStage = null;
            if (stageId != null) {
                systemStage = devopsStageService.info(stageId);
            }
            if (systemStage != null && systemStage.getType() == DevelopmentConstance.StageType.DEV) {
                branches = featureBranches.stream().filter(fb -> StringUtils.isNotEmpty(fb.getBranchDevName())).map(fb -> fb.getBranchDevName()).collect(Collectors.toList());
            }
            Map<String, BranchStageDto> branchStageMap = codeBranchRepository.listBranchStage(codeRepoId, branches, baseBranch);
            Map<Long, FeatureBranch> fbMap = featureBranches.stream().collect(Collectors.toMap(FeatureBranch::getFeatureId, fb -> fb));
            for (Long id : collect) {
                FeatureBranch featureBranch = fbMap.get(id);
                if (featureBranch == null) {
                    result.put(id, new BranchStageDto());
                    continue;
                }
                String branch = featureBranch.getBranchName();
                if (systemStage != null && systemStage.getType() == DevelopmentConstance.StageType.DEV) {
                    branch = featureBranch.getBranchDevName();
                }
                result.put(id, branchStageMap.getOrDefault(branch, new BranchStageDto()));
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
   public DevopsFeature create(FeatureCreateRequest request) {
        checkCreateRequestParam(request);
        User currentUser = iamRepository.getCurrentUser();
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(request.getSubsystemId());
        checkSubsystemPermission(devopsSubsystem);
        DevopsFeature devopsFeature = savaLocal(request, devopsSubsystem, currentUser);
        featureBranchService.create(devopsFeature, request.getSourceBranch(),null);
        // 保存特性关联任务
        FeatureTaskAddReq req = new FeatureTaskAddReq();
        req.setFeatureId(devopsFeature.getId());
        req.setTaskIds(request.getTaskIds());
        this.addTask(req);
        // 保存标签数据
        devopsLabelRepository.removeAndSave(DevopsLabelEnum.FEATURE.getClassificationCode(), devopsFeature.getId(), request.getLabels());

        return devopsFeature;
    }

    @Override
    public void update(FeatureUpdateRequest request) {
        User currentUser = iamRepository.getCurrentUser();
        DevopsFeature oldFeature = featureRepository.getById(request.getId());
        if(request.getFeatureStatus() != null && oldFeature.getFeatureStatus() == FeatureStatusEnum.CLEAR.getStatus()){
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "当前状态无法更新");
        }
        DevopsFeature devopsFeature = featureMapstruct.toDevopsFeature(request);
        devopsFeature.setUpdateBy(currentUser.getId());
        devopsFeature.setUpdateTime(LocalDateTime.now());
        featureRepository.updateById(devopsFeature);
    }

    @Override
    public void delete(Long id) {
        User currentUser = iamRepository.getCurrentUser();
        FeatureBranch featureBranch = featureBranchRepository.getByParam(id);
        if(featureBranch != null){
            Integer codeRepoIdBySystemId = codeRepoService.getCodeRepoIdBySystemId(featureBranch.getSubSystemId());
            if(codeRepoIdBySystemId != null){
                codeBranchRepository.removeBranch(codeRepoIdBySystemId, featureBranch.getBranchDevName());
                codeBranchRepository.removeBranch(codeRepoIdBySystemId, featureBranch.getBranchName());
            }
            featureBranchRepository.removeById(featureBranch.getId(), true);
        }
        try {
            deleteFeatureIssue(id);
        } catch (Exception e) {
            log.error("删除关联事项失败, featureId: {}", id, e);
        }
        featureRepository.removeLogic(id, true, currentUser);
    }

    private void deleteFeatureIssue(Long featureId) {
        IssuesQueryDTO issuesQueryDTO = new IssuesQueryDTO();
        Map<String, Object> search = new HashMap<>();
        search.put("devopsFeatureId", featureId);
        //todo
//        issuesQueryDTO.setSearch(search);
//        issuesQueryDTO.setIssuesTypeCode("func_feature");
//        List<IssuesDTO> issuesList = issuesRepository.listIssues(issuesQueryDTO);
//        if(CollectionUtils.isNotEmpty(issuesList)) {
//            issuesList.stream().forEach(issuesDTO -> {
//                issuesRepository.deleteIssues(issuesDTO.getId());
//
//            });
//        }
    }

    @Transactional(rollbackFor = Exception.class)
    public DevopsFeature savaLocal(FeatureCreateRequest request, DevopsSubSystem devopsSubsystem, User currentUser) {
        LocalDateTime now = LocalDateTime.now();
        if(StringUtils.isEmpty(request.getBranchModel())){
            DevopsSystem devopsSystem = systemRepository.getById(devopsSubsystem.getSystemId());
            if(StringUtils.isEmpty(devopsSystem.getBranchModel())){
                request.setBranchModel(BranchModelEnum.GENERAL.getCode());
            }else{
                request.setBranchModel(devopsSystem.getBranchModel());
            }
        }
        DevopsFeature devopsFeature = featureMapstruct.toDevopsFeature(request);
        devopsFeature.setSystemId(devopsSubsystem.getSystemId());
        devopsFeature.setDelFlag(SystemConstance.NOT_DELETE);
        devopsFeature.setCreateBy(currentUser.getId());
        devopsFeature.setUpdateBy(currentUser.getId());
        devopsFeature.setCreateTime(now);
        devopsFeature.setUpdateTime(now);
        devopsFeature.setFeatureStatus(0);
        int i = featureRepository.maxFeatureNumberBySubId(request.getSubsystemId());
        devopsFeature.setFeatureNumber(i + 1);
        featureRepository.save(devopsFeature);
        String featureCode = this.getFeatureCode(devopsFeature.getId(), devopsFeature.getFeatureType());
        devopsFeature.setFeatureCode(featureCode);
        featureRepository.updateById(devopsFeature);
        return devopsFeature;
    }

    public void checkCreateRequestParam(FeatureCreateRequest request) {
        codeRepoService.getScmIdBySystemId(request.getSubsystemId());
        if (request.getFeatureName().length() > 100) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "标题限制长度最大为100字符");
        }
        if (!checkParam(request.getBranchDesc())) {
            throw new SystemException(ExceptionCode.REQUEST_ARGUMENT_NOT_VALID, "特性分支名称只能由1-20个数字、字母、下划线、中划线或'.'组成");
        }
        FeatureListQuery query = new FeatureListQuery();
        query.setSubSystemId(request.getSubsystemId());
        query.setBranchDesc(request.getBranchDesc());
        if (CollectionUtils.isNotEmpty(featureRepository.list(query))) {
            throw new SystemException(ExceptionCode.DATA_IS_ALREADY_EXIT, "分支名称重复，请更新后重试");
        }
    }

    private boolean checkParam(String branchDesc) {
        return Pattern.matches("^[a-zA-Z0-9._-]{1,20}$", branchDesc);
    }


    @Override
    public List<FeatureIssuesDto> listFeatureIssues(List<Long> featureIds) {
        if (CollectionUtils.isEmpty(featureIds)){
            return new ArrayList<>();
        }
        FeatureListRequest query = new FeatureListRequest();
        query.setIds(featureIds);
        List<DevopsFeatureDto> list = this.list(query);
        Map<Long, FeatureBranch> featureBranchMap = featureBranchRepository.listByParam(featureIds).stream().collect(Collectors.toMap(FeatureBranch::getFeatureId, fb -> fb));
        for (DevopsFeatureDto devopsFeatureDto : list) {
            FeatureBranch featureBranch = featureBranchMap.getOrDefault(devopsFeatureDto.getId(), new FeatureBranch());
            devopsFeatureDto.setFeatureBranch(featureBranch.getBranchName());
            devopsFeatureDto.setFeatureDevBranch(featureBranch.getBranchDevName());
        }
        return addIssues(list);
    }

    private Map<Long, BranchStageDto> defaultMapBranchStage(List<Long> ids) {
        return ids.stream().collect(Collectors.toMap(id -> id, id -> new BranchStageDto()));

    }

    private List<FeatureTask> listTaskByFeatureId(Long featureId) {
        LambdaQueryWrapper<FeatureTask> query = new LambdaQueryWrapper<>();
        query.eq(FeatureTask::getFeatureId, featureId);
        return featureTaskRepository.list(query);
    }

    @Override
    @Transactional
    public void createFeature(FeatureCreateRequestDto request) {
        FeatureCreateRequest featureCreateRequest = new FeatureCreateRequest();
        featureCreateRequest.setFeatureType(FeatureTypeEnum.FEATURE_TYPE_FEATURE.getType());
        featureCreateRequest.setFeatureName(request.getTitle());
        featureCreateRequest.setProjectId(request.getProjectId());
        featureCreateRequest.setSubsystemId(request.getSubsystemId());
        User currentUser = iamRepository.getCurrentUser();
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(request.getSubsystemId());
        checkSubsystemPermission(devopsSubsystem);
        DevopsFeature devopsFeature =savaLocal(featureCreateRequest, devopsSubsystem, currentUser);
        FeatureBranch featureBranch = featureBranchService.create(devopsFeature, featureCreateRequest.getSourceBranch(), request.getFeatureId());
        FeatureTaskAddReq req = new FeatureTaskAddReq();
        req.setFeatureId(devopsFeature.getId());
        req.setTaskIds(request.getTaskIds());
        this.addTask(req);
        IssuesDTO issuesDTO = new IssuesDTO();
        issuesDTO.setId(request.getFeatureId());
        //todo
//        issuesDTO.setIssuesTypeId(request.getIssuesTypeId());
//        issuesDTO.setIssuesTypeName(request.getIssuesTypeName());
//        issuesDTO.setIssuesTypeCode(request.getIssuesTypeCode());
//        HashMap<String, Object> customFields = new HashMap<>();
//        customFields.put("branch_name",featureBranch.getBranchName());
//        customFields.put("devops_feature_id",devopsFeature.getId());
//        customFields.put("devops_system_id",devopsFeature.getSystemId());
//        issuesDTO.setCustomFields(customFields);
//        issuesProvider.updateIssuesOfSys(request.getFeatureId(), issuesDTO);
    }

    @Override
    public FeatureClearResponse deleteBranch(FeatureClearRequest request) {

        List<DevopsFeature> records = featureRepository.listByIds(request.getIds());
        // 只处理未被清理的功能分支
        List<DevopsFeature> devopsFeatures = records.stream().filter(f -> f.getDelFlag() == 0 && f.getClearFlag() != 2 ).collect(Collectors.toList());
        User currentUser = iamRepository.getCurrentUser();
        if(request.getCheckPermission()){
            // 权限校验
            Map<Long, List<DevopsFeature>> collect = devopsFeatures.stream().collect(Collectors.groupingBy(DevopsFeature::getSubSystemId));
            Map<Long, Map<String, Boolean>> permission = permissionRepository.mapPermission(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, collect.keySet().stream().collect(Collectors.toList()));
            List<String> nPermission = new ArrayList<>();
            for (Long subId : collect.keySet()) {
                List<DevopsFeature> devopsFeatures1 = collect.get(subId);
                Map<String, Boolean> permissionMap = permission.get(subId);
                for (DevopsFeature devopsFeature : devopsFeatures1) {
                    if((devopsFeature.getDirector() == null || !currentUser.getId().equals(devopsFeature.getDirector())) && (permissionMap == null || permissionMap.get(IamConstance.PermissionCode.FEATURE_CLEAR) == null || !permissionMap.get(IamConstance.PermissionCode.FEATURE_CLEAR))){
                        nPermission.add(devopsFeature.getFeatureName());
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(nPermission)){
                throw new SystemException(ExceptionCode.CLEAR_BRANCH_FAIL, nPermission.stream().collect(Collectors.joining(",")) + "  权限不足，清理终止");
            }
        }
        List<Long> subIds = devopsFeatures.stream().map(f -> f.getSubSystemId()).collect(Collectors.toSet()).stream().collect(Collectors.toList());
        Map<Long, Integer> subCodeMap = codeRepoService.mapCodeRepoIdBySystemId(subIds);
        Map<Long, FeatureBranch> mapBranch = featureBranchRepository.listByParam(devopsFeatures.stream().map(f -> f.getId()).collect(Collectors.toList())).stream().collect(Collectors.toMap(FeatureBranch::getFeatureId, fb -> fb));
        if(request.getCheckDelete()){
            // 分支是否合并校验
            FeatureClearResponse response = checkBranchMerge(devopsFeatures, subCodeMap, mapBranch);
            if(response != null){
                return response;
            }
        }
        for (DevopsFeature devopsFeature : devopsFeatures) {
            if(!subCodeMap.containsKey(devopsFeature.getSubSystemId())){
                throw new SystemException(ExceptionCode.CLEAR_BRANCH_FAIL, devopsFeature.getFeatureName() + "  代码库信息错误, 清理终止");
            }
            if(!mapBranch.containsKey(devopsFeature.getId())){
                log.debug(devopsFeature.getFeatureName() + " 代码分支不存在，跳过清理");
                continue;
            }
            FeatureBranch featureBranch = mapBranch.get(devopsFeature.getId());
            if(devopsFeature.getFeatureStatus() == FeatureStatusEnum.CLEAR.getStatus() || (featureBranch.getIsClear() != null &&featureBranch.getIsClear())){
                // 已清理的不做处理
                continue;
            }
            // 清理分支
            this.deleteBranch(devopsFeature, mapBranch.get(devopsFeature.getId()), subCodeMap.get(devopsFeature.getSubSystemId()), currentUser);
        }
        return FeatureClearResponse.success();
    }

    private FeatureClearResponse checkBranchMerge(List<DevopsFeature> devopsFeatures, Map<Long, Integer> subCodeMap, Map<Long, FeatureBranch> mapBranch) {
        List<String> nMerge = new ArrayList<>();
        Map<Long, List<DevopsFeature>> collect = devopsFeatures.stream().collect(Collectors.groupingBy(DevopsFeature::getSubSystemId));
        for (Long subId : collect.keySet()) {
            if(!subCodeMap.containsKey(subId)){
                continue;
            }
            List<DevopsFeature> records = collect.get(subId);
            List<FeatureBranch> fb = new ArrayList<>(); // 待清理的分支
            for (DevopsFeature record : records) {
                if(mapBranch.containsKey(record.getId())){
                    fb.add(mapBranch.get(record.getId()));
                }
            }
            if(CollectionUtils.isNotEmpty(fb)){
                List<String> branches = featureBranchService.listAllBranch(fb, false, false);
                if(CollectionUtils.isNotEmpty(branches)){
                    Map<String, BranchStageDto> stageMap = codeBranchRepository.listBranchStage(subCodeMap.get(subId), branches, codeRepoService.getBaseBranch());
                    for (String branch : stageMap.keySet()) {
                        BranchStageDto branchStageDto = stageMap.get(branch);
                        if(branchStageDto.getAhead() > 0){
                            nMerge.add(branch);
                        }
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty(nMerge)){
            return FeatureClearResponse.builder(nMerge);
        }
        return null;
    }

    public void deleteBranch(DevopsFeature devopsFeature, FeatureBranch featureBranch, Integer codeRepoId, User currentUser){
        Long currentUserId = (currentUser == null || currentUser.getId() == null) ? 0L : currentUser.getId();
        if (featureBranch != null) {
            BranchDto branch = codeBranchRepository.detailsBranch(codeRepoId, featureBranch.getBranchName());
            BranchDto devBranch = branch;
            if(!featureBranch.getBranchName().equals(featureBranch.getBranchDevName())){
                devBranch = codeBranchRepository.detailsBranch(codeRepoId, featureBranch.getBranchDevName());
            }
            if(branch != null){
                codeBranchRepository.removeBranch(codeRepoId, featureBranch.getBranchName());
            }
            if(!featureBranch.getBranchName().equals(featureBranch.getBranchDevName()) && devBranch != null){
                codeBranchRepository.removeBranch(codeRepoId, featureBranch.getBranchDevName());
            }
            if(!featureBranch.getIsClear()){
                // 代码分支标记为已清理，填充commit信息
                String clearCommit = branch == null ? "-" : branch.getCommit().getId();
                String clearDevCommit = devBranch == null ? "-" : devBranch.getCommit().getId();
                featureBranchRepository.clear(devopsFeature.getId(), clearCommit, clearDevCommit, currentUserId);
            }
        }
        // 分支状态更新为已清理
        featureRepository.clear(devopsFeature.getId(), currentUserId);
    }

    private void checkSubsystemPermission(DevopsSubSystem subSystem){
        if(!permissionRepository.checkResource(SystemConstance.AmpResourceTypeCode.SUBSYSTEM, subSystem.getId(), SystemConstance.AmpResourceTypeCode.SYSTEM, subSystem.getSystemId())){
            throw new SystemException(ExceptionCode.PERMISSION_DENIED, "无应用权限");
        }
    }

}




