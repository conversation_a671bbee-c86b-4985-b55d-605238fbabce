package cn.harmonycloud.development.service.mapstruct;

import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.pojo.dto.feature.*;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.vo.feature.*;
import cn.harmonycloud.pojo.FeatureInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2022/8/3
 */
@Mapper(componentModel = "spring")
public interface FeatureMapstruct {
    FeatureMapstruct MAPSTRUCT = Mappers.getMapper(FeatureMapstruct.class);

    WorkItem issuesToWorkItem(IssuesDto projectTask);

    FeatureTaskDTO toFeatureTaskDto(FeatureFeatureBranchDTO fb);

    FeatureInfoDto toFeatureInfoDto(FeatureFeatureBranchDTO f);

    @Mappings({
            @Mapping(target = "featureType", ignore = true),
            @Mapping(target = "featureStatus", ignore = true),
            @Mapping(source = "completeData", target = "completeTime")
    })
    DevopsFeatureDto toDevopsFeatureDto(DevopsFeature feature);

    @Mappings({
            @Mapping(source = "featureName", target = "name"),
            @Mapping(target = "featureStatus", ignore = true),
            @Mapping(source = "featureStatus", target = "status"),
            @Mapping(source = "featureDesc", target = "desc")
    })
    FeatureDetailsDto toFeatureDetailsDto(DevopsFeature devopsFeature);

    @Mappings({
            @Mapping(source = "directorId", target = "director")
    })
    FeaturePageQuery toFeaturePageQuery(FeaturePageRequest request);

    @Mappings({
            @Mapping(source = "id", target = "featureId")
    })
    FeatureFeatureBranchDTO toFeatureBranchDto(DevopsFeatureDto feature);

    @Mappings({
            @Mapping(source = "subsystemId", target = "subSystemId"),
            @Mapping(source = "featureIdsFilter", target = "idsFilter")
    })
    FeaturePageQuery toFeaturePageQuery(FeatureBranchRequestDto requestDto);

    @Mappings({
            @Mapping(source = "title", target = "featureName")
    })
    FeatureListQuery toFeatureListQuery(FeatureListRequest request);

    FeatureListQuery toFeatureListQuery(FeatureBranchRequestDto requestDto);

    @Mappings({
            @Mapping(source = "subsystemId", target = "subSystemId")
    })
    DevopsFeature toDevopsFeature(FeatureCreateRequest request);

    DevopsFeature toDevopsFeature(FeatureUpdateRequest request);

    FeatureIssuesDto FeatureIssuesDto(DevopsFeatureDto fb);

    FeatureBranchDTO toFeatureBranchDTO(FeatureBranch featureBranch);
}
