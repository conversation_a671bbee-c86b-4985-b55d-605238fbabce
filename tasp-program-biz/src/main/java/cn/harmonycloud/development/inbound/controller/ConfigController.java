package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.vo.config.SubConfigCreateRspVO;
import cn.harmonycloud.development.pojo.vo.config.SubConfigCreateVO;
import cn.harmonycloud.development.service.SubSystemComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/10 4:36 下午
 **/
@Validated
@Api(tags = "应用配置管理")
@RequestMapping("/config")
@RestController
public class ConfigController {

    @Autowired
    SubSystemComponentService subSystemComponentService;

    @ApiOperation("创建应用配置库")
    @PostMapping("/create")
    public BaseResult<SubConfigCreateRspVO> list(@Valid @RequestBody SubConfigCreateVO createVO) {
        return BaseResult.ok(subSystemComponentService.createConfig(createVO));
    }

}
