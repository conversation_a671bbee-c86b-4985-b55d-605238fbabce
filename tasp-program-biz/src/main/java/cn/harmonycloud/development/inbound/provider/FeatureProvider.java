package cn.harmonycloud.development.inbound.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.service.FeatureService;
import cn.harmonycloud.pojo.feature.FeatureDto;
import cn.harmonycloud.pojo.feature.FeatureQueryReq;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/23 7:13 下午
 **/
@RestController
@RequestMapping("/provider/feature")
public class FeatureProvider {

    @Autowired
    private FeatureService featureService;

    @ApiOperation("特性列表")
    @GetMapping("/listByCurrent")
    public BaseResult<List<FeatureDto>> list(FeatureQueryReq featureQueryReq) {
//        List<FeatureDto> list = featureService.listFeatureByCurrent(featureQueryReq);
//        return BaseResult.ok(list);
        return null;
    }
}
