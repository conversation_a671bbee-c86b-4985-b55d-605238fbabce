package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.SubsystemRepository;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemCreateRequest;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemDetails;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemGeneralRequest;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemUpdateRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.query.MemberQuery;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.development.service.SubSystemMemberService;
import cn.harmonycloud.development.service.mapstruct.DevopsSubSystemMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigDTO;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigSaveRequest;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/7/28
 */
@Api(tags = "子系统管理")
@RequestMapping("/subSystem/")
@RestController
@Validated
public class SubSystemController {

    @Autowired
    private SubSystemService subSystemService;

    @Autowired
    private SubSystemMemberService subSystemMemberService;

    @Autowired
    private SubsystemRepository subsystemRepository;

    @Autowired
    private DevopsSubSystemMapstruct devopsSubSystemMapstruct;

    @Autowired
    private IamRepository iamRepository;

    @ApiOperation("获取子系统详情")
    @GetMapping("getById/{id}")
    public BaseResult<DevopsSubSystem> getById(@ApiParam(value = "子系统id") @PathVariable Long id) {
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(id);
        if (devopsSubsystem == null) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "子系统信息不存在");
        }
        return BaseResult.ok(devopsSubsystem);
    }

    @OperationAudit(operationName = "添加子系统成员, 实例：${instanceId}",
            operationCode = "研发协同-子系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @ApiOperation("添加子系统成员")
    @PostMapping("createMember")
    public BaseResult createMember(@Valid @RequestBody CreateMemberVO req) {
        req.setCheckCodeRepo(false);
        subSystemMemberService.createMember(req, true);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "更新子系统成员, 实例：${instanceId}",
            operationCode = "研发协同-子系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @ApiOperation("更新子系统成员角色")
    @PostMapping("modifyMember")
    public BaseResult modifyMember(@Valid @RequestBody ModifyMemberVo req) {
        subSystemMemberService.modifyMember(req);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "添加子系统成员, 实例列表：${instanceIds}",
            operationCode = "研发协同-子系统",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceIds", jstl = "$[0].instanceIds")
            })
    @ApiOperation("批量更新子系统成员角色")
    @PostMapping("modifyMemberBatch")
    public BaseResult modifyMemberBatch(@Valid @RequestBody ModifyMemberBatchRequest req) {
        subSystemMemberService.modifyMemberBatch(req);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "删除子系统成员, 实例：${instanceId}",
            operationCode = "研发协同-子系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @ApiOperation("删除子系统成员")
    @PostMapping("deleteMember")
    public BaseResult deleteMember(@RequestBody DeleteMemberVO deleteMemberVO) {
        subSystemMemberService.deleteMember(deleteMemberVO);
        return BaseResult.ok();
    }

    @ApiOperation("展示子系统成员")
    @GetMapping("listMember")
    public BaseResult<Page<ListSystemMemberVO>> listMember(MemberQuery req) {
        return BaseResult.ok(subSystemMemberService.querySystemMember(req));
    }

    @ApiOperation("展示所有子系统成员")
    @GetMapping("allMember")
    public BaseResult<List<ListSystemMemberVO>> allMember(@RequestParam Long subSystemId,
                                                          @RequestParam(required = false) String user) {
        List<ListSystemMemberVO> list = subSystemMemberService.allMember(subSystemId, user);
        return BaseResult.ok(list);
    }

    @ApiOperation("更据子系统id查询系统子系统信息")
    @GetMapping("{subSystemId}/info")
    public BaseResult<SubSystemInfoDto> info(@PathVariable Long subSystemId) {
        return BaseResult.ok(subSystemService.info(subSystemId));
    }

    @ApiOperation("更据子系统id查询系统子系统信息")
    @GetMapping("info")
    public BaseResult<SubSystemInfoDto> infoByCode(@RequestParam String subSystemCode) {
        return BaseResult.ok(subSystemService.infoByCode(subSystemCode));
    }

    @ApiOperation("根据系统id查询所有子系统（弃用）")
    @GetMapping("/listByUser")
    public BaseResult<List<SubSystemDataVO>> listByUser(@RequestParam Long systemId) {
        List<DevopsSubSystem> devopsSubSystems = subsystemRepository.listBySystemId(systemId);
        List<SubSystemDataVO> collect = devopsSubSystems.stream().map(subSystem -> devopsSubSystemMapstruct.doToSystemData(subSystem)).collect(Collectors.toList());
        return BaseResult.ok(collect);
    }

    @ApiOperation("查询子系统的成员角色")
    @GetMapping("/roles")
    public BaseResult<List<RoleInfoDto>> roles() {
        return BaseResult.ok(subSystemMemberService.roles());
    }

    @ApiOperation("查询可添加的系统成员")
    @GetMapping("/listOverUser")
    public BaseResult<List<UserVo>> listOverUser(@RequestParam Long subSystemId,
                                                 @RequestParam(required = false) String queryParam) {
        return BaseResult.ok(subSystemMemberService.listOverUser(subSystemId, queryParam));
    }

    @ApiOperation("查询所有可添加的成员")
    @GetMapping("/listAll")
    public BaseResult<List<UserVo>> listByCurrent(@RequestParam Long subSystemId) {
        return BaseResult.ok(subSystemMemberService.listOverUser(subSystemId, ""));
    }

    @ApiOperation("查询当前登录人的子系统系统列表")
    @GetMapping("resource/listAll")
    public BaseResult<List<ListSubSystemByCurrentVo>> listByCurrent() {
        return BaseResult.ok(subSystemMemberService.listByCurrent());
    }

    @ApiOperation("查询当前登录人的子系统系统列表")
    @GetMapping("resourceAll")
    public BaseResult<List<SubsystemDto>> resourceAll(SubsystemGeneralRequest request) {
        return BaseResult.ok(subSystemService.resourceListAll(request));
    }

    @ApiOperation("系统下子系统列表(权限控制)")
    @GetMapping("list/resource")
    public BaseResult<List<SubsystemDto>> listResource(SubsystemGeneralRequest request) {
        List<SubsystemDto> records = subSystemService.listResource(request);
        return BaseResult.ok(records);
    }

    @OperationAudit(operationName = "创建子系统, 名称：${fullNameCn}",
            operationCode = "研发协同-子系统",
            dynOperationValues = {
                    @DynOperationValue(key = "fullNameCn", jstl = "$[0].fullNameCn")
            })
    @ApiOperation("子系统创建")
    @PostMapping("create")
    public BaseResult<DevopsSubSystem> create(@Valid @RequestBody SubsystemCreateRequest request) {
        User currentUser = iamRepository.getCurrentUser();
        DevopsSubSystem devopsSubSystem = subSystemService.createSubsystem(request, currentUser);
        subSystemService.creatSubsystemPost(devopsSubSystem, request);
        return BaseResult.ok(devopsSubSystem);
    }

    @OperationAudit(operationName = "更新子系统, 名称：${fullNameCn}",
            operationCode = "研发协同-子系统",
            operationId = "${fullNameCn}",
            dynOperationValues = {
                    @DynOperationValue(key = "fullNameCn", jstl = "$[0].fullNameCn")
            })
    @ApiOperation("更新子系统")
    @PostMapping("update")
    public BaseResult<SubsystemDetails> update(@Valid @RequestBody SubsystemUpdateRequest request) {
        SubsystemDetails update = subSystemService.update(request);
        return BaseResult.ok(update);
    }

    @ApiOperation("子系统详情")
    @GetMapping("details/{id}")
    public BaseResult<SubsystemDetails> update(@PathVariable Long id, @RequestParam(required = false) Boolean codeDetails) {
        return BaseResult.ok(subSystemService.details(id, codeDetails));
    }

    @OperationAudit(operationName = "删除子系统, key：${id}",
            operationCode = "研发协同-子系统",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0]")
            })
    @ApiOperation("删除子系统")
    @DeleteMapping("remove/{id}")
    public BaseResult remove(@PathVariable Long id) {
        subSystemService.remove(id);
        return BaseResult.ok();
    }


    @ApiOperation("子系统列表")
    @GetMapping("/list")
    public BaseResult<List<SubsystemDto>> list(SubsystemGeneralRequest request) {
        return BaseResult.ok(subSystemService.list(request));
    }


    @ApiOperation("查询用户在子系统下的角色信息")
    @GetMapping("/listSubsystemRole")
    public BaseResult<List<SubsystemDto>> listSubsystemRole(@ApiParam(name = "系统id") @RequestParam Long systemId,
                                                            @ApiParam(name = "用户id") @RequestParam Long userId) {
        List<SubsystemDto> list = subSystemMemberService.listSubsystemRole(systemId, userId);
        return BaseResult.ok(list);
    }

    @ApiOperation("查询子系统配置信息")
    @GetMapping("/getConfig")
    public BaseResult<SubsystemConfigDTO> getConfig(@ApiParam(name = "应用id") @RequestParam Long subsystemId) {
        SubsystemConfigDTO config = subSystemService.getConfig(subsystemId);
        return BaseResult.ok(config);
    }

    @ApiOperation("保存配置信息")
    @PostMapping("/saveConfig")
    public BaseResult<SubsystemConfigDTO> saveConfig(@Valid @RequestBody SubsystemConfigSaveRequest request) {
        SubsystemConfigDTO config = subSystemService.saveConfig(request);
        return BaseResult.ok(config);
    }

}
