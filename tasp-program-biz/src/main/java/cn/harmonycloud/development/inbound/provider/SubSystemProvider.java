package cn.harmonycloud.development.inbound.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.pojo.dto.issues.IssuesBaseSelectDTO;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemGeneralRequest;
import cn.harmonycloud.development.pojo.vo.system.SubSystemGitlab;
import cn.harmonycloud.development.service.SubSystemMemberService;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.pojo.subsystem.MemberQuery;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import cn.harmonycloud.pojo.subsystem.SubsystemQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/21 1:56 下午
 **/
@Validated
@Api(tags = "子系统管理")
@RequestMapping("/provider/subSystem")
@RestController
public class SubSystemProvider {

    @Autowired
    private SubSystemService subSystemService;
    @Autowired
    private SubSystemMemberService subSystemMemberService;
    @Autowired
    private IamRepository iamRepository;

    @ApiOperation("获取子系统详情")
    @GetMapping("/listByGitlabIds")
    public BaseResult<List<SubSystemGitlab>> listByGitlabIds(@RequestParam String gitlabIds) {
        if(StringUtils.isEmpty(gitlabIds)){
            return BaseResult.ok(new ArrayList<>());
        }
        List<String> ids = Arrays.asList(gitlabIds.split(","));
        return BaseResult.ok(subSystemService.listByGitlabIds(ids));
    }

    @ApiOperation("子系统列表")
    @GetMapping("/list")
    public BaseResult<List<SubsystemDto>> list(SubsystemQueryDto queryDto) {
        return BaseResult.ok(subSystemService.list(queryDto));
    }

    /**
     * 跟踪事项
     *
     * @param resourceFlag
     * @return
     */
    @ApiOperation("子系统列表")
    @GetMapping("/listIssues")
    public BaseResult<List<IssuesBaseSelectDTO>> listAll(@RequestParam(required = false) Boolean resourceFlag) {
        if(resourceFlag == null){
            resourceFlag = false;
        }
        SubsystemGeneralRequest request = new SubsystemGeneralRequest();
        request.setResource(resourceFlag);
        List<SubsystemDto> subsystems = subSystemService.resourceListAll(request);
        List<IssuesBaseSelectDTO> collect = subsystems.stream().map(sub -> new IssuesBaseSelectDTO(sub.getId().toString(), sub.getFullNameCn())).collect(Collectors.toList());
        return BaseResult.ok(collect);
    }

    @ApiOperation("获取子系统成员")
    @GetMapping("/listMember")
    public BaseResult<List<UserVo>> listMember(@Valid MemberQuery memberQuery) {
        long subSystemId = Long.parseLong(memberQuery.getInstanceId());
        List<UserVo> users = subSystemMemberService.listMember(subSystemId, memberQuery.getWithSystemAdmin());
        return BaseResult.ok(users);
    }

}
