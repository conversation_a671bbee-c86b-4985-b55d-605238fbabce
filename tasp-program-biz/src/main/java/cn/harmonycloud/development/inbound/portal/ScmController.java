package cn.harmonycloud.development.inbound.portal;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchDto;
import cn.harmonycloud.development.outbound.api.dto.coderepo.TagDto;
import cn.harmonycloud.development.service.CodeRepoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 代码管理相关接口
 * <AUTHOR>
 * @Date 2022/8/22 5:34 下午
 **/
@Api(tags = "代码管理")
@RequestMapping("/scm/")
@RestController
public class ScmController {

    @Autowired
    private CodeRepoService codeRepoService;

    @ApiOperation("根据子系统id查询分支列表")
    @GetMapping("/{subSystemId}/branches")
    public BaseResult<List<BranchDto>> branches(@PathVariable Long subSystemId){
        return BaseResult.ok(codeRepoService.branches(subSystemId));
    }

    @ApiOperation("根据子系统id查询分支列表")
    @GetMapping("/tagList")
    public BaseResult<List<TagDto>> tagList(@RequestParam Long subSystemId,
                                            @RequestParam(required = false) String name){
        return BaseResult.ok(codeRepoService.tagList(subSystemId, name));
    }

    @ApiOperation("根据子系统id查询分支列表以及标签")
    @GetMapping("/listBranchAndTag")
    public BaseResult<List<String>> listBranchAndTag(@RequestParam Long subSystemId){
        return BaseResult.ok(codeRepoService.listBranchAndTag(subSystemId));
    }

    @ApiOperation("根据子系统id查询基础分支")
    @GetMapping("/{subSystemId}/baseBranch")
    public BaseResult<String> baseBranch(@PathVariable Long subSystemId){
        return BaseResult.ok(codeRepoService.baseBranch(subSystemId));
    }
}
