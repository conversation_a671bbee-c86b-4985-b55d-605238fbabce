package cn.harmonycloud.development.inbound.open;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import cn.harmonycloud.development.service.OperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/15 12:39 下午
 **/
@RestController
@Api("运维")
@RequestMapping("/open/operation")
public class OpenOperationController {

    @Autowired
    OperationService operationService;

    @ApiOperation("健康检查")
    @GetMapping("/health")
    public Health health(){
        return new Health.Builder().up().build();
    }

    @ApiOperation("2.4.0升级2.4.1环境数据修复")
    @GetMapping("/iterationEnv")
    public BaseResult iterationEnv(){
        operationService.iterationEnv();
        return BaseResult.ok("操作成功");
    }

    @ApiOperation("2.7.4系统成员同步角色角色修复")
    @GetMapping("/iterationSystemMember")
    public BaseResult iterationSystemMember(){
        operationService.iterationSystemMember();
        return BaseResult.ok("操作成功");
    }

}
