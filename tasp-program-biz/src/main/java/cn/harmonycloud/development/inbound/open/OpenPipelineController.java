package cn.harmonycloud.development.inbound.open;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineMergeNotify;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineNotifyReq;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineProductBuildReq;
import cn.harmonycloud.development.pojo.vo.pipeline.VersionDeployInfo;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import cn.harmonycloud.development.outbound.api.dto.scm.MergeTaskResponse;
import cn.harmonycloud.development.service.DevelopmentService;
import cn.harmonycloud.development.service.SubSystemService;
import cn.harmonycloud.development.service.VersionService;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pojo.version.VersionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description 流水线使用接口
 * <AUTHOR>
 * @Date 2022/10/27 5:05 下午
 **/
@RestController
@Api("流水线内部使用")
@RequestMapping("/open/pipeline")
public class OpenPipelineController {

    @Autowired
    private SubSystemService subSystemService;
    @Autowired
    private DevelopmentService developmentService;
    @Autowired
    private VersionService versionService;


    @ApiOperation("根据环境code、子系统id查询源分支列表")
    @GetMapping("/getSubSystemById")
    public BaseResult<SubSystemInfoDto> listProjectFeature(@RequestParam Long subSystemId){
        return BaseResult.ok(subSystemService.infoWithGitlab(subSystemId));
    }

    @ApiOperation("查询流水线构建环境变量")
    @GetMapping("/getSubSystemEnvVar")
    public BaseResult<Map> getSubSystemEnvVar(@RequestParam(required = false) Long buildId,
                                              @RequestParam(required = false) Long jobId){
        Map var = developmentService.getPipelineVar(jobId, buildId);
        return BaseResult.ok(var);
    }

    @ApiOperation("流水线构建结束通知")
    @PostMapping("/notify/{buildId}")
    public BaseResult<MergeTaskResponse> notify(@PathVariable Long buildId, @RequestBody PipelineNotifyReq notifyReq){
        if(notifyReq.getSubSystemId() == null){
            throw new SystemException(ExceptionCode.INNER_EXCEPTION, "子系统id不能为空");
        }
        developmentService.notifyResult(buildId, notifyReq);
        return BaseResult.ok();
    }

    @ApiOperation("流水线构建结束通知")
    @PostMapping("/mergeNotify")
    public BaseResult<MergeTaskResponse> mergeNotify(@RequestBody PipelineMergeNotify notify){

        developmentService.mergeNotify(notify);
        return BaseResult.ok();
    }

    /**
     * 校验当前子版本是否存在同名制品
     *  不存在测直接返回子版本
     *  存在：
     *      策略1覆盖: 已晋级的不覆盖，生成新的版本号
     *      策略2不覆盖：生成新版本号
     * @param req
     * @return
     */
    @ApiOperation("子系统镜像构建子步骤获取版本")
    @PostMapping("/checkSubversion")
    public BaseResult<VersionDto> checkSubversion(@RequestBody PipelineProductBuildReq req){

        VersionDto versionDto = developmentService.checkSubversion(req);
        return BaseResult.ok(versionDto);
    }

    /**
     * 查询应用版本部署信息
     *
     * @return
     */
    @ApiOperation("查询应用版本部署信息")
    @GetMapping("/versionDeploy")
    public BaseResult<VersionDeployInfo> versionDeploy(@RequestParam Long subsystemId,
                                                @RequestParam String majorVersion,
                                                @RequestParam(required = false) List<Long> envIds,
                                                @RequestParam(required = false) String productFormat){

        VersionDeployInfo deployInfo = versionService.versionDeploy(subsystemId, majorVersion, envIds, productFormat);
        return BaseResult.ok(deployInfo);
    }

}
