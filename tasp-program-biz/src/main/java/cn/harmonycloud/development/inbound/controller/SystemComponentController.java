package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.service.SystemComponentService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "系统构件")
@RequestMapping("/systemComponent/")
@RestController
public class SystemComponentController {

    @Autowired
    private SystemComponentService systemComponentService;

    @OperationAudit(operationName = "创建group, systemId：${systemId}",
            operationCode = "研发协同-系统",
            operationId = "${systemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "systemId", jstl = "$[0]")
            })
    @PostMapping("/createGroup")
    public BaseResult createGroup(@RequestParam Long systemId, @RequestParam String name, @RequestParam String desc, @RequestParam String path) {
        systemComponentService.addGroupId(systemId, name, desc, path, null);
        return BaseResult.ok();
    }
}
