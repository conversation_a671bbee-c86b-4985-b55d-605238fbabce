package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.env.EnvVarDTO;
import cn.harmonycloud.development.pojo.entity.SubSystemVariable;
import cn.harmonycloud.development.service.SubSystemEnvService;
import cn.harmonycloud.development.service.SubSystemVariableService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * @ClassName SubSystemEnvController
 * @Description
 * <AUTHOR>
 * @Date 2022/9/6 9:43 AM
 **/
@RestController
@Api(value = "/subSystem/env",tags = "子系统环境管理")
@RequestMapping("/subSystem/env")
@Validated
public class SubSystemEnvController {

    @Autowired
    SubSystemEnvService subSystemEnvService;

    @Autowired
    SubSystemVariableService variableService;

    @OperationAudit(operationName = "关联环境和变量, instanceId：${systemId}",
            operationCode = "研发协同-子系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @PostMapping
    @ApiOperation("关联环境和变量")
    public BaseResult createEnvVar(@RequestBody EnvVarDTO envVarDTO){
        return subSystemEnvService.createEnvVar(envVarDTO);
    }

    @OperationAudit(operationName = "新建子系统环境变量, eId：${eId}",
            operationCode = "研发协同-子系统",
            operationId = "${eId}",
            dynOperationValues = {
                    @DynOperationValue(key = "eId", jstl = "$[0].eId")
            })
    @PostMapping("/var")
    @ApiOperation("新建子系统环境变量")
    public BaseResult createVar(@Valid @RequestBody SubSystemVariable subSystemVariable){
        return subSystemEnvService.createVar(subSystemVariable);
    }

    @OperationAudit(operationName = "更新子系统环境变量, eId：${eId}",
            operationCode = "研发协同-子系统",
            operationId = "${eId}",
            dynOperationValues = {
                    @DynOperationValue(key = "eId", jstl = "$[0].eId")
            })
    @PutMapping("/var")
    @ApiOperation("更新子系统环境变量")
    public BaseResult updateVar(@Valid @RequestBody SubSystemVariable subSystemVariable){
        return subSystemEnvService.updateVar(subSystemVariable);
    }

    @GetMapping("/var")
    @ApiOperation("获取子系统环境变量列表")
    public BaseResult getVars(Long envId){
        return BaseResult.ok(subSystemEnvService.getVars(envId));
    }

    @GetMapping
    @ApiOperation("获取子系统环境")
    public BaseResult getEnvs(@RequestParam Long subSystemId){
        return subSystemEnvService.getEnvs(subSystemId);
    }

    @GetMapping("/main")
    @ApiOperation("获取上机版本相关变量信息")
    public BaseResult getMainVars(@RequestParam Long subSystemId){
        return subSystemEnvService.getMainVars(subSystemId);
    }

    @OperationAudit(operationName = "删除子系统变量, eId：${eId}",
            operationCode = "研发协同-子系统",
            operationId = "${eId}",
            dynOperationValues = {
                    @DynOperationValue(key = "eId", jstl = "$[0]")
            })
    @DeleteMapping("/var")
    @ApiOperation("删除子系统变量")
    public BaseResult deleteVar(Long id){
        variableService.removeById(id);
        return BaseResult.ok();
    }

    @ApiOperation("获取环境列表")
    @GetMapping("/{subSystemId}/listEnv")
    public BaseResult listEnv(@PathVariable Long subSystemId) {
        return subSystemEnvService.getEnvNames(subSystemId);
    }

    /**
     * 根据子系统id和环境code查询子系统变量
     * @param envId
     * @return
     */
    @ApiOperation("子系统变量")
    @GetMapping("/listVariable")
    public BaseResult<Map<String, Object>> listVariable(@RequestParam Long envId) {
        return BaseResult.ok(subSystemEnvService.getDefaultAndSystemVarsMap(envId));
    }

    @ApiOperation("获取环境列表(制品管理使用)")
    @GetMapping("/getAllEnvData")
    public BaseResult getAllEnvData(@RequestParam String subSystemCode, @RequestParam String envCode) {
        return BaseResult.ok(subSystemEnvService.getAllEnvData(subSystemCode, envCode));
    }
}
