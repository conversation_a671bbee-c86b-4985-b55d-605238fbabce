package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.devopsstage.*;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnv;
import cn.harmonycloud.development.service.DevopsStageService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/6 4:26 下午
 **/
@Api(tags = "子系统阶段")
@RequestMapping("/devopsStage")
@RestController
@Validated
public class DevopsStageController {

    @Autowired
    private DevopsStageService devopsStageService;

    @ApiOperation("子系统阶段列表")
    @GetMapping("/list")
    public BaseResult<List<DevopsStageDto>> list(@ApiParam(name = "subsystemId", value = "子系统id") @RequestParam Long subsystemId) {
        return BaseResult.ok(devopsStageService.list(subsystemId));
    }

    @OperationAudit(operationName = "阶段管理-保存阶段配置",
            operationCode = "研发协同-阶段"
    )
    @ApiOperation("阶段配置保存")
    @PostMapping("/configSave")
    public BaseResult configSave(@Valid @RequestBody DevopsStageSaveRequest request) {
        devopsStageService.configSave(request);
        return BaseResult.ok();
    }

    @ApiOperation("环境列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stageId", value = "阶段id")
    })
    @GetMapping ("/envList")
    public BaseResult<List<DevopsStageEnvDto>> envList(DevopsStageEnvListRequest request ) {
        List<DevopsStageEnvDto> record = devopsStageService.envList(request);
        return BaseResult.ok(record);
    }


    @OperationAudit(operationName = "阶段管理-新增阶段环境:${envName}",
            operationCode = "研发协同-阶段",
            dynOperationValues = {
                    @DynOperationValue(key = "envName",jstl = "$[0].envName")
            }
    )
    @ApiOperation("创建一个环境")
    @PostMapping("/envCreate")
    public BaseResult<DevopsStageEnv> envCreate(@Valid @RequestBody DevopsStageEnvCreate request) {
        return BaseResult.ok(devopsStageService.envCreate(request));
    }

    @ApiOperation("环境详情")
    @GetMapping("/envInfo")
    public BaseResult<DevopsStageEnvDto> envInfo(@ApiParam(name = "id", value = "阶段环境id") @RequestParam Long id) {
        DevopsStageEnvDto record = devopsStageService.envInfo(id, true);
        return BaseResult.ok(record);
    }

    @OperationAudit(operationName = "阶段管理-编辑阶段环境:${envName}",
            operationCode = "研发协同-阶段",
            dynOperationValues = {
                    @DynOperationValue(key = "envName",jstl = "$[0].envName")
            }
    )
    @ApiOperation("环境信息编辑")
    @PostMapping("/envModify")
    public BaseResult envModify(@Valid @RequestBody DevopsStageEnvModify request) {
        devopsStageService.envModify(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "阶段管理-删除环境",
            operationCode = "研发协同-阶段",
            requestFlag = true
    )
    @ApiOperation("环境删除（环境回收）")
    @PostMapping("/envDelete/{id}")
    public BaseResult envDelete(@PathVariable Long id) {
        devopsStageService.envDelete(id);
        return BaseResult.ok();
    }


}
