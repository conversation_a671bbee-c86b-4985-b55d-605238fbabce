package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.autotest.AutotestDTO;
import cn.harmonycloud.development.pojo.dto.autotest.CreateAutotestDTO;
import cn.harmonycloud.development.pojo.dto.autotest.StartAutotestListDTO;
import cn.harmonycloud.development.pojo.dto.test.IdDTO;
import cn.harmonycloud.development.pojo.dto.test.UpdateStatusDTO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestDetailResultListVO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestDetailVO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestVO;
import cn.harmonycloud.development.service.AutotestManagementService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api("自动化测试接口")
@RequestMapping("/autotestManagement")
public class AutotestManagementController {

    @Autowired
    private AutotestManagementService autotestManagementService;

    /**
     * 自动化测试分页
     *
     * @param dto
     * @return
     */
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public BaseResult<Page<AutotestVO>> page(@RequestBody AutotestDTO dto) {
        return BaseResult.ok(autotestManagementService.getPage(dto));
    }

    /**
     * 启动自动化测试弹窗
     */
    @ApiOperation("启动自动化测试弹窗")
    @PostMapping("/startAutotestList")
    public BaseResult<List<AutotestVO>> startAutotestList(@RequestBody StartAutotestListDTO dto) {

        List<AutotestVO> list = autotestManagementService.startAutotestList(dto.getTestIdList());
        return BaseResult.ok(list);
    }

    /**
     * 自动化测试详情
     */
    @ApiOperation("自动化测试详情")
    @PostMapping("/autotestDetailList")
    public BaseResult<AutotestDetailVO> autotestDetailList(@RequestBody IdDTO dto) {
        AutotestDetailVO vo = autotestManagementService.autotestDetailList(dto.getId());
        return BaseResult.ok(vo);
    }

    /**
     * 开启自动化测试
     */
    @OperationAudit(operationName = "开启自动化测试",
            operationCode = "研发协同-测试")
    @ApiOperation("开启自动化测试")
    @PostMapping("/create")
    public BaseResult create(@RequestBody CreateAutotestDTO dto) {
        autotestManagementService.create(dto);
        return BaseResult.ok();
    }

    /**
     * 自动化测试详情-测试结果
     */
    @ApiOperation("自动化测试详情-测试结果")
    @GetMapping("/autotestDetailResultList")
    public BaseResult<List<AutotestDetailResultListVO>> autotestDetailResultList(@RequestParam Long id) {
        List<AutotestDetailResultListVO> vo = autotestManagementService.autotestDetailResultList(id);
        return BaseResult.ok(vo);
    }

    /**
     * 自动化测试记录-状态改变
     */
    @OperationAudit(operationName = "自动化测试记录-状态改变：${id}",
            operationCode = "研发协同-测试",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0].id")
            })
    @ApiOperation("自动化测试记录-状态改变")
    @PutMapping("/updateStatus")
    public BaseResult updateStatus(@RequestBody UpdateStatusDTO dto) {

        return BaseResult.ok(autotestManagementService.updateStatus(dto.getId(), dto.getStatus()));
    }
}
