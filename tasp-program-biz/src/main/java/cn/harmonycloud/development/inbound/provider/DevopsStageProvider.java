package cn.harmonycloud.development.inbound.provider;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.service.DevopsStageService;
import cn.harmonycloud.pojo.devopsstage.DevopsStageEnvRspDto;
import cn.harmonycloud.pojo.feature.FeatureDto;
import cn.harmonycloud.pojo.feature.FeatureQueryReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/5 4:19 下午
 **/
@Api(tags = "阶段服务接口")
@RequestMapping("/provider/devopsStage")
@RestController
public class DevopsStageProvider {

    @Autowired
    private DevopsStageService devopsStageService;

    @ApiOperation("已使用的环境列表")
    @GetMapping("/mapStageEnv")
    public BaseResult<Map<Integer, List<DevopsStageEnvRspDto>>> mapStageEnv(@RequestParam List<Integer> deployIds, @RequestParam Long systemId, @RequestParam Integer deployEnv) {
        Map<Integer, List<DevopsStageEnvRspDto>> result = devopsStageService.mapStageEnv(systemId, deployEnv, deployIds);
        return BaseResult.ok(result);
    }
}
