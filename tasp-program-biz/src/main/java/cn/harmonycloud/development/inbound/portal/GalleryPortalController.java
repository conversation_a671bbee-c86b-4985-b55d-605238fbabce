package cn.harmonycloud.development.inbound.portal;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.vo.gallery.PipelineViewUpdateRequest;
import cn.harmonycloud.development.pojo.vo.gallery.StatisticsRequestVo;
import cn.harmonycloud.development.pojo.vo.gallery.StatisticsResponseVo;
import cn.harmonycloud.development.outbound.api.dto.pipeline.BuildDetailDto;
import cn.harmonycloud.development.service.GalleryService;
import cn.harmonycloud.development.service.PipelineService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import cn.harmonycloud.development.service.*;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Description 子系统概览
 * <AUTHOR>
 * @Date 2022/9/27 10:02 上午
 **/
@Api(tags = "子系统概览")
@RestController
@RequestMapping("/gallery")
public class GalleryPortalController {

    @Autowired
    private GalleryService galleryService;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private TestManagementService testManagementService;

    /**
     * 统计面板 统计代码提交次数、特性完成数、代码评审数、测试数量、构建数等
     *
     * @param requestVo
     * @return
     */
    @ApiOperation("统计面板 统计代码提交次数、特性完成数等")
    @ApiImplicitParams({
//            @ApiImplicitParam(name = "dimension", value = "统计维度: 0  按个人维度统计、1  按子系统全局维度统计", required = true, paramType = "query"),
            @ApiImplicitParam(name = "time", value = "时间范围: 0-今日、1-本周、2-本月", required = true, paramType = "query"),
            @ApiImplicitParam(name = "subSystemId", value = "子系统id", required = true, paramType = "query")
    })
    @GetMapping("/statistics")
    public BaseResult<StatisticsResponseVo> statistics(StatisticsRequestVo requestVo) {
        return BaseResult.ok(galleryService.statistics(requestVo));
    }

    /**
     * 工作台-是否展示流水线接口
     * @param request
     * @return
     */
    @OperationAudit(operationName = "制品库-更新流水线展示视图",
            operationCode = "研发协同-子系统",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0].subSystemId")
            }
    )
    @ApiOperation("修改流水线")
    @PostMapping("/pipeline/view")
    public BaseResult pipelineViewUpdate(@RequestBody PipelineViewUpdateRequest request){
        pipelineService.pipelineViewUpdate(request);
        return BaseResult.ok("更新成功");
    }

    /**
     * 工作台-流水线展示模块
     * @param subSystemId 子系统id
     * @return
     */

    @ApiOperation("查询流水线（工作台）")
    @GetMapping("/pipeline/view")
    public BaseResult<List<BuildDetailDto>> pipelineView(@RequestParam Long subSystemId){
        return BaseResult.ok(pipelineService.pipelineView(subSystemId));
    }

    /**
     * 子系统工作台-测试进度，最多返回10条
     * @param subsystemId 子系统id
     * @param stageEnvId null-全部，1-main，2-sit，3-uat
     * @return
     */
    @ApiOperation("子系统工作台我的测试列表")
    @GetMapping("/myTest")
    public BaseResult myTest(@ApiParam(name = "子系统id", value = "subsystemId") @RequestParam Long subsystemId,
            @ApiParam(name = "阶段环境id", value = "stageEnvId") @RequestParam(value = "stageEnvId", required = false) Long stageEnvId) {
        return BaseResult.ok(testManagementService.myTest(subsystemId, stageEnvId));
    }


}
