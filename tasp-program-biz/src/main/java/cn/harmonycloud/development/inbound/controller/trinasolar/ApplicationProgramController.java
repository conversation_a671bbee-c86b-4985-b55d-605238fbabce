package cn.harmonycloud.development.inbound.controller.trinasolar;


import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.constant.CommonConstants;
import cn.harmonycloud.development.outbound.api.feign.IntegrationProvider;
import cn.harmonycloud.development.pojo.dto.thgn.*;
import cn.harmonycloud.development.pojo.dto.thgn.GitlabCommitCountDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationCheckUserInAppDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDetailDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationUserDTO;
import cn.harmonycloud.trinasolar.model.entity.ApplicationProgram;
import cn.harmonycloud.development.pojo.vo.thgn.app.ApplicationProgramVO;
import cn.harmonycloud.development.service.trinasolar.impl.ApplicationProgramServiceImpl;
import cn.harmonycloud.issue.model.CommonResult;
import cn.harmonycloud.trinasolar.model.PageResult;
import cn.harmonycloud.trinasolar.model.ScaffoldTemplateUseInfoRespDTO;
import cn.harmonycloud.trinasolar.model.TechStackRankRespDTO;
import cn.harmonycloud.trinasolar.model.vo.DevOpsPipelineBuildLogRespVO;
import cn.harmonycloud.trinasolar.model.vo.DevOpsPipelineHisRespVO;
import cn.harmonycloud.trinasolar.model.vo.PipelineRespVO;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 应用程序管理
 * <AUTHOR> Gong
 * @Date 2025/3/25
 **/
@Api(tags = "应用程序管理")
@RequestMapping("/base")
@RestController
public class ApplicationProgramController {


    @Autowired
    private ApplicationProgramServiceImpl applicationProgramService;

    @Autowired
    private IntegrationProvider integrationProvider;


    /**
     * 首页展示应用程序
     */
    @Operation(summary = "获取关联git下拉展示")
    @GetMapping("/git/list/{id}")
    public BaseResult<List<String>> getGitList(@PathVariable("id") Long id) {
        return BaseResult.ok(applicationProgramService.getGitList(id));
    }

    /**
     * 首页展示应用程序
     */
    @Operation(summary = "应用程序总数")
    @GetMapping("/app/count")
    public BaseResult<Integer> count() {
        return BaseResult.ok(applicationProgramService.count());
    }

    /**
     * 创建应用程序（含GitLab仓库初始化）
     *
     * @param applicationProgramDTO 包含技术栈、负责人等信息的DTO
     * @return 创建操作是否成功
     */
    @Operation(summary = "应用程序创建")
    @PostMapping("/create")
    public BaseResult<ApplicationProgram> create(@RequestBody ApplicationProgramDTO applicationProgramDTO) {
        return BaseResult.ok(applicationProgramService.createApplicationProgram(applicationProgramDTO));
    }

    /**
     * 创建应用程序（只是创建应用程序，不同步下游系统）
     *
     * @return 创建操作是否成功
     */
    @Operation(summary = "应用程序批量创建")
    @PostMapping("/createBatch")
    public BaseResult<Boolean> createBatch(@RequestBody List<ApplicationProgramDTO> applicationProgramDTOList) {
        return BaseResult.ok(applicationProgramService.createBatch(applicationProgramDTOList));
    }

    @Operation(summary = "应用程序创建校验")
    @PostMapping("/createCheck")
    public BaseResult<Boolean> createCheck(@RequestBody ApplicationProgramDTO applicationProgramDTO) {
        return BaseResult.ok(applicationProgramService.createCheck(applicationProgramDTO));
    }

    /**
     * 应用程序（含GitLab仓库初始化）
     *
     * @param applicationProgramDTO 包含技术栈、负责人等信息的DTO
     * @return 创建操作是否成功
     */
    @Operation(summary = "应用程序编辑")
    @PutMapping("/modify")
    public BaseResult<Boolean> modify(@RequestBody ApplicationProgramDTO applicationProgramDTO) {
        applicationProgramService.modifyApplicationProgram(applicationProgramDTO);
        return BaseResult.ok(Boolean.TRUE);
    }


    /**
     * @param id
     * @return
     */
    @Operation(summary = "应用删除")
    @GetMapping("/delete/{id}")
    public BaseResult<Boolean> delete(@PathVariable("id") Long id) {
        applicationProgramService.deleteApp(id);
        return BaseResult.ok(Boolean.TRUE);
    }

    /**
     * 条件查询应用程序列表（支持系统ID、状态等过滤条件）
     *
     * @param programNameCn 英文名称
     * @return 应用程序实体列表
     */
    @Operation(summary = "应用程序列表")
    @GetMapping("/list")
    public BaseResult<Page<ApplicationProgramVO>> list(@RequestParam("applicationId") Long applicationId,
                                                       @RequestParam(required = false) String programNameCn,
                                                       @RequestParam(defaultValue = "1") Integer pageNo,
                                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                                       @RequestParam(required = false) String technologyStack) {
        Page<ApplicationProgram> page = new Page<>(pageNo, pageSize);
        return BaseResult.ok(applicationProgramService.listApplicationProgram(applicationId, programNameCn, page, technologyStack));
    }


    /**
     * 校验用户是否在应用程序中存在
     *
     * @param applicationId
     * @param userId
     * @return
     */
    @Operation(summary = "应用系统删除校验")
    @GetMapping("/check")
    public BaseResult<ApplicationCheckUserInAppDTO> checkUserInApp(@RequestParam("applicationId") Long applicationId, @RequestParam("userId") Long userId) {
        return BaseResult.ok(applicationProgramService.checkUserInApp(applicationId, userId));
    }

    /**
     * 获取应用程序完整详情（包含关联的GitLab仓库、技术栈详情等）
     *
     * @param programId 应用程序主键ID
     * @return 包含所有字段的应用程序实体
     */
    @Operation(summary = "应用程序详情")
    @GetMapping("/detail/{programId}")
    public BaseResult<ApplicationProgramDetailDTO> detail(@PathVariable(value = "programId") Long programId) {
        ApplicationProgramDetailDTO applicationProgram = applicationProgramService.applicationProgramDetail(programId);
        return BaseResult.ok(applicationProgram);
    }

    /**
     * 获取技术栈选项列表（用于前端下拉选择）
     *
     * @param applicationProgramDTO 包含过滤条件的DTO（如系统ID）
     * @return 技术栈标签字符串列表
     */
    @Operation(summary = "技术栈")
    @PostMapping("/techStack/list")
    public BaseResult<List<String>> techStackList(@RequestBody(required = false) ApplicationProgramDTO applicationProgramDTO) {
        List<String> techStackList = applicationProgramService.listTechStack(applicationProgramDTO);
        return BaseResult.ok(techStackList);
    }

    @GetMapping("/pipelines")
    @Operation(summary = "获取流水线列表信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public BaseResult<PageResult<PipelineRespVO>> getPipelines(@RequestParam(value = "programId") Long programId) {
        return BaseResult.ok(applicationProgramService.getPipelines(programId));
    }


    @GetMapping("/deploy")
    @Operation(summary = "流水线执行")
    @Parameter(name = "projectId", description = "应用系统id", required = true)
    @Parameter(name = "pipelineId", description = "流水线id", required = true)
    public BaseResult<JSONObject> deploy(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "pipelineId") String pipelineId) {
        return BaseResult.ok(applicationProgramService.deploy(projectId, pipelineId));
    }


    @GetMapping("/add/user")
    @Operation(summary = "应用程序添加用户")
    @Parameter(name = "programId", description = "应用程序id", required = true)
    @Parameter(name = "projectId", description = "应用系统id", required = true)
    @Parameter(name = "appName", description = "应用程序英文名称", required = true)
    @Parameter(name = "userIds", description = "用户ids", required = true)
    public BaseResult<Boolean> addUserToApp(@RequestParam(value = "programId") Long programId, @RequestParam(value = "projectId") Long projectId, @RequestParam(value = "appName") String appName, @RequestParam(value = "userIds") List<Long> userIds) {
        return BaseResult.ok(applicationProgramService.addUserGrantToApplication(programId, projectId, appName, userIds, Boolean.TRUE));
    }

    @DeleteMapping("/delete/user")
    @Operation(summary = "应用程序删除用户")
    @Parameter(name = "programId", description = "应用程序id", required = true)
    @Parameter(name = "projectId", description = "应用系统id", required = true)
    @Parameter(name = "appName", description = "应用程序英文名称", required = true)
    @Parameter(name = "userIds", description = "用户ids", required = true)
    public BaseResult<Boolean> removeUserToApp(@RequestParam(value = "programId") Long programId, @RequestParam(value = "projectId") Long projectId, @RequestParam(value = "appName") String appName, @RequestParam(value = "userIds") List<Long> userIds) {
        return BaseResult.ok(applicationProgramService.removeUserToApplication(programId, projectId, appName, userIds));
    }

    @GetMapping("/users/{id}")
    @Operation(summary = "查询应用程序下的用户")
    @Parameter(name = "userIds", description = "用户ids", required = true)
    public BaseResult<List<ApplicationUserDTO>> listPersonnelByProgramId(@PathVariable Long id) {
        return BaseResult.ok(applicationProgramService.listPersonnelByProgramId(id));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除应用系统下的所有应用程序")
    public BaseResult<Boolean> deleteAllPrograms(@PathVariable("id") Long id) {
        applicationProgramService.deleteAllPrograms(id);
        return BaseResult.ok();
    }

    @GetMapping("/pipelines/history")
    @Operation(summary = "获取流水线构建执行记录")
    @Parameter(name = "projectCode", description = "DevOps项目编号", required = true, example = "1024")
    @Parameter(name = "pipelineId", description = "流水线编号", required = true, example = "1024")
    public BaseResult<PageResult<DevOpsPipelineHisRespVO>> getPipelinesHistory(
            @RequestParam("projectCode") String projectCode, @RequestParam("pipelineId") String pipelineId) {
        CommonResult<PageResult<DevOpsPipelineHisRespVO>> pipelinesHistory = integrationProvider.getPipelinesHistory(projectCode, pipelineId);
        if (CommonConstants.SUCCESS.equals(pipelinesHistory.getCode())) {
            return BaseResult.ok(pipelinesHistory.getData());
        }
        return BaseResult.failed("获取流水线构建执行记录失败:" + pipelinesHistory.getMsg());
    }

    @GetMapping("/pipelines/logs")
    @Operation(summary = "获取流水线执行记录日志")
    @Parameter(name = "projectCode", description = "DevOps项目编号", required = true, example = "1024")
    @Parameter(name = "pipelineId", description = "流水线编号", required = true, example = "1024")
    @Parameter(name = "buildId", description = "构建编号", required = true, example = "1024")
    public BaseResult<DevOpsPipelineBuildLogRespVO> getPipelinesLogs(@RequestParam("projectCode") String projectCode,
                                                                     @RequestParam("pipelineId") String pipelineId,
                                                                     @RequestParam("buildId") String buildId) {
        CommonResult<DevOpsPipelineBuildLogRespVO> pipelinesLogs = integrationProvider.getPipelinesLogs(projectCode, pipelineId, buildId);
        if (CommonConstants.SUCCESS.equals(pipelinesLogs.getCode())) {
            return BaseResult.ok(pipelinesLogs.getData());
        }
        return BaseResult.failed("获取流水线执行记录日志失败:" + pipelinesLogs.getMsg());
    }


    /**
     * 运营平台页面-应用模板脚手架使用情况统计
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 模板脚手架使用情况统计结果
     */
    @Operation(summary = "模板脚手架使用情况统计", description = "获取各应用模板脚手架的使用数量统计")
    @Parameters({
            @Parameter(name = "startTime", description = "开始时间", example = "2023-01-01 00:00:00"),
            @Parameter(name = "endTime", description = "结束时间", example = "2023-12-31 23:59:59")
    })
    @GetMapping("/dashboard/scaffold-template")
    public BaseResult<ScaffoldTemplateUseInfoRespDTO> getScaffoldTemplateInfoRank(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {
        ScaffoldTemplateUseInfoRespDTO scaffoldTemplateInfoRank = applicationProgramService.getScaffoldTemplateInfoRank(startTime, endTime);
        return BaseResult.ok(scaffoldTemplateInfoRank);

    }

    /**
     * 运营平台页面-技术栈使用情况统计
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 技术栈使用情况统计结果
     */
    @Operation(summary = "技术栈使用情况统计", description = "获取各技术栈的使用数量统计，包括总数量、前后端数量及各技术栈排名")
    @Parameters({
            @Parameter(name = "startTime", description = "开始时间", example = "2023-01-01 00:00:00"),
            @Parameter(name = "endTime", description = "结束时间", example = "2023-12-31 23:59:59")
    })
    @GetMapping("/dashboard/tech-stack-rank")
    public BaseResult<TechStackRankRespDTO> getTechStackRank(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {
        TechStackRankRespDTO techStackRank = applicationProgramService.getTechStackRank(startTime, endTime);
        return BaseResult.ok(techStackRank);
    }


    @Operation(summary = "获取用户Git提交统计信息", description = "根据项目ID、开始时间和结束时间，获取用户在GitLab上的提交统计信息")
    @Parameters({
            @Parameter(name = "startTime", description = "开始时间，格式：yyyy-MM-dd HH:mm:ss", required = false),
            @Parameter(name = "endTime", description = "结束时间，格式：yyyy-MM-dd HH:mm:ss", required = false),
            @Parameter(name = "projectId", description = "项目ID", required = true)
    })
    @GetMapping("/dashboard/user-git-commit")
    public BaseResult<List<GitlabCommitInfoDTO>> getUserGitCommit(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime, @RequestParam Long projectId) {
        return BaseResult.ok(applicationProgramService.getUserGitCommit(startTime, endTime, projectId));
    }

    @Operation(summary = "获取用户系统下的提交统计信息", description = "根据用户名、系统ID、开始时间和结束时间，获取用户在GitLab上的提交统计信息")
    @Parameters({
            @Parameter(name = "startTime", description = "开始时间，格式：yyyy-MM-dd HH:mm:ss", required = false),
            @Parameter(name = "endTime", description = "结束时间，格式：yyyy-MM-dd HH:mm:ss", required = false),
            @Parameter(name = "username", description = "用户名AD", required = true),
            @Parameter(name = "id", description = "用户ID", required = true)
    })
    @GetMapping("/dashboard/user-sys-commit")
    public BaseResult<List<ProjectCommitInfoDTO>> getUserSysCommit(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime, @RequestParam String username, @RequestParam Long id) {
        return BaseResult.ok(applicationProgramService.getUserSysCommit(startTime, endTime, username, id));
    }
}
