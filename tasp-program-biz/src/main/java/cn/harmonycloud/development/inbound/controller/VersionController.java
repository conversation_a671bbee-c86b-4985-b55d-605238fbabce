package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.promotion.ProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.feature.FeatureIssuesDto;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.pojo.entity.MajorVersion;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.PromotionNodeInstance;
import cn.harmonycloud.development.pojo.vo.repository.ProductVo;
import cn.harmonycloud.development.pojo.vo.repository.PromotionNodeInstanceVo;
import cn.harmonycloud.development.pojo.vo.version.*;
import cn.harmonycloud.development.service.VersionService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import cn.harmonycloud.pojo.version.MajorVersionDto;
import cn.harmonycloud.pojo.version.VersionDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.lang.annotation.Retention;
import java.util.List;

/**
 * @Description 上机版本
 * <AUTHOR>
 * @Date 2022/8/00
 **/
@Validated
@Api(tags = "上机版本管理")
@RequestMapping("/version")
@RestController
public class VersionController {

    @Autowired
    private VersionService versionService;

    @OperationAudit(operationName = "创建新版本, 版本：${versionNumberTotal}",
            operationCode = "研发协同-版本",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0].subSystemId"),
                    @DynOperationValue(key = "versionNumberTotal", jstl = "$[0].versionNumberTotal")
            })
    @ApiOperation("创建大版本")
    @PostMapping("/createVersion")
    public BaseResult<MajorVersion> createVersion(@Valid @RequestBody CreateVersionRequest request){
        return BaseResult.ok(versionService.createVersion(request));
    }

    @ApiOperation("子系统工作台-上机版本列表")
    @PostMapping("/myVersionList/{subSystemId}")
    public BaseResult myVersionList(@PathVariable("subSystemId") Long subSystemId) {
        return BaseResult.ok(versionService.myVersion(subSystemId));
    }


    @OperationAudit(operationName = "版本变更, 子版本：${subVersionNumber}",
            operationCode = "研发协同-版本",
            operationId = "${subVersionId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subVersionId", jstl = "$[0].subVersionId"),
                    @DynOperationValue(key = "subVersionNumber", jstl = "$[0].subVersionNumber")
            })
    @ApiOperation("版本变更")
    @PostMapping("/versionIteration")
    public BaseResult<VersionManagement> versionIteration(@Valid @RequestBody VersionIterationRequest request) {
        return BaseResult.ok(versionService.versionIteration(request));
    }

    @ApiOperation("预变更")
    @GetMapping("/preIteration")
    public BaseResult<VersionManagement> preIteration(@RequestParam Long subVersionId){
        return BaseResult.ok(versionService.preIteration(subVersionId));
    }

    @ApiOperation("大版本分页列表")
    @GetMapping("/page")
    public BaseResult<Page<MajorVersionDto>> page(@Valid MajorVersionQuery request) {
        return BaseResult.ok(versionService.page(request));
    }

    @ApiOperation("最近一次版本")
    @GetMapping("/last")
    public BaseResult<VersionDetails> last(@RequestParam Long subsystemId) {
        VersionDetails versionDetails = versionService.last(subsystemId);
        return BaseResult.ok(versionDetails);
    }

    @ApiOperation("大版本列表")
    @GetMapping("/list")
    public BaseResult<List<MajorVersionDto>> list(VersionGeneralListRequest request) {
        List<MajorVersionDto> data = versionService.list(request);
        return BaseResult.ok(data);
    }


    @ApiOperation("子版本详情")
    @GetMapping("/details")
    public BaseResult<VersionDetails> details(@RequestParam Long id) {
        return BaseResult.ok(versionService.details(id));
    }

    @ApiOperation("子版本详情")
    @GetMapping("/versionNum")
    public BaseResult<MajorVersion> findByVersionNum(@RequestParam Long subSystemId,@RequestParam String version) {
        return BaseResult.ok(versionService.findByVersionNum(subSystemId,version));
    }

    @ApiOperation("版本特性列表")
    @GetMapping("/featureList")
    public BaseResult<List<FeatureIssuesDto>> featureQuery(@RequestParam Long id) {
        return BaseResult.ok(versionService.featureQuery(id));
    }

    @ApiOperation("版本列表（测试环境管理模块）")
    @GetMapping("/list/{subSystemId}")
    public BaseResult<List<VersionDetailsVo>> list(@PathVariable Long subSystemId) {
        return BaseResult.ok(versionService.getVersionAll(subSystemId));
    }

    @ApiOperation("版本制品列表")
    @GetMapping("/productList")
    public BaseResult<List<DevopsProductMetadataDto>> productList(@RequestParam Long id,
                                                                  @RequestParam(required = false) String format) {
        return BaseResult.ok(versionService.productList(id, format));
    }

    @ApiOperation("部署配置列表")
    @GetMapping("/deployConfig")
    public BaseResult<VersionDeployVo> deployConfig(@RequestParam Long id) {
        return BaseResult.ok(versionService.deployConfig(id));
    }

    @OperationAudit(operationName = "版本变更-部署配置变更",
            operationCode = "研发协同-版本",
            operationId = "${versionId}",
            dynOperationValues = {
                    @DynOperationValue(key = "versionId", jstl = "$[0].versionId")
            })
    @ApiOperation("保存部署配置列表")
    @PostMapping("/deployConfig")
    public BaseResult<VersionDeployConfig> saveDeployConfig(@RequestBody VersionDeployConfig config) {
        versionService.saveDeployConfig(config);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "版本变更-移除信息",
            operationCode = "研发协同-版本",
            operationId = "${versionId}",
            dynOperationValues = {
                    @DynOperationValue(key = "versionId", jstl = "$[0].versionId")
            })
    @ApiOperation("版本变更-移除信息")
    @PostMapping("/removeVersionComponents")
    public BaseResult removeDeployConfig(@Valid @RequestBody RemoveVersionConfig versionConfig) {
        versionService.removeDeployConfig(versionConfig);
        return BaseResult.ok();
    }

    @ApiOperation("版本特性添加")
    @OperationAudit(operationName = "版本特性添加",
            operationCode = "研发协同-版本",
            operationId = "${versionId}",
            dynOperationValues = {
                    @DynOperationValue(key = "versionId", jstl = "$[0].versionId")
            })
    @PostMapping("/featureAdd")
    public BaseResult featureAdd(@Valid @RequestBody VersionFeatureModify modify) {
        versionService.featureAdd(modify);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "修改版本信息",
            operationCode = "研发协同-版本",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0].id")
            })
    @ApiOperation("修改版本信息")
    @PostMapping("/updateVersion")
    public BaseResult updateVersion(@Valid @RequestBody UpdateVersionVo request) {
        versionService.updateVersion(request);
        return BaseResult.ok();
    }

    @ApiOperation("修改大版本信息")
    @PostMapping("/updateMajorVersion")
    public BaseResult updateMajorVersion(@Valid @RequestBody UpdateMajorVersionVo request) {
        versionService.updateMajorVersion(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "删除子版本, subSystemId：${subSystemId}",
            operationCode = "研发协同-版本",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0].subSystemId")
            })
    @ApiOperation("删除子版本")
    @PostMapping("/deleteVersion")
    public BaseResult deleteVersion(@RequestBody DeleteVersionVo request) {
        versionService.deleteVersion(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "删除主版本",
            operationCode = "研发协同-版本",
            operationId = "${majorVersionId}",
            dynOperationValues = {
                    @DynOperationValue(key = "majorVersionId", jstl = "$[0].majorVersionId")
            })
    @ApiOperation("删除主版本")
    @PostMapping("/deleteMajorVersion")
    public BaseResult deleteMajorVersion(@RequestBody DeleteMajorVersionReq request) {
        versionService.deleteMajorVersion(request);
        return BaseResult.ok();
    }

    @ApiOperation("选择版本,获取版本信息")
    @PostMapping("/selectVersion")
    public BaseResult<VersionVo> selectVersion(@RequestBody SelectVersionVo request) {
        return BaseResult.ok(versionService.selectVersion(request));
    }

    @ApiOperation("系统版本列表")
    @GetMapping("/systemVersionList")
    public BaseResult<List<DevopsSystemDto>> systemVersionList(VersionSubsystemQuery query) {
        return BaseResult.ok(versionService.systemVersionList(query));
    }

    @ApiOperation(("大版本下的子版本列表"))
    @GetMapping("/versionList/{majorVersionId}")
    public BaseResult<List<VersionDto>> versionList(@PathVariable Long majorVersionId){
        return BaseResult.ok(versionService.versionList(majorVersionId));
    }

    /**
     * 为保证至少有一个子版本状态为开启，只能更改子版本状态为开启状态
     * @param request
     * @return
     */
    @ApiOperation("更改子版本号状态")
    @PostMapping("/changeVersionStatus")
    public BaseResult changeVersionStatus(@Valid @RequestBody ChangeVersionSwitchStatusRequest request){
        versionService.changeVersionStatus(request);
        return BaseResult.ok();
    }

    @ApiOperation("大版本列表")
    @GetMapping("/majorVersionList/{subSystemId}")
    public BaseResult<List<MajorVersionDto>> majorVersionList(@PathVariable Long subSystemId){
        List<MajorVersionDto> majorVersionDtoList = versionService.majorVersionList(subSystemId);
        return BaseResult.ok(majorVersionDtoList);
    }

    @ApiOperation("未关联环境列表")
    @GetMapping ("/envList")
    public BaseResult<List<String>> envList(@RequestParam("configId") Long configId ,
                                            @RequestParam("versionId") Long versionId){
        List<String> envList  = versionService.envList(configId,versionId);
        return BaseResult.ok(envList);
    }

    @ApiOperation("查询制品晋级实例")
    @GetMapping("/listPromotionInstance")
    public BaseResult<List<ProductVo>> listProduct(@RequestParam Long versionId){
        List<ProductVo>  productVoList= versionService.listProduct(versionId);
        return BaseResult.ok(productVoList);
    }

    @ApiOperation("查询制品上传信息")
    @GetMapping("/getUploadProductInfo")
    public BaseResult<UploadVersionProductVo> getUploadProductInfo(@RequestParam Long versionId){
        UploadVersionProductVo result = versionService.getUploadProductInfo(versionId);
        return BaseResult.ok(result);
    }

    @ApiOperation("制品上传信息校验")
    @PostMapping("/checkUploadProductInfo")
    public BaseResult<UploadVersionProductCheckRsp> checkUploadProductInfo(@RequestBody UploadVersionProductCheck check){
        UploadVersionProductCheckRsp result = versionService.checkUploadProductInfo(check);
        return BaseResult.ok(result);
    }

    @ApiOperation("查询开启子版本详情")
    @GetMapping("/openedDetails")
    public BaseResult<VersionDetails> openedDetails(@RequestParam Long majorVersionId){
        return BaseResult.ok(versionService.openedDetails(majorVersionId));
    }


}