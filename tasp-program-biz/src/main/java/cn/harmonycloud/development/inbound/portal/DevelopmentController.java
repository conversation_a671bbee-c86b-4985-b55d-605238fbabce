package cn.harmonycloud.development.inbound.portal;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.constants.FeatureConstance;
import cn.harmonycloud.development.outbound.SubsystemRepository;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.development.pojo.dto.development.*;
import cn.harmonycloud.development.pojo.dto.feature.FeatureTaskDTO;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.vo.project.ProjectDto;
import cn.harmonycloud.development.pojo.vo.system.SubSystemDataVO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.service.CodeRepoService;
import cn.harmonycloud.development.service.DevelopmentService;
import cn.harmonycloud.development.service.FeatureService;
import cn.harmonycloud.development.service.mapstruct.DevopsSubSystemMapstruct;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 研发工作台接口
 * <AUTHOR>
 * @Date 2023/2/10 2:32 下午
 **/
@Api(tags = "研发工作台接口")
@RestController
@RequestMapping("/development")
public class DevelopmentController {

    @Autowired
    private DevelopmentService developmentService;
    @Autowired
    private FeatureService featureService;
    @Autowired
    private CodeRepoService codeRepoService;
    @Autowired
    private DevopsSubSystemMapstruct devopsSubSystemMapstruct;
    @Autowired
    private SubsystemRepository subsystemRepository;

    /**
     * 特性列表（过滤本环境已集成的特性列表）
     * @param requestVo
     * @return
     */
    @ApiOperation("特性列表（过滤本环境已集成的特性列表）")
    @GetMapping("/feature")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "branch", value = "分支名称"),
            @ApiImplicitParam(name = "featureStatus", value = "特性状态"),
            @ApiImplicitParam(name = "director", value = "负责人id"),
            @ApiImplicitParam(name = "featureName", value = "特性名称")
    })
    public BaseResult<List<FeatureTaskDTO>> feature(QueryFeatureRequest requestVo) {
        List<FeatureTaskDTO> records = developmentService.featureQuery(requestVo);
        return BaseResult.ok(records);
    }

    /**
     * 特性列表（过滤本环境已集成的特性列表）
     * @param requestVo
     * @return
     */
    @ApiOperation("特性列表（过滤本环境已集成的特性列表）")
    @GetMapping("/featurePage")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stageEnvId", value = "阶段环境id"),
            @ApiImplicitParam(name = "branch", value = "分支名称"),
            @ApiImplicitParam(name = "featureStatus", value = "特性状态"),
            @ApiImplicitParam(name = "director", value = "负责人id"),
            @ApiImplicitParam(name = "featureName", value = "特性名称"),
            @ApiImplicitParam(name = "projectId", value = "项目id")
    })
    public BaseResult<Page<FeatureTaskDTO>> featurePage(QueryFeatureRequest requestVo) {
        //调用featurepage方法，获取records
        if(requestVo.getStageEnvId() == null){
            return BaseResult.ok(new Page<>(requestVo.getPageNo(), requestVo.getPageSize()));
        }
        Page<FeatureTaskDTO> records = developmentService.featurePage(requestVo);
        return BaseResult.ok(records);
    }



    /**
     * 集成特性列表
     * @param stageEnvId
     * @return
     */
    @ApiOperation("集成特性列表")
    @GetMapping("/featureIntegration")
    public BaseResult<List<FeatureTaskDTO>> featureIntegration(@ApiParam(name="stageEnvId", value = "阶段环境id") @RequestParam Long stageEnvId,
                                                               @ApiParam(name="versionId", value = "版本id") @RequestParam(required = false) Long versionId) {
        return BaseResult.ok(developmentService.featureIntegration(stageEnvId, versionId));
    }

    /**
     * 开始构建
     * @param request
     * @return
     */
    @ApiOperation("开始构建")
    @PostMapping("/pipelineBuild")
    public BaseResult pipelineBuild(@Validated @RequestBody PipelineBuildRequest request) {
        developmentService.pipelineBuild(request);
        return BaseResult.ok();
    }

    /**
     * 开始构建
     * @param request
     * @return
     */
    @ApiOperation("流水线重试")
    @PostMapping("/replay")
    public BaseResult pipelineReplay(@Validated @RequestBody PipelineReplayRequest request) {
        developmentService.pipelineReplay(request);
        return BaseResult.ok();
    }

    /**
     * 获取研发工作台启动变量
     *
     * @param jobId
     * @return
     */
    @ApiOperation("获取研发工作台启动变量")
    @GetMapping("/runStartParams")
    public BaseResult<List<JenkinsFileStartParameter>> runStartParams(@RequestParam Long jobId,
                                                                      @RequestParam(required = false) Long stageEnvId,
                                                                      @RequestParam(required = false) Long versionId,
                                                                      @RequestParam(required = false) Boolean lastFlag) {
        List<JenkinsFileStartParameter> startParameters = developmentService.runStartParams(jobId, stageEnvId, versionId, lastFlag);
        return BaseResult.ok(startParameters);
    }

    /**
     * 构建详情
     * @param stageEnvId 阶段环境id
     * @param stageEnvId jobId
     * @return
     */
    @ApiOperation("构建详情")
    @GetMapping("/buildDetail")
    public BaseResult pipelineBuildInfo(@ApiParam(name = "阶段环境id",value = "stageEnvId") @RequestParam Long stageEnvId,
                                        @ApiParam(name = "流水线jobId",value = "jobId")@RequestParam Long jobId,
                                        @ApiParam(name = "版本id",value = "versionId")@RequestParam(required = false) Long versionId) {
        return BaseResult.ok(developmentService.pipelineBuildInfo(stageEnvId, jobId, versionId));
    }

    /**
     * 自测通过
     * @param featureId 特性id
     * @return
     */
    @ApiOperation("自测通过")
    @PostMapping("/passTest/{featureId}")
    public BaseResult passTest(@PathVariable Long featureId) {
        featureService.updateStatus(featureId, FeatureConstance.Status.DEV_SUS);
        return BaseResult.ok();
    }

    /**
     * 添加集成
     * @param request 特性id
     * @return
     */
    @ApiOperation("添加集成")
    @PostMapping("/addIntegration")
    public BaseResult addIntegration(@RequestBody StageEnvFeatureRequest request) {
        developmentService.addIntegration(request);
        return BaseResult.ok();
    }

    /**
     * 添加集成
     * @param subsystemId 子系统id
     * @param branch 分支
     * @return
     */
    @ApiOperation("差异比对")
    @GetMapping("/branchStage")
    public BaseResult<BranchStageDto> branchStage(@RequestParam Long subsystemId,
                                  @RequestParam String branch) {
        BranchStageDto branchStageDto = codeRepoService.branchStage(subsystemId, branch);
        return BaseResult.ok(branchStageDto);
    }

    /**
     * 移除集成
     * @param request 特性id
     * @return
     */
    @ApiOperation("移除集成")
    @PostMapping("/removeIntegration")
    public BaseResult removeIntegration(@RequestBody StageEnvFeatureRequest request) {
        developmentService.removeIntegration(request);
        return BaseResult.ok();
    }

    /**
     * 发起检修
     * @param request
     * @return
     */
    @ApiOperation("发起检修")
    @GetMapping("/online")
    public BaseResult<OnlineDetails> online(OnlineRequest request) {
        OnlineDetails online = developmentService.online(request);
        return BaseResult.ok(online);
    }

    /**
     * 检修详情
     * @param orderId
     * @return
     */
    @ApiOperation("检修详情")
    @GetMapping("/onlineDetails")
    public BaseResult<OnlineDetails> onlineDetails(@ApiParam(name = "检修单id",value = "orderId") @RequestParam Long orderId) {
        OnlineDetails detail = developmentService.onlineDetails(orderId);
        return BaseResult.ok(detail);
    }

    /**
     * 检修列表分页
     * @param request
     * @return
     */
    @ApiOperation("检修列表分页")
    @GetMapping("/onlinePage")
    public BaseResult<Page<OnlineDto>> onlinePage(OnlineQuery request) {
        Page<OnlineDto> page = developmentService.onlinePage(request);
        return BaseResult.ok(page);
    }

    /**
     * 保存检修单
     * @param request
     * @return
     */
    @ApiOperation("保存检修单")
    @PostMapping("/onlineSave")
    public BaseResult onlineSave(@RequestBody OnlineCreateRequest request) {
        developmentService.onlineSave(request);
        return BaseResult.ok();
    }

    /**
     * 子系统列表
     * @param systemId
     * @return
     */
    @ApiOperation("子系统列表")
    @GetMapping("/subsystemList")
    public BaseResult<List<SubSystemDataVO>> subsystemList(@ApiParam(name = "系统id", value = "systemId")@RequestParam(required = false) Long systemId) {
        List<DevopsSubSystem> devopsSubSystems = subsystemRepository.listBySystemId(systemId);
        List<SubSystemDataVO> collect = devopsSubSystems.stream().map(subSystem -> devopsSubSystemMapstruct.doToSystemData(subSystem)).collect(Collectors.toList());
        return BaseResult.ok(collect);
    }

    /**
     * （任务列表）检修内容
     * @param buildInstanceId
     * @return
     */
    @ApiOperation("（任务列表）检修内容")
    @GetMapping("/onlineIssues")
    public BaseResult<List<ProjectDto>> onlineSave(@ApiParam(name = "构建实例id", value = "buildInstanceId")@RequestParam Long buildInstanceId) {
        return BaseResult.ok(developmentService.onlineIssues(buildInstanceId));
    }

    /**
     * 冲突解决方案
     * @param buildId
     * @return
     */
    @ApiOperation("冲突解决方案")
    @GetMapping("/conflictPlan")
    public BaseResult<ConflictPlanDto> conflictPlan(@ApiParam(name = "构建id", value = "buildId")@RequestParam Long buildId) {
        return BaseResult.ok(developmentService.conflictPlan(buildId));
    }

    /**
     * 撤销检修单
     * @param id
     * @return
     */
    @ApiOperation("撤销检修单")
    @GetMapping("/onlineCancel")
    public BaseResult onlineCancel(@ApiParam(name = "检修单id", value = "id")@RequestParam Long id) {
        developmentService.onlineCancel(id);
        return BaseResult.ok();
    }

    /**
     * 删除检修单（逻辑删除）
     * @param id
     * @return
     */
    @ApiOperation("删除检修单")
    @PostMapping("/onlineDel/{id}")
    public BaseResult onlineDel(@ApiParam(name = "检修单id", value = "id") @PathVariable Long id) {
        developmentService.onlineDel(id);
        return BaseResult.ok();
    }

    /**
     * 查询最近一次构建信息
     * @param stageEnvId
     * @return
     */
    @ApiOperation("查询最近一次构建信息")
    @GetMapping("/lastBuildInstance")
    public BaseResult<BuildInstanceDto> lastBuildInstance(@ApiParam(name = "阶段环境id", value = "stageEnvId") @RequestParam Long stageEnvId,
                                                          @ApiParam(name = "大版本id", value = "versionId") @RequestParam(required = false) Long versionId) {
        BuildInstanceDto buildInstanceDto = developmentService.lastBuildInstance(stageEnvId, versionId);
        return BaseResult.ok(buildInstanceDto);
    }


}
