package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.JobRepository;
import cn.harmonycloud.development.pojo.vo.pipeline.*;
import cn.harmonycloud.development.service.PipelineService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/25 4:36 下午
 **/
@Api(tags = "流水线相关接口")
@RequestMapping("/pipeline")
@RestController
@Validated
public class PipelineController {

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private JobRepository jobRepository;

    @ApiOperation("获取子系统下所有流水线")
    @GetMapping("/{subSystemId}/all")
    public BaseResult<List<PipelineVo>> createIssues(@PathVariable Long subSystemId,
                                                     @RequestParam(required = false) String envCode,
                                                     @RequestParam(required = false) String draftStatus){
        return BaseResult.ok(pipelineService.getPipelineBySystemId(subSystemId, envCode, draftStatus));
    }

    @OperationAudit(operationName = "在子系统下创建一条流水线，subSystemId：${subSystemId}",
            operationCode = "研发协同-流水线",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0].subSystemId")
            })
    @ApiOperation("在子系统下创建一条流水线")
    @PostMapping("/create")
    public BaseResult create(@RequestBody PipelineCreateRequest request){
        return BaseResult.ok(pipelineService.createPipeline(request));
    }

    @ApiOperation("在子系统下创建一条流水线")
    @PostMapping("/createBinding")
    public BaseResult createBinding(@Valid @RequestBody PipelineBindingRequest request){
        pipelineService.createPipelineBinding(request);
        return BaseResult.ok();
    }

    @ApiOperation("在子系统下取消关联流水线")
    @OperationAudit(operationName = "取消关联流水线",
            operationCode = "研发协同-流水线",
            operationId = "${jobId}",
            dynOperationValues = {
                    @DynOperationValue(key = "jobId", jstl = "$[0].jobId")
            })
    @PostMapping("/unBind")
    public BaseResult unBind(@Valid @RequestBody PipelineBindingRequest request){
        pipelineService.unBind(request);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "取消流水线，jobId：${jobId}",
            operationCode = "研发协同-流水线",
            operationId = "${jobId}",
            dynOperationValues = {
                    @DynOperationValue(key = "jobId", jstl = "$[0]")
            })
    @ApiOperation("取消流水线")
    @PostMapping("/stopBuild/{jobId}")
    public BaseResult stopBuild(@PathVariable Long jobId){
        jobRepository.stopBuild(jobId);
        return BaseResult.ok();
    }

    @ApiOperation("最近一次流水线详情")
    @GetMapping("/{jobId}/builds/recent")
    public BaseResult recent(@PathVariable Long jobId){
        return BaseResult.ok(jobRepository.getRecentBuildByJobId(jobId));
    }

    @ApiOperation("流水线分页列表")
    @GetMapping("/listByPage")
    public BaseResult listByPage( PipelinePageRequest request){
        return BaseResult.ok(pipelineService.listByPage(request));
    }

    @ApiOperation("批量关联流水线")
    @PostMapping("/relevance")
    public BaseResult relevance(@RequestBody RelevanceJobsRequest request){
        pipelineService.relevanceJobs(request);
        return BaseResult.ok();
    }

}
