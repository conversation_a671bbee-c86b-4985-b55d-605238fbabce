package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.web.WebFile;
import cn.harmonycloud.development.service.SystemFileService;
//import cn.harmonycloud.minio.config.MinioProperties;
//import cn.harmonycloud.minio.service.FileTemplate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

@RestController
@Api("通用文件管理")
@RequestMapping("/file")
public class FileController {

//    @Resource
//    private FileTemplate fileTemplate;
//
//    @Resource
//    private MinioProperties minioProperties;
    @Autowired
    private SystemFileService systemFileService;

    @Value("${minio.bucket-name}")
    private String bucketName;

    /**
     * 文件上传（弃用）
     * @param file
     * @return
     */
    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public BaseResult<?> upload(MultipartFile file) {
        try {
            String fileName = UUID.randomUUID().toString();
          //  fileTemplate.putObject(bucketName, fileName, file.getInputStream(), file.getSize(), file.getContentType());
           // String url = minioProperties.getUrl() + "/" + bucketName + "/" + fileName;
            String url ="";
            return BaseResult.ok(url);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResult.failed(e);
        }
    }

    @OperationAudit(operationName = "文件-上传",
            operationCode = "研发协同-文件"
    )
    @PostMapping(value = "/uploadSubject", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传制品")
    public BaseResult upload(@Schema(description = "文件", required = true) MultipartFile file,
                             @Schema(description = "文件分类") String subject) throws IOException {
        WebFile webFile = systemFileService.upload(file, subject);
        return BaseResult.ok(webFile);
    }

    @GetMapping(value = "/download")
    @Operation(summary = "文件下载")
    public void download(@RequestParam Long id,
                         HttpServletResponse response) throws IOException {
        systemFileService.download(id,response);
    }


}
