package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.ProjectRepository;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectManagementDto;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@Api("项目管理接口")
@RequestMapping("/projectManagement")
public class ProjectManagementController {

    @Autowired
    private ProjectRepository projectRepository;

    /**
     * 项目列表-（下拉框）
     * @return
     */
    @GetMapping("/list/local")
    public BaseResult<List<ProjectManagementDto>> localProject(@RequestParam Long subSystemId) {
        //List<ProjectManagement> result = projectManagementService.localProject(subSystemId);
        List<ProjectManagementDto> byUser = projectRepository.getByUser();
        return BaseResult.ok(byUser);
    }

    /**
     * 项目列表-（下拉框）
     * @return
     */
    @GetMapping("/list/project")
    public BaseResult<List<ProjectManagementDto>> remoteProject() {
        List<ProjectManagementDto> byUser = projectRepository.getByUser();
        return BaseResult.ok(byUser);
    }
}
