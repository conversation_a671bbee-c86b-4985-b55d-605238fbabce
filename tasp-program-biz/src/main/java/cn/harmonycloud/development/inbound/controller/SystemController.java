package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.DevopsSystemRepository;
import cn.harmonycloud.development.pojo.dto.system.DevopsSystemDto;
import cn.harmonycloud.development.pojo.dto.system.SystemCreateRequest;
import cn.harmonycloud.development.pojo.dto.system.SystemPageRequest;
import cn.harmonycloud.development.pojo.dto.system.SystemUpdateRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.query.MemberQuery;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.development.service.SystemService;
import cn.harmonycloud.development.service.SystemMemberService;
import cn.harmonycloud.development.service.mapstruct.DevopsSystemMapstruct;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import cn.harmonycloud.pmp.model.vo.UserVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 */
@Validated
@Api(tags = "系统管理")
@RequestMapping("/system/")
@RestController
public class SystemController {

    @Autowired
    private SystemService systemService;

    @Autowired
    private SystemMemberService systemMemberService;

    @Autowired
    private DevopsSystemRepository devopsSystemRepository;

    @Autowired
    private DevopsSystemMapstruct devopsSystemMapstruct;

    @ApiOperation("获取系统信息")
    @GetMapping("getById/{id}")
    public BaseResult<SystemDataVO> getById(@ApiParam(value = "系统id") @PathVariable Long id) {
        DevopsSystem devopsSystem = devopsSystemRepository.getById(id);
        SystemDataVO systemDataVO = devopsSystemMapstruct.toSystemDataVO(devopsSystem);
        return BaseResult.ok(systemDataVO);
    }

    @ApiOperation("查询可关联的gitlab分组")
    @GetMapping("groups")
    public BaseResult<List<GroupDto>> groups() {
        return BaseResult.ok(systemService.groups());
    }

    @ApiOperation("获取当前用户所有系统")
    @GetMapping("listSystemByUser")
    public BaseResult<List<SystemDataVO>> listSystemByUser(@RequestParam(required = false) String permissionCode) {
        List<DevopsSystem> systemList = systemService.listSystemByUser(permissionCode);
        List<SystemDataVO> collect = systemList.stream().map(ds -> devopsSystemMapstruct.toSystemDataVO(ds)).collect(Collectors.toList());
        return BaseResult.ok(collect);
    }

    @OperationAudit(operationName = "添加系统成员, 实例：${instanceId}",
            operationCode = "研发协同-系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @ApiOperation("添加系统成员")
    @PostMapping("createMember")
    public BaseResult createMember(@Valid @RequestBody CreateMemberVO req) {
        systemMemberService.createMember(req);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "更新系统成员",
            operationCode = "研发协同-系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @ApiOperation("更新系统成员")
    @PostMapping("modifyMember")
    public BaseResult modifyMember(@Valid @RequestBody ModifyMemberVo req) {
        systemMemberService.modifyMember(req);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "删除系统成员, 实例：${instanceId}",
            operationCode = "研发协同-系统",
            operationId = "${instanceId}",
            dynOperationValues = {
                    @DynOperationValue(key = "instanceId", jstl = "$[0].instanceId")
            })
    @ApiOperation("删除系统成员")
    @PostMapping("deleteMember")
    public BaseResult deleteMember(@RequestBody DeleteMemberVO deleteMemberVO) {
        systemMemberService.deleteMember(deleteMemberVO);
        return BaseResult.ok();
    }

    @ApiOperation("展示系统成员")
    @GetMapping("listMember")
    public BaseResult<Page<ListSystemMemberVO>> listMember(MemberQuery req) {
        return BaseResult.ok(systemMemberService.querySystemMember(req));
    }

    @ApiOperation("查询系统维度角色列表")
    @GetMapping("roles")
    public BaseResult<List<RoleInfoDto>> roles() {
        return BaseResult.ok(systemMemberService.systemRoles());
    }

    @ApiOperation("查询可添加的系统成员")
    @GetMapping("listOverUser")
    public BaseResult<List<UserVo>> listOverUser(@RequestParam Long systemId,
                                                 @RequestParam(required = false) String queryParam) {
        return BaseResult.ok(systemMemberService.listOverUser(systemId, queryParam));
    }

    @ApiOperation("查询当前登录人的系统列表")
    @GetMapping("resource/listAll")
    public BaseResult<List<ListSystemByCurrentVo>> listByCurrent(ListSystemByCurrentRequest request) {
        return BaseResult.ok(systemMemberService.listByCurrent(request));
    }

    @ApiOperation("系统成员特性数统计")
    @GetMapping("{systemId}/statistics")
    public BaseResult<List<SystemStatisticsVo>> statisticsFeature(@PathVariable Long systemId) {
        return BaseResult.ok(systemService.statisticsFeature(systemId));
    }

    @ApiOperation("根据code查询")
    @GetMapping("infoByCode")
    public BaseResult<SystemDataVO> infoByCode(@RequestParam String systemCode) {
        DevopsSystem devopsSystem = systemService.systemCode(systemCode);
        SystemDataVO systemDataVO = devopsSystemMapstruct.toSystemDataVO(devopsSystem);
        return BaseResult.ok(systemDataVO);
    }

    @ApiOperation("系统列表分页")
    @GetMapping("page")
    public BaseResult<Page<DevopsSystemDto>> page(SystemPageRequest query) {
        Page<DevopsSystemDto> result = systemService.page(query);
        return BaseResult.ok(result);
    }

    @OperationAudit(operationName = "创建系统, 名称：${subFullNameCn}",
            operationCode = "研发协同-系统",
            operationId = "${subFullNameCn}",
            dynOperationValues = {
                    @DynOperationValue(key = "subFullNameCn", jstl = "$[0].subFullNameCn")
            })
    @ApiOperation("创建系统")
    @PostMapping("create")
    public BaseResult<Long> create(@Valid @RequestBody SystemCreateRequest request) {
        DevopsSystem devopsSystem = systemService.create(request);
        return BaseResult.ok(devopsSystem.getId());
    }

    @OperationAudit(operationName = "更新系统, 名称：${subFullNameCn}",
            operationCode = "研发协同-系统",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "subFullNameCn", jstl = "$[0].subFullNameCn"),
                    @DynOperationValue(key = "id", jstl = "$[0].id")
            })
    @ApiOperation("编辑系统")
    @PostMapping("modify")
    public BaseResult<DevopsSystem> modify(@Valid @RequestBody SystemUpdateRequest request) {
        DevopsSystem modify = systemService.modify(request);
        return BaseResult.ok(modify);
    }

    @ApiOperation("获取系统详情")
    @GetMapping("details/{id}")
    public BaseResult<SystemDetails> details(@ApiParam(value = "系统id") @PathVariable Long id) {
        return BaseResult.ok(systemService.details(id));
    }

    @OperationAudit(operationName = "删除系统, key：${id}",
            operationCode = "研发协同-系统",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0]")
            })
    @ApiOperation("删除系统")
    @PostMapping("remove/{id}")
    public BaseResult remove(@ApiParam(value = "系统id") @PathVariable Long id) {
        systemService.remove(id);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "置顶、取消置顶",
            operationCode = "研发协同-系统",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0]")
            })
    @ApiOperation("置顶、取消置顶")
    @PostMapping("top")
    public BaseResult top(@RequestBody TopRequest topRequest) {
        systemService.top(topRequest);
        return BaseResult.ok();
    }

    @ApiOperation("系统列表")
    @GetMapping("list")
    public BaseResult<List<DevopsSystemDto>> list(@RequestParam(required = false) Long projectId) {
        List<DevopsSystemDto> list = systemService.list(projectId);
        return BaseResult.ok(list);
    }

    @ApiOperation("关联项目")
    @PostMapping("bind")
    public BaseResult bindProject(@RequestBody SystemProjectRequest systemProjectRequest) {
        systemService.bindProject(systemProjectRequest);
        return BaseResult.ok();
    }

    @ApiOperation("解除关联项目")
    @PostMapping("unbind")
    public BaseResult unbindProject(@RequestBody SystemProjectRequest systemProjectRequest) {
        systemService.unbindProject(systemProjectRequest);
        return BaseResult.ok();
    }

}
