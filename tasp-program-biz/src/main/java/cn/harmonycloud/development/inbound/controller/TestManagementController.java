package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.system.EnvTestDTO;
import cn.harmonycloud.development.pojo.dto.system.EnvTestListDTO;
import cn.harmonycloud.development.pojo.dto.test.*;
import cn.harmonycloud.development.pojo.vo.autotest.ResultItemVO;
import cn.harmonycloud.development.pojo.vo.test.*;
import cn.harmonycloud.development.service.TestManagementService;
//import cn.harmonycloud.minio.config.MinioProperties;
//import cn.harmonycloud.minio.service.FileTemplate;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import cn.harmonycloud.util.LogUtils;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@Api("提测模块")
@RequestMapping("/testManagement")
@Validated
public class TestManagementController {

    @Autowired
    private TestManagementService testManagementService;

//    @Resource
//    private FileTemplate fileTemplate;
//
//    @Resource
//    private MinioProperties minioProperties;

    @Value("${minio.bucket-name:devops-development-svc}")
    private String bucketName;

    /**
     * 测试环境管理，测试记录（环境测试sit/uat）
     */
    @PostMapping("/getEnvTestRecords")
    public BaseResult<Page<EnvTestVO>> getEnvTestRecords(@Valid @RequestBody EnvTestDTO envTestDTO) {

        return BaseResult.ok(testManagementService.getEnvTestRecords(envTestDTO));
    }

    /**
     * 发起测试
     */
    @OperationAudit(operationName = "发起测试, subSystemId：${subSystemId}",
            operationCode = "研发协同-测试",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0].subSystemId")
            })
    @PostMapping("/create")
    public BaseResult create(@RequestBody CreateTestRequest createTestRequest) {
        testManagementService.create(createTestRequest);
        return BaseResult.ok();
    }

    /**
     * 测试环境管理-版本测试记录
     */
    @PostMapping("/getVersionTestRecords")
    public BaseResult<Page<EnvTestVersionVO>> getVersionTestRecords(@Valid @RequestBody EnvTestDTO dto) {
        Page<EnvTestVersionVO> page = testManagementService.getVersionTestRecords(dto);
        return BaseResult.ok(page);
    }



    /**
     * 测试管理-环境测试列表
     */
    @PostMapping("/envTestList")
    public BaseResult envTestList(@RequestBody EnvTestListDTO envTestListDTO) {
        return BaseResult.ok(testManagementService.envTestList(envTestListDTO));
    }

    /**
     * 版本测试列表-状态改变
     */
    @OperationAudit(operationName = "版本测试列表-状态改变, id：${id}",
            operationCode = "研发协同-测试",
            operationId = "${id}",
            dynOperationValues = {
                    @DynOperationValue(key = "id", jstl = "$[0].id")
            })
    @PutMapping("/updateStatus")
    public BaseResult updateStatus(@RequestBody UpdateStatusDTO dto) {

        return BaseResult.ok(testManagementService.updateStatus(dto.getId(), dto.getStatus()));
    }

    /**
     * 版本测试详情-测试结果
     */
    @GetMapping("/resultList")
    public BaseResult<ResultItemVO> resultList(@RequestParam Long id) {
        ResultItemVO vo = testManagementService.resultList(id);
        return BaseResult.ok(vo);
    }

    /**
     * 转派测试人员
     * @return
     */
    @PutMapping("/updateDirector")
    public BaseResult updateDirector(@RequestBody UpdateDirectorDTO dto) {
        return BaseResult.ok(testManagementService.updateDirector(dto.getId(), dto.getDirectorId()));
    }

    @OperationAudit(operationName = "保存测试报告, ids：${ids}",
            operationCode = "研发协同-测试",
            operationId = "${ids}",
            dynOperationValues = {
                    @DynOperationValue(key = "ids", jstl = "$[0]")
            })
    @PostMapping("/saveTestReport")
    public BaseResult saveTestReport(@RequestParam String ids, @RequestParam Integer testResult, @RequestParam String testResultDescription, MultipartFile file) {
        //文件上传
        String url = null;
        try {
            String fileName = UUID.randomUUID().toString();
//            fileTemplate.putObject(bucketName, fileName, file.getInputStream(), file.getSize(), file.getContentType());
//            url = minioProperties.getUrl() + "/" + bucketName + "/" + fileName;
        } catch (Exception e) {
            log.error(LogUtils.throwableExceptionString(e));
            return BaseResult.failed(null, "文件上传失败");
        }
        return BaseResult.ok(testManagementService.saveTestReport(ids, testResult, url, testResultDescription));
    }

    /**
     * 获取任务列表
     */
    @GetMapping("/issues")
    public BaseResult<List<IssuesDTO>> issues(@ApiParam(name = "阶段环境id", value = "stageEnvId") @RequestParam Long stageEnvId,
                                              @ApiParam(name = "jobId", value = "jobId") @RequestParam Long jobId) {
        return BaseResult.ok(testManagementService.issues(stageEnvId, jobId));
    }

    /**
     * 发起测试-获取数据（2。0版本）
     */
    @ApiOperation("发起测试-获取数据（2。0版本）")
    @GetMapping("/preCreate")
    public BaseResult<TestPreCreateResponse> preCreate(TestPreCreateRequest createTestRequest) {
        TestPreCreateResponse response = testManagementService.preCreate(createTestRequest);
        return BaseResult.ok(response);
    }

    /**
     * 发起测试-保存（2。0版本）
     */
    @ApiOperation("发起测试-保存（2。0版本")
    @PostMapping("/save")
    public BaseResult save(@RequestBody TestCreateRequest createTestRequest) {
        testManagementService.save(createTestRequest);
        return BaseResult.ok();
    }

    /**
     * 测试环境管理环境测试详情
     * @param id 体测单id
     */
    @ApiOperation("测试单详情")
    @GetMapping("/detail")
    public BaseResult<TestManagementDetails> detail(@RequestParam Long id) {
        TestManagementDetails details = testManagementService.detail(id);
        return BaseResult.ok(details);
    }

    /**
     * 测试环境管理环境测试详情
     * @param modifyRequest 体测单id
     */
    @ApiOperation("测试单更新")
    @PostMapping("/modify")
    public BaseResult modify(@Valid @RequestBody TestModifyRequest modifyRequest) {
        testManagementService.modify(modifyRequest);
        return BaseResult.ok();
    }

    /**
     * 测试环境管理环境测试详情
     * @param modifyRequest 体测单id
     */
    @ApiOperation("测试单更新")
    @PostMapping("/batchModify")
    public BaseResult batchModify(@Valid @RequestBody TestBatchModifyRequest modifyRequest) {
        testManagementService.batchModify(modifyRequest);
        return BaseResult.ok();
    }


}
