package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.SystemDictRepository;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.system.SystemDictDto;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.service.mapstruct.DevopsSystemMapstruct;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/17 3:09 下午
 **/
@Api(tags = "字典管理")
@RequestMapping("/systemDict")
@RestController
public class SystemDictController {

    @Autowired
    private SystemDictRepository systemDictRepository;
    @Autowired
    private DevopsSystemMapstruct devopsSystemMapstruct;

    @GetMapping("/list")
    public BaseResult<List<SystemDictDto>> deleteMember(@RequestParam(name = "subject") String subject,
                                                        @RequestParam(name = "filter", required = false) Boolean filter) {
        List<SystemDict> systemDictList = systemDictRepository.getByParams(subject);
        List<SystemDictDto> collect = systemDictList.stream().map(dict -> {
            SystemDictDto systemDictDto = devopsSystemMapstruct.toSystemDictDto(dict);
            if(StringUtils.isNotEmpty(dict.getDictCustomizeParam())){
                systemDictDto.setCustomize(JSONObject.parseObject(dict.getDictCustomizeParam()));
            }
            if(StringUtils.equals(SystemConstance.SystemDictSubject.FEATURE_STATUS, subject)){
                systemDictDto.setDictValueInt(Integer.parseInt(dict.getDictValue()));
            }
            return systemDictDto;
        }).collect(Collectors.toList());
        if (filter != null && filter) {
            collect = collect.stream().filter(dict -> {
                JSONObject customize = dict.getCustomize();
                if(customize != null && customize.containsKey("filter") && customize.getBoolean("filter")){
                    return false;
                }
                return true;
            }).collect(Collectors.toList());;
        }
        return BaseResult.ok(collect);
    }

}
