package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.promotion.SavePromotionRelationReq;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeDto;
import cn.harmonycloud.development.pojo.vo.repository.ProductTreeQuery;
import cn.harmonycloud.development.pojo.vo.repository.ProductVersionQuery;
import cn.harmonycloud.development.service.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/12 9:39 上午
 **/
@Validated
@Api(tags = "制品管理")
@RequestMapping("/product")
@RestController
public class ProductController {

    @Autowired
    private ProductService productService;

    @ApiOperation("制品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "systemId", value = "系统id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "subsystemId", value = "子系统id", required = false, paramType = "query"),
            @ApiImplicitParam(name = "envId", value = "环境id", required = false, paramType = "query"),
            @ApiImplicitParam(name = "format", value = "制品格式：raw-通用制品，docker-容器制品", required = false, paramType = "query"),
            @ApiImplicitParam(name = "productName", value = "制品名称", required = false, paramType = "query"),
            @ApiImplicitParam(name = "version", value = "版本号", required = false, paramType = "query")
    })
    @GetMapping("/tree")
    public BaseResult<List<ProductTreeDto>> tree(@Valid ProductTreeQuery request) {
        List<ProductTreeDto> result = productService.tree(request);
        return BaseResult.ok(result);
    }

    @ApiOperation("查询制品列表（流水线启动变量）")
    @GetMapping("/listByVersion")
    public BaseResult<List<DevopsProductMetadataDto>> listByVersion(@RequestParam String versionNumber,
                                                 @RequestParam Long subsystemId,
                                                 @RequestParam(required = false) String format,
                                                 @RequestParam Long repoId) {
        List<DevopsProductMetadataDto> devopsProductMetadataDtos = productService.listByVersion(subsystemId, versionNumber, repoId, format);
        return BaseResult.ok(devopsProductMetadataDtos);
    }

    @ApiOperation("版本列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "systemId", value = "系统id", required = true, paramType = "query")
    })
    @GetMapping("/versionList")
    public BaseResult<List<String>> versionList(ProductVersionQuery query) {
        List<String> result = productService.versionList(query);
        return BaseResult.ok(result);
    }

    @ApiOperation("上传制品保存关联关系")
    @PostMapping("/savaPromotionRelation")
    public BaseResult savaPromotionRelation(@RequestBody SavePromotionRelationReq savePromotionRelationReq) {
        productService.savaPromotionRelation(savePromotionRelationReq);
        return BaseResult.ok();
    }
}
