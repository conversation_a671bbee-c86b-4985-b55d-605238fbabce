package cn.harmonycloud.development.inbound.controller;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.scm.RelateProjectRequest;
import cn.harmonycloud.development.pojo.vo.scm.MyMergeRequestVO;
import cn.harmonycloud.development.pojo.vo.scm.ProjectListVO;
import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.development.service.SubSystemComponentService;
import cn.harmonycloud.pmp.annotation.DynOperationValue;
import cn.harmonycloud.pmp.annotation.OperationAudit;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "子系统构件")
@RequestMapping("/subSystemComponent/")
@RestController
public class SubSystemComponentController {

    @Autowired
    private SubSystemComponentService subSystemComponentService;

    /**
     * 根据子系统id获取gitlab信息
     *
     * @param subSystemId
     * @return
     */
    @PostMapping("/gitlabInfo/{subSystemId}")
    public BaseResult<GitProjectDto> gitlabInfo(@PathVariable("subSystemId") Long subSystemId) {
        return BaseResult.ok(subSystemComponentService.gitlabInfo(subSystemId));
    }


    /**
     * 子系统控制台-评审列表
     *
     * @param subSystemId
     * @param type        0-我创建的，1-我负责的
     * @return
     */
    @PostMapping("/mergeRequest/{subSystemId}/{type}")
    public BaseResult<List<MyMergeRequestVO>> myMergeRequest(@PathVariable("subSystemId") Long subSystemId, @PathVariable("type") Integer type) {
        return BaseResult.ok(subSystemComponentService.myMergeRequest(subSystemId, type));
    }

    @OperationAudit(operationName = "更新关联",
            operationCode = "研发协同-子系统"
    )
    @PostMapping("/updateRelation")
    public BaseResult<GitProjectDto> updateRelation(@RequestBody UpdateRelationRequest request) {
        return BaseResult.ok(subSystemComponentService.updateRelation(request.getOldId(), request.getNewId()));
    }

    @OperationAudit(operationName = "解除关联代码库，id：${gitlabId}",
            operationCode = "研发协同-子系统",
            operationId = "${gitlabId}",
            dynOperationValues = {
                    @DynOperationValue(key = "gitlabId", jstl = "$[0]")
            })
    @DeleteMapping("/deleteRelation/{gitlabId}")
    public BaseResult deleteRelation(@PathVariable("gitlabId") String gitlabId) {
        subSystemComponentService.deleteRelation(gitlabId);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "创建项目，id：${subSystemId}",
            operationCode = "研发协同-子系统",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0]")
            })
    @PostMapping("/createProject/{subSystemId}")
    public BaseResult createProject(@PathVariable("subSystemId") Long subSystemId, @RequestBody ProjectRequest request) {
        subSystemComponentService.createProject(subSystemId, request, true,true);
        return BaseResult.ok();
    }

    @OperationAudit(operationName = "关联项目，id：${subSystemId}",
            operationCode = "研发协同-子系统",
            operationId = "${subSystemId}",
            dynOperationValues = {
                    @DynOperationValue(key = "subSystemId", jstl = "$[0].subSystemId")
            })
    @PostMapping("/relateProject")
    public BaseResult<GitProjectDto> relateProject(@RequestBody RelateProjectRequest request) {
        return BaseResult.ok(subSystemComponentService.relateProject(request, true, true));
    }

    @GetMapping("/getProjectDetail/{subSystemId}")
    public BaseResult<List<GitProjectDto>> getProjectDetail(@PathVariable("subSystemId") String subSystemId) {
        return BaseResult.ok(subSystemComponentService.getProjectDetail(subSystemId));
    }

    @GetMapping(value = {"/getProjectList/{systemId}/{search}", "/getProjectList/{systemId}"})
    public BaseResult<List<ProjectListVO>> getProjectList(@PathVariable("systemId") String systemId, @PathVariable(value = "search", required = false) String search) {
        if (search == null) {
            search = "";
        }
        return BaseResult.ok(subSystemComponentService.getProjectList(systemId, search));
    }

    @PostMapping("/updateProject")
    public BaseResult updateProject(@RequestBody UpdateProjectRequest request) {
        subSystemComponentService.updateProject(request);
        return BaseResult.ok();
    }

}
