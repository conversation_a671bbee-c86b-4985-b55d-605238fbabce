package cn.harmonycloud.development.pojo.dto.system;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 2:31 下午
 **/
@Data
public class SystemPageQuery extends PageQuery {

    private List<Long> ids;

    private String subFullNameCn; // 系统名称

    @ApiModelProperty("系统编码")
    private String sysCode;

    @ApiModelProperty("负责人Id")
    private List<Long> projectDirectorIds;

    @ApiModelProperty("置顶字段")
    private Boolean top;
}
