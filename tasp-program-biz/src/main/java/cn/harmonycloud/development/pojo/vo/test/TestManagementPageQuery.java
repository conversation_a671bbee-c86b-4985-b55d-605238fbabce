package cn.harmonycloud.development.pojo.vo.test;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/7 1:49 下午
 **/
@Data
@ApiModel("提测单分页查询请求实体类")
public class TestManagementPageQuery implements Serializable {

    private static final long serialVersionUID = -8144132462595988091L;
    // 测试单编码  模糊查询
    private String testCode;

    /**
     * 环境测试状态:待测试=1,测试中=2,测试通过=3,测试失败=4
     */
    private Integer testStatus;

//    private String projectName;
    // 系统id列表 null表示查询全部；长度为0表示不查询，直接放回空
    private List<Long> systemIds;

    // 子系统id列表 null表示查询全部；长度为0表示不查询，直接放回空
    private List<Long> subSystemIds;


    private String startTime;


    private String endTime;

    private Integer pageNum = 1;
    private Integer pageSize = 10;
}
