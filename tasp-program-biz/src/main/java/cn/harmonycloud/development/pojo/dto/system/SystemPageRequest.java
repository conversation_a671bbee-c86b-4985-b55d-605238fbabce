package cn.harmonycloud.development.pojo.dto.system;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 2:02 下午
 **/
@Data
@ApiModel("查询系统分页列表通用模型")
public class SystemPageRequest extends PageQuery {

    @ApiModelProperty("标签")
    private List<Long> labels;

    @ApiModelProperty("系统名称")
    private String subFullNameCn;

    @ApiModelProperty("置顶字段")
    private Boolean top;

    @ApiModelProperty("项目id")
    private List<Long> projectIds;

    @ApiModelProperty("系统编码")
    private String sysCode;

    @ApiModelProperty("负责人Id")
    private List<Long> projectDirectorIds;

}
