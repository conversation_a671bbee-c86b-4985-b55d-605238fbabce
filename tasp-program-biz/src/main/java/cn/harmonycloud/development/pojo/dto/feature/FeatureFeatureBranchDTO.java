package cn.harmonycloud.development.pojo.dto.feature;

import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.enums.BranchModelEnum;
import lombok.Data;

import java.io.Serializable;

import static cn.harmonycloud.constants.DevelopmentConstance.StageType.DEV;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/28 10:23 上午
 **/
@Data
public class FeatureFeatureBranchDTO implements Serializable {

    private static final long serialVersionUID = 1297492167548938589L;
    private Long featureId;
    private String featureName;
    private String branchModel;
    private Integer featureStatus;
    private Long projectId;
    private Integer integrationFlag; // 0-否，1-是
    private String projectName;
    private Long directorId;
    private String directorName;
    private String featureCode;
    private String branchName;
    private String devBranch;
    private BranchStageDto branchStage;
    private BranchStageDto devBranchStage;
    private String baseBranch;


    public String getBranch(Integer envType){
        if (envType == DEV && branchModel != null && branchModel.equals(BranchModelEnum.CONTROLLED.getCode())) {
            return devBranch;
        }
        return branchName;
    }
}
