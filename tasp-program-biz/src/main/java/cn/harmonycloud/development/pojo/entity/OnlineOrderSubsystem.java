package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 上机单、系统关联实体
 * <AUTHOR>
 * @Date 2023/2/17 3:37 下午
 **/
@Data
@TableName("online_order_subsystem")
public class OnlineOrderSubsystem {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系统id
     */
    private Long subsystemId;

    /**
     * 上机单id
     */
    private Long orderId;
}
