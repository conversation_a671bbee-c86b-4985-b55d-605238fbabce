package cn.harmonycloud.development.pojo.vo.version;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/13 7:48 下午
 **/
@Data
public class RemoveVersionConfig {

    @NotNull(message = "缺少参数：versionId")
    private Long versionId;

    @NotEmpty(message = "缺少参数：component")
    private String component;

    private List<String> keys;
}
