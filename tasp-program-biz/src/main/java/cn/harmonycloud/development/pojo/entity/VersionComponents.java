package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 版本组成部分
 * <AUTHOR>
 * @Date 2023/7/11 8:33 下午
 **/
@TableName(value ="version_component")
@Data
public class VersionComponents {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long subsystemId;
    private Long versionId;
    private String component;
    private String componentKey;
    private Long createBy;
    private LocalDateTime createTime;
    private Long subordination;

}
