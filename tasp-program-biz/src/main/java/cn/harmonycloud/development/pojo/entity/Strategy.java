package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/22
 */
@TableName(value ="product_promotion_strategy")
@Data
public class Strategy implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -8151239850172768338L;
    /**
     * 策略id
     */
    @TableId(type = IdType.AUTO)
    private Long strategyId;

    /**
     * 子系统id
     */
    private Long subSystemId;

    private String strategyName;

    private String envIdList;

}
