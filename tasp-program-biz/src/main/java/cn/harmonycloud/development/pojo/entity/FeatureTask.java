package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 特性任务关联实体
 * <AUTHOR>
 * @Date 2023/1/9 10:09 上午
 **/
@TableName(value ="feature_task")
@Data
public class FeatureTask {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long subSystemId;
    private Long featureId;
    private String taskKey;

}
