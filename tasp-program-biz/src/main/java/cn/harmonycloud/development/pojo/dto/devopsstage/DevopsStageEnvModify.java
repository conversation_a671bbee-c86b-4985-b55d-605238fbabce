package cn.harmonycloud.development.pojo.dto.devopsstage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/10 11:46 上午
 **/
@Data
@ApiModel("编辑环境实体模型")
public class DevopsStageEnvModify implements Serializable {

    private static final long serialVersionUID = -2594494561985293183L;
    @ApiModelProperty("阶段环境id")
    @NotNull(message = "阶段环境id不能为空")
    private Long id;

    @ApiModelProperty("环境名称")
    @NotEmpty(message = "环境名称不能为空")
    private String envName;

    @ApiModelProperty("环境编码")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,20}$", message = "环境编码必须由1-20位英文字母或数字组成")
    private String stageEnvCode;

    @ApiModelProperty("环境类型：1-主机类型；4-k8s集群")
    @NotNull(message = "环境类型不能为空")
    private Integer deployType;

    @ApiModelProperty("容器资源id")
    private Integer clusterId;

    @ApiModelProperty("容器资源命名空间")
    private String namespace;

    @ApiModelProperty("主机资源id列表，多个以逗号隔开")
    private List<Long> hostId;

    @ApiModelProperty("环境地址")
    private String address;
}
