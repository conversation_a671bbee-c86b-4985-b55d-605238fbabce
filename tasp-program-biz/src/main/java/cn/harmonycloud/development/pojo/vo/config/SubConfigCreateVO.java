package cn.harmonycloud.development.pojo.vo.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/10 4:49 下午
 **/
@Data
@ApiModel("应用配置创建")
public class SubConfigCreateVO {

    @ApiModelProperty("应用id")
    private Long subsystemId;
    /**
     * 为空时新建配置库，不为空关联配置库
     */
    @ApiModelProperty("配置库Id")
    private Long configId;

    @ApiModelProperty("配置库名称")
    private String projectName;

    @ApiModelProperty("配置库简介")
    private String description;
}
