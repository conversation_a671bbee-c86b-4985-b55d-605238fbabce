package cn.harmonycloud.development.pojo.dto.test;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/5 6:48 下午
 **/
@Data
public class TestManagementQuery implements Serializable {

    private static final long serialVersionUID = -8612077040760979517L;
    /**
     * 子系统id
     */
    private Long subSystemId;
    /**
     * 创建人，null表示查询全部
     */
    private Long userId;
    /**
     * 创建开始时间
     */
    private LocalDateTime startTime;
    /**
     * 创建结束时间
     */
    private LocalDateTime endTime;
}
