package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.pmp.model.entity.Tag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/4
 */
@ApiModel("特性列表实体")
@Data
public class DevopsFeatureDto {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("特性名称")
    private String featureName;

    private String featureCode;

    @ApiModelProperty("项目id")
    private Long projectId;

    private Long subSystemId;

    @ApiModelProperty("子系统名称")
    private String subSystemName;

    private Long systemId;

    @ApiModelProperty("系统名称")
    private String systemName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("来源：0：特性任务，1：生产缺陷")
    private String featureType;

    @ApiModelProperty("分支模型")
    private String branchModel;

    @ApiModelProperty("特性状态")
    private String featureStatus;

    @ApiModelProperty("负责人id")
    private String directorId;

    @ApiModelProperty("负责人姓名")
    private String directorName;

    private Integer integrationFlag; // 0-否，1-是

    @ApiModelProperty("完成时间")
    private String completeTime;

    private LocalDateTime createTime;

    private List<Tag> labels;

    private String featureBranch;

    private String featureDevBranch;

    @ApiModelProperty("来源")
    private String sourceRef;

    @ApiModelProperty("来源Cmmit")
    private String sourceCommit;

    @ApiModelProperty("代码库Id")
    private Integer gitlabId;
}
