package cn.harmonycloud.development.pojo.dto.thgn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Gitlab提交统计DTO
 * 用于封装用户提交次数、用户名、项目ID和Git URL
 * <AUTHOR>
 */
@ApiModel(description = "Gitlab提交统计DTO")
@Data
@NoArgsConstructor
public class GitlabCommitCountDTO {
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", example = "张三")
    private String username;

    /**
     * 提交次数
     */
    @ApiModelProperty(value = "提交次数", example = "10")
    private int commitCount;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", example = "1001")
    private String projectId;

    /**
     * 项目Git URL
     */
    @ApiModelProperty(value = "项目Git URL", example = "http://gitlab.example.com/group/project.git")
    private String gitUrl;


    /**
     * 构造函数
     *
     * @param username    用户名
     * @param commitCount 提交次数
     * @param projectId   项目ID
     * @param gitUrl      项目Git URL
     */
    public GitlabCommitCountDTO(String username, int commitCount, String projectId, String gitUrl) {
        this.username = username;
        this.commitCount = commitCount;
        this.projectId = projectId;
        this.gitUrl = gitUrl;
    }
}