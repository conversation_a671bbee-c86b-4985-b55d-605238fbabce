package cn.harmonycloud.development.pojo.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/7/27
 */
@Data
public class SubSystemDataVO {
    private Long id;

    @ApiModelProperty(value = "系统中文全称")
    private String fullNameCn;

    @ApiModelProperty(value = "系统中文简称")
    private String nameCn;

    @ApiModelProperty(value = "语言")
    private String technology;

    @ApiModelProperty(value = "系统英文全称")
    private String name;

    @ApiModelProperty(value = "系统编码")
    private String code;

    @ApiModelProperty(value = "系统简介")
    private String descCn;

    @ApiModelProperty(value = "系统简介")
    private String projectName;

    @ApiModelProperty(value = "开发部门")
    private String techDept;

    @ApiModelProperty(value = "业务部门")
    private String busiDept;

    @ApiModelProperty(value = "负责人")
    private String director;

    @ApiModelProperty(value = "架构师")
    private String architect;

    @ApiModelProperty(value = "来源，0：巨象导入，1：手动创建")
    private Integer source;

    private Long systemId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    private String techDirector;
    private Long techDirectorId;
    private String opsDirector;
    private Long opsDirectorId;

    //private String outCode;
}
