package cn.harmonycloud.development.pojo.vo.test;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/13
 **/
@Data
@ApiModel(value = "环境测试列表返回结果参数")
public class EnvTestVO {

    private Long id;

    @ApiModelProperty("测试单编号")
    private String testCode;

    @ApiModelProperty("提测环境，格式：阶段/环境")
    private String testEnv;

    @ApiModelProperty("提测人id")
    private Long createBy;

    @ApiModelProperty("提测人名称")
    private String createByName;

    @ApiModelProperty("测试负责人id")
    private Long directorId;

    @ApiModelProperty("测试负责人名称")
    private String directorName;

    @ApiModelProperty("提测时间")
    private LocalDateTime createTime;

    @ApiModelProperty("测试状态：待测试=1,测试中=2,测试通过=3,测试失败=4")
    private Integer testStatus;

}
