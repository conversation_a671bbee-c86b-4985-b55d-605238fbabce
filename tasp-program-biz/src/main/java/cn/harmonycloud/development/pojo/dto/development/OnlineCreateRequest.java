package cn.harmonycloud.development.pojo.dto.development;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 7:54 下午
 **/
@Data
@ApiModel("保存检修单实体信息")
public class OnlineCreateRequest {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("审批状态：0-保存草稿；1-发起检修")
    private Integer applyStatus;

    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("构建实例id")
    private Long buildInstanceId;

    @ApiModelProperty("检修名称")
    private String onlineName;

    @ApiModelProperty("检修开始时间：yyyy-MM-dd HH:mm")
    private String onlineStartTime;

    @ApiModelProperty("检修结束时间：yyyy-MM-dd HH:mm")
    private String onlineEndTime;

    @ApiModelProperty("关联子系统列表")
    private List<Long> subsystemIds;

    @ApiModelProperty("影响范围")
    private String influenceScope;

    @ApiModelProperty("简介")
    private String description;

}
