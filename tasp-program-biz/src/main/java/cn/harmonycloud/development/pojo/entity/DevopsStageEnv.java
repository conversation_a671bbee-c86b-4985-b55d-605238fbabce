package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 子系统阶段表
 * <AUTHOR>
 * @Date 2023/2/6 4:29 下午
 **/
@TableName(value ="devops_stage_env")
@Data
public class DevopsStageEnv {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 子系统id
     */
    private Long subsystemId;
    /**
     * 阶段id
     */
    private Long stageDictId;

    private Long stageId;
    /**
     * 环境名称；
     */
    private String envName;
    /**
     * 环境编码
     */
    private String stageEnvCode;
    /**
     * 部署资源类型：1-主机类型；4-k8s集群
     */
    private Integer deployType;
    /**
     * 容器资源；环境管理集群类型资源id
     */
    private Integer clusterId;
    /**
     * 容器资源；环境管理集群类型资源id
     */
    private String namespace;
    /**
     * 主机资源；环境管理主机资源id列表，多个以逗号隔开
     */
    private String hostId;

    /**
     * 环境地址
     */
    private String address;

    private Integer delFlag = 0;
    private Long createBy;
    private LocalDateTime createTime;
    private Long updateBy;
    private LocalDateTime updateTime;

}
