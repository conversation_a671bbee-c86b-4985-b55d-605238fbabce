package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 系统项目关联关系
 *
 * <AUTHOR>
 * @Date 2024/8/14 2:00 下午
 **/
@Data
@TableName("system_project")
public class SystemProject implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系统id
     *
     */
    private Long systemId;

    /**
     * 项目id
     *
     */
    private Long projectId;

    /**
     * 创建人
     *
     */
    private Long createBy;

    /**
     * 创建时间
     *
     */
    private LocalDateTime createTime;

}
