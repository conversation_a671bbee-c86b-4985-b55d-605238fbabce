package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.development.pojo.dto.feature.FeatureBranchDTO;
import cn.harmonycloud.development.pojo.entity.FeatureStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/4
 */
@ApiModel("特性任务详情实体")
@Data
public class FeatureDetailsDto {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("特性名称")
    private String name;

    @ApiModelProperty("创建人名称")
    private String createName;

    @ApiModelProperty("负责人id")
    private Long director;

    @ApiModelProperty("负责人名称")
    private String directorName;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目名称")
    private String subSystemName;

    @ApiModelProperty("特性id")
    private Long branchId;

    @ApiModelProperty("来源：0：特性任务，1：生产缺陷")
    private Integer type;

    @ApiModelProperty("特性受控分支")
    private String branchName;

    @ApiModelProperty("特性开发分支")
    private String branchDevName;

    @ApiModelProperty("特性开发分支")
    private String branchModel;

    @ApiModelProperty("特性状态：0：开发中，1；已完成")
    private Integer status;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("完成时间")
    private String completeTime;

    @ApiModelProperty("特性描述")
    private String desc;

    @ApiModelProperty("子系统id")
    private Long subSystemId;

    @ApiModelProperty("创建时的来源ref")
    private String sourceRef;

    @ApiModelProperty("分支信息")
    private FeatureBranchDTO featureBranch = new FeatureBranchDTO();

    @ApiModelProperty("组织id")
    private String organId;

    private List<FeatureStatus> featureStatus;

    private String systemName;

    private Long serverId;
    private Integer gitlabId;

    private Integer integrationFlag; // 0-否，1-是

}
