package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 子系统表
 * @TableName hzbank_sub_system
 */
@Data
@TableName("devops_sub_system")
public class DevopsSubSystem implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 跟踪事项id
     */
    private Long issuesId;

    /**
     * 子系统中文全称
     */
    private String fullNameCn;

    /**
     * 子系统编码
     */
    private String subCode;

    /**
     * 子系统描述
     */
    private String subDescCn;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 负责人
     */

    private Long techDirectorId;

    /**
     * 语言
     */
    private String technology;

    // 逻辑删除标志位
    private Integer delFlag;
    // 创建人
    private Long createBy;
    // 创建时间
    private LocalDateTime createTime;
    // 更新人
    private Long updateBy;
    // 更新时间
    private LocalDateTime updateTime;
    //组织id
    private Long tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}