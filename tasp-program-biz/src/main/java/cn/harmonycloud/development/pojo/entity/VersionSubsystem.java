package cn.harmonycloud.development.pojo.entity;

import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/27 9:24 上午
 **/
@Data
public class VersionSubsystem {

    private Long versionId;
    private Long subsystemId;
    private Long systemId;
    private String VersionNumber;
    private Long versionDirectorId;
    /**
     * 版本状态：0-未开始；1-开发中；2-测试中；3-已发布
     */
//    private Integer versionStatus;

    private String versionDescription;

    /**
     * 子系统中文全称
     */
    private String fullNameCn;

    /**
     * 子系统编码
     */
    private String subCode;

    /**
     * 子系统描述
     */
    private String subDescCn;

    /**
     * 负责人
     */

    private Long techDirectorId;

    /**
     * 语言
     */
    private String technology;
}
