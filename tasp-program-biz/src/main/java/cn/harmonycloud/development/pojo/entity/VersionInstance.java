package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/22
 */
@TableName(value ="version_instance")
@Data
public class VersionInstance implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上机版本id
     */
    private Long versionId;

    private String subVersionName;

    /**
     * 子系统id
     */
    private Long subSystemId;

    /**
     * 子版本的补丁号
     */
    private Integer subPatchNumber;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 关联流水线id
     */
    private Long pipelineJobId;

    private Long pipelineBuildId;


    private Long createBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
