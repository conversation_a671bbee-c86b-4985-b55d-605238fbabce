package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 检修单实体
 * <AUTHOR>
 * @Date 2023/2/17 3:28 下午
 **/
@Data
@TableName("online_order")
public class OnlineOrder {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 子系统id
     */
    private Long subsystemId;
    /**
     * 构建实例id
     */
    private Long buildInstanceId;
    /**
     * 检修单编码
     */
    private String onlineCode;
    /**
     * 检修单名称
     */
    private String onlineName;
    /**
     * 检修开始时间
     */
    private LocalDateTime startTime;
    /**
     * 检修结束时间
     */
    private LocalDateTime endTime;
    /**
     * 影响范围
     */
    private String influenceScope;
    /**
     * 检修原因
     */
    private String description;
    /**
     * 审批状态：审批状态：0-草稿；1-审批中；2-审批通过；3-已撤回；4-已驳回
     */
    private Integer applyStatus;
    /**
     * 检修状态：1-待检修；2-检修中；3-检修成功；4-检修失败；5-检修取消
     */
    private Integer onlineStatus;
    /**
     * 逻辑删除标志位：0-不删除；1-删除
     */
    private Integer delFlag;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
