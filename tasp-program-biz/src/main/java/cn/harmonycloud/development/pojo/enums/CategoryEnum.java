package cn.harmonycloud.development.pojo.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "开源组件分类枚举")
public enum CategoryEnum {
    @Schema(description = "后端框架")
    BACKEND_FRAMEWORK("后端框架"),
    @Schema(description = "库")
    BACKEND_LIBRARY("后端库"),
    @Schema(description = "中间件")
    MIDDLEWARE("中间件"),
    @Schema(description = "开发或运维工具")
    TOOL("工具"),
    @Schema(description = "业务应用系统")
    APPLICATION("应用"),
    @Schema(description = "前端框架")
    FRONTEND_FRAMEWORK("前端框架"),
    @Schema(description = "前端库")
    FRONTEND_LIBRARY("前端库");

    private final String description;

    CategoryEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}