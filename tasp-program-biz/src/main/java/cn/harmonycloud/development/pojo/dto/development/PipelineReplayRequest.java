package cn.harmonycloud.development.pojo.dto.development;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/31 10:25 上午
 **/
@Data
public class PipelineReplayRequest {

    @ApiModelProperty("阶段环境id")
    @NotNull(message = "环境id不能为空")
    private Long stageEnvId;
    private Long buildId;
    private String runDescribe;
}
