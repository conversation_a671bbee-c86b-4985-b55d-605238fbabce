package cn.harmonycloud.development.pojo.dto.repository;

import cn.harmonycloud.development.outbound.api.enums.RepositoryKindEnum;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/19 7:50 下午
 **/
@Data
public class SystemRepoQuery {

    private Long systemId;
    private String format;
    private String repoName;
    private Integer kind;
    private Long configId;
    private boolean withPublicRepository = false;
    private Boolean withProdRepository;

    public SystemRepoQuery(){

    }

    public SystemRepoQuery(Long systemId){
        this.systemId = systemId;
    }

    public SystemRepoQuery(Long systemId, String format, String repoName){
        this.systemId = systemId;
        this.format = format;
        this.repoName = repoName;
        this.kind = RepositoryKindEnum.ARTIFACT.getType();
    }


    public SystemRepoQuery(Long systemId,  String format){
        this.systemId = systemId;
        this.format = format;
    }

}
