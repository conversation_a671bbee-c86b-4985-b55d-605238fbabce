package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/1 11:14 上午
 **/
@Data
@TableName("system_file")
public class SystemFile implements Serializable {


    private static final long serialVersionUID = -4316967558412925194L;
    private Long id;
    private String fileSubject;
    private String subjectKey;
    private Integer saveType;  // 1-minio
    private String serverUrl;
    private String bucket;
    private String fileKey;
    private String fileName;
    private Long createBy;
    private LocalDateTime createTime;



}
