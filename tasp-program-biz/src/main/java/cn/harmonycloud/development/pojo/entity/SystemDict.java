package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 字典
 * <AUTHOR>
 * @Date 2022/8/23 10:03 上午
 **/
@TableName("system_dict")
@Data
public class SystemDict {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String subject; // 字典分类
    private String dictCode; // 字典编码
    private String dictName; // 名称
    private String dictType; // 类型
    private String dictValue; // 值
    private Integer dictSort; // 排序
    private String description; // 简介
    private String dictCustomizeParam;

}
