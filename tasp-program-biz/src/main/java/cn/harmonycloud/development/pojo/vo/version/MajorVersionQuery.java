package cn.harmonycloud.development.pojo.vo.version;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
@Data
public class MajorVersionQuery extends PageQuery {

    @ApiModelProperty("子系统id")
    @NotNull(message = "缺少参数：subsystemId")
    private Long subsystemId;

    @ApiModelProperty("版本状态")
    private Integer versionStatus;

    @ApiModelProperty("版本号")
    private String versionNumber;
}

