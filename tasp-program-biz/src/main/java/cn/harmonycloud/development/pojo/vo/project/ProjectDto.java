package cn.harmonycloud.development.pojo.vo.project;

import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/11 3:35 下午
 **/
@Data
public class ProjectDto implements Serializable {

    private static final long serialVersionUID = 3230946416934670245L;
    private Long projectId;
    private String projectName;
    private List<IssuesDto> issues;

}
