package cn.harmonycloud.development.pojo.dto.project;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class ProjectRequest {

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    /**
     * 搜索关键字
     */
    private String search;

    /**
     * 项目状态：null-全部，0-进行中，1-已完成
     */
    private Integer status;

    /**
     * 子系统id
     */
    @NotBlank(message = "子系统id不能为空")
    private Long subSystemId;
}
