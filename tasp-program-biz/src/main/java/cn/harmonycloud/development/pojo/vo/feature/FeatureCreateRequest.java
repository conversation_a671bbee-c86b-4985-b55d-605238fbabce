package cn.harmonycloud.development.pojo.vo.feature;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/21 8:57 上午
 **/
@Data
@ApiModel("特性创建模型")
public class FeatureCreateRequest {
    @NotNull(message = "特性类型不能为空")
    private Integer featureType;

    @NotEmpty(message = "标题不能为空")
    @Size(max = 100 , message = "标题限制长度最大为100字符")
    private String featureName;

    private String featureDesc;

    private Long subsystemId;

    @ApiModelProperty("分支模型")
    private String branchModel;

    private String branchDesc;

    private String completeData;

    private Long director;

    @NotEmpty(message = "创建来源不能为空")
    private String sourceBranch;

    @NotNull(message = "所属项目不能为空")
    private Long projectId;

    private List<Long> labels;

    private List<Long> taskIds;

}
