package cn.harmonycloud.development.pojo.vo.version;

import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/9 6:43 下午
 **/
@Data
public class UploadVersionProductVo implements Serializable {

    private List<DevopsRepository> dockerRepository;

    private List<DevopsRepository> rawRepository;

    private String dockerProductName;

    private String rawProductName;

    private String majorVersion;

    private String totalVersion;

    private String subCode;


}
