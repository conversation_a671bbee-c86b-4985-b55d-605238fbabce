package cn.harmonycloud.development.pojo.entity;

import cn.harmonycloud.development.pojo.SystemConstance;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 成员表
 * @TableName system_member
 */
@TableName(value ="version_management")
@Data
public class VersionManagement implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 子系统id
     */
    private Long subSystemId;
    /**
     * 大版本id
     */
    private Long majorVersionId;

    /**
     * 负责人id
     */
    private Long directorId;

    /**
     * 大版本号
     */
    private String versionNumber;

    /**
     * 子版本号
     */
    private String subVersionNumber;

    /**
     * totalVersionNumber,由大版本号，子版本号拼接得到
     */
    private String totalVersionNumber;

    /**
     * 补丁号
     */
    private String patchNumber;
    /**
     * 版本号类型 1-日期版本；2-自定义版本
     */
    private Integer versionType;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本状态：0-未开始；1-开发中；2-测试中；3-已发布
     */
    private Integer versionStatus;

    /**
     * 版本开闭状态，0版本开启，1版本关闭
     */
    private Integer switchStatus;

    private String updateArea;

    private String updateType;

    private Integer deleteStatus;

    private String tarList;

    private String yamlList;

    private String description;

    private LocalDateTime releaseTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public String getVersionString(){
        if(versionType == null || versionType == SystemConstance.VersionType.DATA_TIME){
            return versionNumber + patchNumber;
        }else {
            return versionNumber;
        }
    }
}