package cn.harmonycloud.development.pojo.dto.thgn;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 扩展的提交统计信息封装类
 * 继承自CommitStatistics，增加首次提交时间字段
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectCommitInfoDTO {

    private Date firstCommitTime;
    private String username;
    private String projectName;
    private int commitCount;



    /**
     * 更新首次提交时间
     * 如果当前首次提交时间为空或传入时间更早，则更新
     *
     * @param commitTime 提交时间
     */
    public void updateFirstCommitTime(Date commitTime) {
        if (this.firstCommitTime == null || commitTime.before(this.firstCommitTime)) {
            this.firstCommitTime = commitTime;
        }
    }
}