package cn.harmonycloud.development.pojo.dto.thgn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Gitlab提交统计扩展DTO
 * 继承自GitlabCommitCountDTO，增加了应用程序名称信息
 * <AUTHOR>
 */
@ApiModel(description = "Gitlab提交统计扩展DTO")
@EqualsAndHashCode(callSuper = true)
@Data
public class GitlabCommitInfoDTO extends GitlabCommitCountDTO {

    /**
     * 应用程序中文名称
     */
    @ApiModelProperty(value = "应用程序中文名称", example = "测试应用")
    private String programNameCn;


    /**
     * 应用程序英文名称
     */
    @ApiModelProperty(value = "应用程序英文名称", example = "test-app")
    private String programNameEn;


}
