package cn.harmonycloud.development.pojo.dto.autotest;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel(value = "自动化测试请求参数")
public class AutotestDTO {

    @ApiModelProperty("测试记录编号")
    private String testCode;

    @ApiModelProperty("环境测试状态:全部=0,待测试=1,测试中=2,测试通过=3,测试失败=4")
    private Integer testStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    private Integer pageNum = 1;
    private Integer pageSize = 10;
}
