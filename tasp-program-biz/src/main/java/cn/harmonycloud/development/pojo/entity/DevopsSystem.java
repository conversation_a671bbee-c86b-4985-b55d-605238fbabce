package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 系统
 * <AUTHOR>
 * @Date 2023/4/11 5:15 下午
 **/
@Data
@TableName(value ="devops_system")
public class DevopsSystem {

    // 系统id
    @TableId(type = IdType.AUTO)
    private Long id;
    // 系统名称
    private String subFullNameCn;
    // 系统编码
    private String sysCode;
    // 分支模型 GENERAL-普通   CONTROLLED-受控
    private String branchModel;
    // 负责人id
    @TableField("projectdirectorid")
    private Long projectDirectorId;
    // 系统简介
    private String sysDescCn;
    // 置顶排序
    private Integer topSort;
    // 逻辑删除标志位
    private Integer delFlag;
    // 创建人
    private Long createBy;
    // 创建时间
    private LocalDateTime createTime;
    // 更新人
    private Long updateBy;
    // 更新时间
    private LocalDateTime updateTime;
    //组织id
    private Long tenantId;

}
