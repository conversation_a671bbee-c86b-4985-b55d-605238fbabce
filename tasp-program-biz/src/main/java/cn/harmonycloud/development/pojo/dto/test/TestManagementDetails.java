package cn.harmonycloud.development.pojo.dto.test;

import cn.harmonycloud.development.pojo.dto.project.IssuesWithProjectDto;
import cn.harmonycloud.development.pojo.dto.web.WebFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/28 7:48 下午
 **/
@Data
@ApiModel("提测单详情返回参数实体")
public class TestManagementDetails implements Serializable {

    private static final long serialVersionUID = -7131299303761164414L;
    /**
     * 测试记录编号
     */
    @ApiModelProperty("测试单编号")
    private String testCode;

    @ApiModelProperty("系统名称")
    private String systemName;

    @ApiModelProperty("系统名称")
    private String systemId;

    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("子系统名称")
    private String subsystemName;

    @ApiModelProperty("提测环境")
    private String testEnv;

    @ApiModelProperty("虚拟机测试地址")
    private String testEnvVm;

    @ApiModelProperty("容器测试地址")
    private String testEnvContainer;

    @ApiModelProperty("测试负责人id")
    private String directorId;

    @ApiModelProperty("测试负责人名称")
    private String directorName;

    @ApiModelProperty("阶段环境id")
    private Long stageEnvId;

    @ApiModelProperty("实例id")
    private Long buildInstanceId;

    @ApiModelProperty("测试状态：1-待测试；2-测试中；3-测试通过，4-测试失败")
    private Integer testStatus;

    @ApiModelProperty("提测说明")
    private String description;

    @ApiModelProperty("测试结果说明")
    private String testResultDescription;

    @ApiModelProperty("手动测试结果")
    private Integer manualResult;

    @ApiModelProperty("手动测试报告")
    private List<WebFile> manualFile = new ArrayList<>();

    @ApiModelProperty("手动测试结果")
    private Integer autotestResult;

    @ApiModelProperty("自动测试报告")
    private List<WebFile> autotestFile = new ArrayList<>();

    @ApiModelProperty("提测人名称")
    private String createName;

    @ApiModelProperty("版本号")
    private String version = "-";

    private List<IssuesWithProjectDto> issues;

    private Boolean sqlUpdateFlag;

}
