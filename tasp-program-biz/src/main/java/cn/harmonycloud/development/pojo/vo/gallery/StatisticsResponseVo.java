package cn.harmonycloud.development.pojo.vo.gallery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 子系统工作台统计接口返回结果类
 * <AUTHOR>
 * @Date 2022/9/27 10:12 上午
 **/
@Data
@ApiModel("子系统工作台统计接口返回结果类")
public class StatisticsResponseVo {


    @ApiModelProperty("完成特性数")
    private Integer feature;

    @ApiModelProperty("代码评审次数")
    private Integer codeReview;

    @ApiModelProperty("发起测试数")
    private Integer test;

    @ApiModelProperty("流水线构建数")
    private Integer pipeline;

}
