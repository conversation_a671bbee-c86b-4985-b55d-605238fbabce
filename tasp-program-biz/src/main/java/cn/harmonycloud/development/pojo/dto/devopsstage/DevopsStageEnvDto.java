package cn.harmonycloud.development.pojo.dto.devopsstage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/10 10:51 上午
 **/
@Data
@ApiModel("阶段环境信息实体")
public class DevopsStageEnvDto {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("所属阶段id")
    private Long stageId;

    @ApiModelProperty("所属阶段pid")
    private Long stageDictId;

    @ApiModelProperty("所属阶段名称")
    private String stageName = "-";

    @ApiModelProperty("环境名称")
    private String envName;

    @ApiModelProperty("阶段环境拼接名称")
    private String stageEnvName;

    @ApiModelProperty("阶段环境编码")
    private String stageEnvCode;

    @ApiModelProperty("环境id")
    private Integer envId;

    @ApiModelProperty("部署环境名称")
    private String deployEnvName;

    @ApiModelProperty("部署类型")
    private Integer deployType;

    @ApiModelProperty("部署资源：容器资源")
    private Integer clusterId;

    @ApiModelProperty("部署资源名称：容器资源")
    private String clusterName = "";

    @ApiModelProperty("集群命名空间")
    private String namespace;

    @ApiModelProperty("部署资源：主机资源")
    private List<Integer> hostId = new ArrayList<>();

    @ApiModelProperty("部署资源名称：主机资源")
    private String hostsName = "";

    @ApiModelProperty("最后一次构建的流水线id")
    private Long lastBuildJob;

    private String lastBuildJobStr = "";

    @ApiModelProperty("通用制品库id")
    private Long rawRepoId;

    @ApiModelProperty("docker制品库id")
    private Long containerRepoId;

    @ApiModelProperty("最后一次构建的流水线版本")
    private Long lastBuildVersion;

    private String lastBuildVersionStr  = "";

    @ApiModelProperty("访问地址")
    private String address;

}
