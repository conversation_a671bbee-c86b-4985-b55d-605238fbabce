package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/25 4:51 下午
 **/
@Data
@TableName("sub_system_pipeline")
public class SubSystemPipeline implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 3809759082905607535L;
    private Long subSystemId;
    private Long pipelineJobId;
    private Integer viewType;
    private Long createBy;
    private LocalDateTime createTime;
}
