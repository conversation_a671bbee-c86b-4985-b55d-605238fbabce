package cn.harmonycloud.development.pojo.dto.thgn;

import lombok.Data;
import java.util.List;

/**
 * GitLab提交记录查询参数DTO
 *
 * <AUTHOR>
 */
@Data
public class GitlabCommitQueryDTO {
    /**
     * GitLab域名（如：gitlab.example.com）
     */
    private String gitlabDomain;

    /**
     * GitLab访问令牌
     */
    private String privateToken;

    /**
     * 项目ID列表
     */
    private List<Long> projectIds;

    /**
     * 开始时间（格式：yyyy-MM-dd'T'HH:mm:ss'Z'，如：2023-01-01T00:00:00Z）
     */
    private String startTime;

    /**
     * 结束时间（格式：yyyy-MM-dd'T'HH:mm:ss'Z'）
     */
    private String endTime;
}