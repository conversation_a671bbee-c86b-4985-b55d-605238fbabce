package cn.harmonycloud.development.pojo.dto.system;

import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemDto;
import cn.harmonycloud.pmp.model.entity.Tag;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 2:11 下午
 **/
@Data
@ApiModel("系统模型")
public class DevopsSystemDto {

    @ApiModelProperty("系统id")
    private Long id;

    @ApiModelProperty("系统名称")
    private String subFullNameCn;

    @ApiModelProperty("系统编码")
    private String sysCode;

    @ApiModelProperty("负责人id")
    private Long projectDirectorId;

    @ApiModelProperty("负责人名称")
    private String projectDirectorName;

    @ApiModelProperty("系统简介")
    private String sysDescCn;

    @ApiModelProperty("分组id")
    private Integer groupId;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("指定排序")
    private int topSort;

    private List<Tag> labels;

    private List<VersionSubsystemDto> versionSubsystemList;

}
