package cn.harmonycloud.development.pojo.dto.system;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/9/13
 **/
@Data
@ApiModel(value = "环境测试列表请求参数")
public class EnvTestDTO extends PageQuery {

    @ApiModelProperty("子系统id")
    @NotBlank(message = "子系统id不能为空")
    private Long subSystemId;

    @ApiModelProperty("测试记录编号")
    private String testCode;

    @ApiModelProperty("环境测试状态:全部=null,待测试=1,测试中=2,测试通过=3,测试失败=4")
    private Integer testStatus;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
