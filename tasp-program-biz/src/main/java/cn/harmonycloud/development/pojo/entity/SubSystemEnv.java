package cn.harmonycloud.development.pojo.entity;

import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName subSystemEnv
 * @Description
 * <AUTHOR>
 * @Date 2022/9/6 9:39 AM
 **/
@Data
@Builder
@TableName("sub_system_env")
@AllArgsConstructor
@NoArgsConstructor
public class SubSystemEnv extends BaseEntity {

    private Integer envId;

    private Long subSystemId;

}
