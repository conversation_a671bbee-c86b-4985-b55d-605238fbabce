package cn.harmonycloud.development.pojo.dto.thgn;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * 提交统计信息封装类
 * 用于存储用户提交次数和首次提交时间
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommitStatistics {
    private String username;
    private String projectId;
    private int commitCount;

    /**
     * 增加提交次数
     */
    public void incrementCommitCount() {
        this.commitCount++;
    }
}