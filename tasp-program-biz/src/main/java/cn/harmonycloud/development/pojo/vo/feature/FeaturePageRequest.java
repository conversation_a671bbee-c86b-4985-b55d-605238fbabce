package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.development.pojo.query.PageQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 分页查询特性信息请求参数  param
 * <AUTHOR>
 * @Date 2022/10/17 10:27 上午
 **/
@Data
public class FeaturePageRequest extends PageQuery implements Serializable {

    private static final long serialVersionUID = -8636267135883488192L;
    // 子系统id
    private Long subSystemId;
    // 系统id
    private Long systemId;
    // 特性类型
    private Integer featureType;

    // 查询类型 1- 查询我创建的， 2 - 查询我负责的
    private String queryType;

    private Long projectId;

    private String featureName;

    private String search; // 标签或编码

    private Integer featureStatus;

    private List<Long> labels;

    private boolean withLabel = false;

    private boolean withBranch = false;

    private boolean withSubsystemName = false;

    private boolean withSystemName = false;

    private Long directorId;
    private Long filterByVersionId;

}
