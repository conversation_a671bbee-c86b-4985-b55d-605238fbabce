package cn.harmonycloud.development.pojo.entity;

import cn.harmonycloud.common.core.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName SubSystemVariable
 * @Description
 * <AUTHOR>
 * @Date 2022/9/6 9:35 AM
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sub_system_variable")
public class SubSystemVariable extends BaseEntity {

    private Long eId;

    @NotBlank(message = "变量key不能为空")
    private String varKey;

    private String varValue;

    @NotBlank(message = "变量说明不能为空")
    private String description;

    private Boolean defaultFlag;

    private Integer relateId;

    public SubSystemVariable(Long eid, String key, String value, String description){
        this.eId = eid;
        this.varKey = key;
        this.varValue = value;
        this.description = description;
        this.defaultFlag = Boolean.TRUE;
    }

}
