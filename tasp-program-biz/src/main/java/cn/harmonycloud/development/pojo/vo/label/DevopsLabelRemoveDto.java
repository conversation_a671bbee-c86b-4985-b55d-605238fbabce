package cn.harmonycloud.development.pojo.vo.label;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/22 9:25 上午
 **/
@Data
@ApiModel("删除标签")
public class DevopsLabelRemoveDto implements Serializable {
    private static final long serialVersionUID = 1207729401896217704L;
    @NotEmpty(message = "缺少参数：类型编码")
    private String classificationCode;
    @NotNull(message = "缺少参数：实例id")
    private Long instanceId;
    @NotNull(message = "缺少参数：标签id")
    private Long labelId;

}
