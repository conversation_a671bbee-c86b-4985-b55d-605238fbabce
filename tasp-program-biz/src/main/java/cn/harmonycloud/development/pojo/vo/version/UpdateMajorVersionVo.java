package cn.harmonycloud.development.pojo.vo.version;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.Size;
@Data
public class UpdateMajorVersionVo {
    @ApiModelProperty("大版本Id")
    private Long id;

    @ApiModelProperty("负责人Id")
    private Long directorId;

    @ApiModelProperty("简介")
    @Size(max = 1000, message = "描述长度过长，最大长度为1000")
    private String description;

    @ApiModelProperty("版本状态：0-未开始；1-开发中；2-测试中；3-已发布")
    private Integer versionStatus;
}
