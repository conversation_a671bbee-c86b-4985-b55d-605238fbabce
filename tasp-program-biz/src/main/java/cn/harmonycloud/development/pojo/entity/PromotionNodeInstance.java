package cn.harmonycloud.development.pojo.entity;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PromotionNodeInstance {
    private Long id;
    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略实例
     */

    private Long strategyInstance;

    /**
     * 节点id
     */
    private Long nodeId;
    /**
     * 下一个节点id
     */
    private Long nextInstanceId;
    /**
     * 制品id
     */
    private Long productId;

    /**
     * 晋级状态：0-未晋级；；2-晋级中；3-晋级成功；4-晋级失败
     */
    private Integer promotionStatus ;

    /**
     * 1-已入库；0-未入库
     */
    private Integer productStatus ;
    /**
     * 配置库id
     */
    private Long repoId;

    /**
     * 配置库名称
     */
    private String repoName;
    /**
     * 步骤顺序
     */
    private Integer sort;
    /**
     * 晋级实例id
     */
    private Long promotionInstanceId;
    /**
     * 晋级失败原因
     */
    private String errMsg;
    /**
     * 晋级开始时间
     */
    private LocalDateTime startTime;
    /**
     * 晋级结束时间
     */
    private LocalDateTime endTime;

}
