package cn.harmonycloud.development.pojo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 开源组件基线查询参数
 */
@Data
@ApiModel(description = "开源组件基线查询参数")
public class OpenSourceComponentBaselineQuery {
    @ApiModelProperty(value = "组件名称模糊查询", example = "Spring Boot")
    private String name;

    @ApiModelProperty(value = "分类精确查询", example = "框架")
    private String category;

    @ApiModelProperty(value = "版本模糊查询", example = "2.7")
    private String version;

    @ApiModelProperty(value = "负责人模糊查询", example = "张三")
    private String manager;

    private Integer pageNo;

    private Integer pageSize;
}