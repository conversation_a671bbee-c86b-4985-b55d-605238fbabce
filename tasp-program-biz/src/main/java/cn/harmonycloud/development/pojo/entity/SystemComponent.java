package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@TableName("system_component")
public class SystemComponent {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long systemId;
    private String component;
    private String componentKey;
    private String keyType;
    private Long createBy;
    private LocalDateTime createTime;
    private Integer delFlag;

    @Override
    public boolean equals(Object o){
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SystemComponent that = (SystemComponent) o;
        return StringUtils.equals(component, that.component) && StringUtils.equals(componentKey, that.componentKey);
    }
    @Override
    public int hashCode(){
        return Objects.hash(component, componentKey);
    }
}
