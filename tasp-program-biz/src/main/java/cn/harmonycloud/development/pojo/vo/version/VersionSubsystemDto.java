package cn.harmonycloud.development.pojo.vo.version;

import cn.harmonycloud.development.outbound.api.dto.promotion.ProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/27 9:21 上午
 **/
@Data
public class VersionSubsystemDto {

    @ApiModelProperty("版本id")
    private Long versionId;

    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("系统id")
    private Long systemId;

    @ApiModelProperty("负责人id")
    private Long versionDirectorId;

    private String versionDescription;

    /**
     * 子系统中文全称
     */
    private String fullNameCn;

    /**
     * 子系统编码
     */
    private String subCode;

    /**
     * 子系统描述
     */
    private String subDescCn;

    /**
     * 负责人
     */
    @ApiModelProperty("子系统负责人id")
    private Long techDirectorId;

    @ApiModelProperty("子系统负责人名称")
    private String techDirectorName;

    /**
     * 语言
     */
    private String technology;


    @ApiModelProperty("版本负责人名称")
    private String versionDirectorName;

    @ApiModelProperty("大版本号")
    private String versionNumber;

    @ApiModelProperty("版本号类型 1-日期版本；2-自定义版本")
    private Integer versionType;

    @ApiModelProperty("制品列表")
    private List<DevopsProductMetadataDto> products = new ArrayList<>();


//    @ApiModelProperty("版本状态：0-未开始；1-开发中；2-测试中；3-已发布")
//    private Integer versionStatus;

}
