package cn.harmonycloud.development.pojo.dto.devopsstage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 子系统阶段配置列表实体
 * <AUTHOR>
 * @Date 2023/2/7 10:37 上午
 **/
@Data
@ApiModel("子系统阶段配置列表实体")
public class DevopsStageConfigDto implements Serializable {

    private static final long serialVersionUID = -5109872882568692223L;
    private Long id;
    @ApiModelProperty("步骤id")
    private Long stageDictId;
    @ApiModelProperty("环境id")
    private Integer envId;
    @ApiModelProperty("通用仓库id")
    private Long rawRepoId;
    @ApiModelProperty("容器仓库id")
    private Long containerRepoId;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("简介")
    private String description;
    @ApiModelProperty("准入分支")
    private List<Integer> accessFeatureStatus;

}
