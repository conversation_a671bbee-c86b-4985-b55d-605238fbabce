package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

import static cn.harmonycloud.constants.DevelopmentConstance.StageType.DEV;

/**
 * 特性关联分支表
 * @TableName feature_branch
 */
@TableName(value ="feature_branch")
@Data
public class FeatureBranch implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分支模型
     */
    private String branchModel;

    /**
     * 受控分支
     */
    private String branchName;

    /**
     * 开发分支
     */
    private String branchDevName;

    /**
     * 代码库名称
     */
    private String codeName;

    /**
     * 特性id
     */
    private Long featureId;

    /**
     * 子系统id
     */
    private Long subSystemId;

    /**
     * 创建时的来源ref
     */
    private String sourceRef;

    /**
     * 创建时commit
     */
    private String sourceCommit;

    /**
     * 清理时受控分支commit
     */
    private String clearCommit;


    /**
     * 清理时开发分支commit
     */
    private String clearDevCommit;

    /**
     * 清理时开发分支commit
     */
    private Boolean isClear;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 删除标志，0正常，1逻辑删除
     */
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public String getBranch(Integer envType){
        if (envType == DEV) {
            return branchDevName;
        }
        return branchName;
    }
}