package cn.harmonycloud.development.pojo.vo.pipeline;

import lombok.Data;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/7/19 12:01 下午
 **/
@Data
public class PipelineCreateRequest implements Serializable {

    private static final long serialVersionUID = 1591995577131246875L;

    private Long subSystemId;
    private Long templateId;
    private String language = "java";
    private String type = "default";
    private Integer envId;

}
