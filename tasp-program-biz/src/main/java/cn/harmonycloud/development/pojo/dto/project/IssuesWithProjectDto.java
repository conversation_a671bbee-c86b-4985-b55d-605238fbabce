package cn.harmonycloud.development.pojo.dto.project;

import cn.harmonycloud.project.model.dto.UserDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/1 1:37 下午
 **/
public class IssuesWithProjectDto {
    @ApiModelProperty("工作项id")
    private Long id;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("项目id")
    private String projectName;
    @ApiModelProperty("工作项编号")
    private String code;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项类型id")
    private Long issuesClassicId;
    @ApiModelProperty("工作项类型名称")
    private String issuesClassicName;
    @ApiModelProperty("状态名称")
    private String status;
    @ApiModelProperty("迭代名称")
    private String sprint;
    @ApiModelProperty("负责人")
    private List<UserDTO> ownerList;

    public IssuesWithProjectDto() {
        // 默认构造方法
    }

    public String getProjectName() {
        return this.projectName;
    }

    public void setProjectName(String projectName){
        this.projectName = projectName;
    }

    public Long getId() {
        return this.id;
    }

    public Long getProjectId() {
        return this.projectId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Long getIssuesClassicId() {
        return this.issuesClassicId;
    }

    public String getIssuesClassicName() {
        return this.issuesClassicName;
    }

    public String getStatus() {
        return this.status;
    }

    public String getSprint() {
        return this.sprint;
    }

    public List<UserDTO> getOwnerList() {
        return this.ownerList;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectId(final Long projectId) {
        this.projectId = projectId;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setIssuesClassicId(final Long issuesClassicId) {
        this.issuesClassicId = issuesClassicId;
    }

    public void setIssuesClassicName(final String issuesClassicName) {
        this.issuesClassicName = issuesClassicName;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public void setSprint(final String sprint) {
        this.sprint = sprint;
    }

    public void setOwnerList(final List<UserDTO> ownerList) {
        this.ownerList = ownerList;
    }


}
