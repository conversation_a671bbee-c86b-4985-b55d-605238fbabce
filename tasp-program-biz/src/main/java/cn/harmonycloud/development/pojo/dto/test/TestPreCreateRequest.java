package cn.harmonycloud.development.pojo.dto.test;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/28 2:17 下午
 **/
@Data
@ApiModel("发起提测获取提测内容数据")
public class TestPreCreateRequest implements Serializable {

    private static final long serialVersionUID = 399944500448127605L;
    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("阶段环境id")
    private Long stageEnvId;

    @ApiModelProperty("流水线jobid")
    private Long jobId;

    @ApiModelProperty("版本id；非必填")
    private Long versionId;
}
