package cn.harmonycloud.development.pojo.vo.feature;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/2 3:56 下午
 **/
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FeatureListRequest implements Serializable {

    private static final long serialVersionUID = -6936128343826860915L;
    private Long director;
    private String title;
    private Integer featureStatus;
    private Long subSystemId;
    private Long projectId;
    private List<Long> ids;
    private List<Long> labels;
    private String startTime;
    private String endTime;

}
