package cn.harmonycloud.development.pojo.dto.development;

import cn.harmonycloud.development.pojo.query.PageQuery;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/10 3:22 下午
 **/
@Data
public class QueryFeatureRequest extends PageQuery implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 5186949254585327150L;
    private Long stageEnvId;
    private Long projectId;
    private String branch;
    private Integer featureStatus;
    private Long director;
    private String featureName;
    private Long versionId;

}
