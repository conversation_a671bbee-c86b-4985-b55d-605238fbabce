package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 特性收藏表
 * <AUTHOR>
 * @Date 2022/8/17 10:00 上午
 **/
@Data
@TableName("feature_star")
public class FeatureStar implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 7144091199170186184L;
    @TableId(type = IdType.AUTO)
    private Long id;

    // 特性id
    private Long featureId;

    // 收藏人
    private Long userId;

    // 收藏时间
    private LocalDateTime createTime;

    private Long subSystemId;

}
