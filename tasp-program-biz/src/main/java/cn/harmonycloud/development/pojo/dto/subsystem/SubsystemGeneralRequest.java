package cn.harmonycloud.development.pojo.dto.subsystem;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/19 11:24 上午
 **/
@Data
@ApiModel("子系统列表查询参数")
public class SubsystemGeneralRequest {

    private Long systemId;

    private String fullNameCn;

    private Long directorId;

    private String version;

    private Boolean resource = true;

    private Boolean filterGitlab = false;

    private Boolean withGitlab = false;

    private Boolean withMergeStatistic = false;

    private Boolean withVersion = false;

    private Boolean filterByVersion = false;

    private List<Long> ids;

    private Long projectId;


}
