package cn.harmonycloud.development.pojo;

public interface SystemConstance {

    Integer IS_DELETE = 1;
    Integer NOT_DELETE = 0;
    String COMMA = ",";
    String OAUTH_TOKEN_HEADER  = "Authorization";
    String OAUTH_TENANT_HEADER = "Amp-Organ-Id";
    String AMP_APP_ID          = "Amp-App-Id";
    String AMP_APP_CODE        = "Amp-App-CODE";
    String SCM_TOKEN        = "X-SCM-TOKEN";

    interface ComponentType {
        String KUBERNETES = "KUBERNETES";
        String GITLAB = "GITLAB";
        String DOCKER = "DOCKER";
    }

    interface MergeType {
        Integer MERGE_TASK = 1;
        Integer MERGE_REQUEST = 2;
    }

    interface VersionType {
        Integer DATA_TIME = 1;
        Integer CUSTOMS = 2;
    }

    interface AmpResourceTypeCode {
        String SYSTEM = "system";
        String SUBSYSTEM = "subsystem";
        String GITLAB = "gitlab";
        String GITLAB_GROUP = "group";
        String PIPELINE_JOB = "pipeline";
    }

    interface SystemRoleCode {
        String SYS_ADMIN = "sys_admin";
        String SYS_MEMBER = "sys_member";
    }

    interface SystemDictSubject {
        String BASE_BRANCH = "BASE_BRANCH"; // 基础分支
        String DEFAULT_STRATEGY = "DEFAULT_STRATEGY"; // 默认晋级策略
        String START_PARAM = "START_PARAM"; // 启动变量
        String DEVOPS_STAGE = "DEVOPS_STAGE"; // 研发阶段
        String FILTER_START_PARAM = "FILTER_START_PARAM"; // 启动参数
        String FEATURE_STATUS = "FEATURE_STATUS"; // 特性状态
        String DEFAULT_DEVOPS_STAGE = "DEFAULT_DEVOPS_STAGE"; // 默认阶段
        String MAX_DEVOPS_STAGE = "MAX_DEVOPS_STAGE";
    }


    interface SubSystemRoleCode {
        String SUB_ADMIN = "subsys_admin";
        String SUB_DEVELOP = "subsys_develop";
        String SUB_TESTER = "subsys_tester";
        String SUB_TECHNICAL = "subsys_technical";
    }

    interface SystemRole {
        Long SYS_ADMIN = 7L;
        String SYS_ = "subsys_develop";
        String SUB_TESTER = "subsys_tester";
        String SUB_TECHNICAL = "subsys_technical";
    }

    interface VersionStatus {
        Integer NOT_START = 0;
        Integer DEV = 1;
        Integer TESTING = 2;
        Integer DEPLOYED = 3;
    }

    interface VersionComponent {
        String FEATURE = "FEATURE";
        String DEPLOY_CONFIG_REPO = "DEPLOY_CONFIG_REPO";
        String DEPLOY_CONFIG_ENV = "DEPLOY_CONFIG_ENV";
        String DEPLOY_CONFIG_COMMIT = "DEPLOY_CONFIG_COMMIT";
        String DEPLOY_CONFIG_FILE = "DEPLOY_CONFIG_FILE";
        String PRODUCT = "PRODUCT";
    }

    interface DefaultVersionNumber{
        String DEFAULT_VERSION_NUMBER = "01";
    }

    interface VersionTypeContance{
        Integer DATE_BASED = 1;
        Integer CUSTOM = 2;
    }

    interface SwitchStaus{
        Integer OPENED = 0;
        Integer CLOSED = 1;
    }
}
