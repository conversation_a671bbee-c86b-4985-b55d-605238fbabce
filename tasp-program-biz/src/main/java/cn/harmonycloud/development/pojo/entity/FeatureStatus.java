package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 特性测试状态表
 * @TableName feature_status
 */
@TableName(value ="feature_status")
@Data
public class FeatureStatus implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 特性id
     */
    private Long featureId;

    /**
     * 环境
     */
    private Object context;

    /**
     * 开发测试状态
     */
    private Integer developStatus;

    /**
     * 测试状态
     */
    private Integer testStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private Integer sort;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    private Long updateBy;

    /**
     * 删除标志，0正常，1逻辑删除
     */
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}