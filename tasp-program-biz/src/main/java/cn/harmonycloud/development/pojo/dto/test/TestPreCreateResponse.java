package cn.harmonycloud.development.pojo.dto.test;

import cn.harmonycloud.development.pojo.dto.project.IssuesWithProjectDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/28 2:20 下午
 **/
@Data
@ApiModel("发起测试，创建提测单返回参数实体")
public class TestPreCreateResponse {

    private Long buildInstanceId;

    private List<IssuesWithProjectDto> issues;
}
