package cn.harmonycloud.development.pojo.vo.repository;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/11 10:50 上午
 **/
@Data
@ApiModel("创建制品仓库请求实体")
public class CreateRepositoryRequest {

    @ApiModelProperty("系统id")
    @NotNull(message = "缺少必填字段：系统id")
    private Long systemId;

    @ApiModelProperty("制品库类型")
    @NotEmpty(message = "缺少必填字段：仓库类型")
    private String format;

    @ApiModelProperty("环境id")
    @NotNull(message = "缺少必填字段：环境")
    private Long envId;

    @ApiModelProperty("服务id")
    private Long configId;

    @ApiModelProperty("仓库名称")
    @NotEmpty(message = "缺少必填字段：仓库名称")
    private String repoName;

    @ApiModelProperty("组件仓库是否创建")
    @Schema(description = "关联仓库,true表示先建， false 表示关联已有")
    private Boolean componentCreate = true;

    @ApiModelProperty("仓库简介")
    private String description;
}
