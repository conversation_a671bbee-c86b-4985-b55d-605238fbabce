package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.google.common.base.Splitter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 子系统阶段表
 * <AUTHOR>
 * @Date 2023/2/6 4:29 下午
 **/
@TableName(value ="devops_stage")
@Data
public class DevopsStage {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 阶段名称
     */
    private String stageName;
    /**
     * 子系统id
     */
    private Long subsystemId;
    /**
     * 阶段字典id
     */
    private Long stageDictId;
    /**
     * 环境id
     */
    private Integer envId;
    /**
     * 通用制品库id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long rawRepoId;
    /**
     * docker制品库id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long containerRepoId;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 配置状态：0-不可配置；1-可配置；
     */
    private Integer configStatus;

    /**
     * 简介
     */
    private String description;

    /**
     * 准入分支
     */
    private  String accessFeatureStatus;

    public List<Integer> getAccessFeatureStatusList(){
        if(StringUtils.isEmpty(accessFeatureStatus)){
            return new ArrayList<>();
        }
        return Splitter.on(",").splitToStream(accessFeatureStatus).map(status -> Integer.parseInt(status)).collect(Collectors.toList());
    }
}
