package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.development.pojo.query.PageQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 2:17 下午
 **/
@Data
public class FeatureBranchRequestDto extends PageQuery implements Serializable {

    private static final long serialVersionUID = 8281704627907234855L;
    private Long subsystemId;
    private Long projectId;
    private String branchDevName;
    private String branchName;
    private Integer featureStatus;
    private Long director;
    private String featureName;
    private List<Integer> featureStatusList;
    private List<Long> featureIdsFilter; // 过滤id
    private List<Integer> featureStatusFilter;
}
