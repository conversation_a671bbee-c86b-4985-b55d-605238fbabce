package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("project_management")
public class ProjectManagement {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目状态 0-进行中；
     *        1-已完成
     */
    private Integer status;

    /**
     * 负责人
     */
    private Long directorId;

    /**
     * 项目管理编码
     */
    @TableField("project_id")
    private Long projectId;

    /**
     * 项目描述
     */
    @TableField("description")
    private String description;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 子系统id
     */
    @TableField("sub_system_id")
    private Long subSystemId;

    /**
     * 项目开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 项目结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("create_by")
    private Long createBy;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_by")
    private Long updateBy;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;
}

