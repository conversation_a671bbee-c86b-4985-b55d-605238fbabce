package cn.harmonycloud.development.pojo.dto.thgn;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 用于封装提交统计的键对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommitKey {
    private String username;
    private String projectId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommitKey commitKey = (CommitKey) o;
        return Objects.equals(username, commitKey.username) &&
                Objects.equals(projectId, commitKey.projectId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(username, projectId);
    }
}