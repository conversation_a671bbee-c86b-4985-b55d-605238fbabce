package cn.harmonycloud.development.pojo.vo.version;

import cn.harmonycloud.development.outbound.api.dto.coderepo.CodeCommit;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/13 4:50 下午
 **/
@Data
public class VersionDeployConfig {

    private Long versionId;
    private Long configId;
    private String configName;
    private String commitId;
    private String env;
    private CodeCommit codeCommit;

}
