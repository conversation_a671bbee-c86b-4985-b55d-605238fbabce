package cn.harmonycloud.development.pojo.dto.development;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 6:42 下午
 **/
@Data
@ApiModel("检修单分页数据实体")
public class OnlineDto implements Serializable {

    private static final long serialVersionUID = 6530240724755446375L;
    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("上机单编码")
    private String onlineCode;
    @ApiModelProperty("上机单标题")
    private String onlineTitle;
    @ApiModelProperty("审批状态")
    private Integer applyStatus;
    @ApiModelProperty("计划检修时间")
    private String onlineTime;
    @ApiModelProperty("检修状态")
    private Integer onlineStatus;
    @ApiModelProperty("申请人id")
    private Long applyUserId;
    @ApiModelProperty("申请人名称")
    private String applyUserName;
    @ApiModelProperty("申请时间")
    private LocalDateTime createTime;

}
