package cn.harmonycloud.development.pojo.dto.test;

import cn.harmonycloud.development.pojo.dto.web.WebFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/2 3:33 下午
 **/
@Data
@ApiModel("批量更新报告接口")
public class TestBatchModifyRequest implements Serializable {

    private static final long serialVersionUID = 978794753409974528L;
    @ApiModelProperty("测试单id列表")
    private List<Long> ids;

    @ApiModelProperty("手动测试结果：null表示不更新")
    private Integer manualResult;

    @ApiModelProperty("自动化测试结果：null表示不更新")
    private Integer autotestResult;

    @ApiModelProperty("手动测试报告：空数组表示不更新")
    private List<WebFile> manualReport;

    @ApiModelProperty("自动化测试报告：空数组表示不更新表示不更新")
    private List<WebFile> autotestReport;

}
