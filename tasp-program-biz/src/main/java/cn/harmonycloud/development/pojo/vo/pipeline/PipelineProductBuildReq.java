package cn.harmonycloud.development.pojo.vo.pipeline;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/30 10:23 上午
 **/
@Data
public class PipelineProductBuildReq implements Serializable {

    private Long subSystemId;

    private String versionNumber;

    private Long repoId;

    private String productName;

    /**
     * 1-覆盖：2-不覆盖
     */
    private int overridePolicy = 1;

}
