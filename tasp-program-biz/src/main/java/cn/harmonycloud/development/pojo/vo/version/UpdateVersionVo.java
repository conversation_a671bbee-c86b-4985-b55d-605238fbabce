package cn.harmonycloud.development.pojo.vo.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/29
 */
@Data
@ApiModel("更新版本信息")
public class UpdateVersionVo {

    @ApiModelProperty("主版本Id")
    private Long id;

    @ApiModelProperty("负责人Id")
    private Long directorId;

    @ApiModelProperty("简介")
    @Size(max = 1000, message = "描述长度过长，最大长度为1000")
    private String description;

    @ApiModelProperty("版本状态")
    private Integer versionStatus;
}
