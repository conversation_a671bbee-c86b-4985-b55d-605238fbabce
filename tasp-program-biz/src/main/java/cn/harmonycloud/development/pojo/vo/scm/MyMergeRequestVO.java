package cn.harmonycloud.development.pojo.vo.scm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MyMergeRequestVO {

    private String mergeId;

    private String serverId;

    private String gitlabId;

    private String mergeName;

    private String sourceBranch;

    private String targetBranch;

    private String createBy;

    private String createTime;

    private String status;

    private List<AssigneeVO> assignee;


}
