package cn.harmonycloud.development.pojo.vo.pipeline;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/7/18 4:14 下午
 **/
@Data
public class PipelinePageRequest implements Serializable {

    private static final long serialVersionUID = -4629873035069198088L;
    private Long subSystemId;
    private Long envId;
    private String label;
    private String jobName;
    private String draftStatus;
    private int pageNo = 1;
    private int pageSize = 10;
}
