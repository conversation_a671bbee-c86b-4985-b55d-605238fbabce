package cn.harmonycloud.development.pojo.vo.gallery;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 工作台-更新是否展示流水线接口请求类
 * <AUTHOR>
 * @Date 2022/10/17 4:59 下午
 **/
@Data
public class PipelineViewUpdateRequest implements Serializable {

    private static final long serialVersionUID = 4088206891146918188L;
    @ApiModelProperty("子系统id")
    private Long subSystemId;
    @ApiModelProperty("流水线jobid")
    private Long pipelineJobId;
    @ApiModelProperty("是否展示：0-不展示，1-展示")
    private boolean viewType;

}
