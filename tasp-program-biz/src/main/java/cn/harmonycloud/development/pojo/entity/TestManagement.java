package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.base.Splitter;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@TableName(value = "test_management")
public class TestManagement {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 测试记录编号
     */
    private String testCode;

    /**
     * 补丁号（拼接环境测试编号后）
     */
    private String patchNumber;

    /**
     * 测试环境
     */
    private String testEnv;

    /**
     * main环境为version_instance_id,其他环境为特性表id
     */
    private String componentIds;

    /**
     * 所属子系统id
     */
    private Long subSystemId;

    /**
     * 所属系统id
     */
    private Long systemId;

    /**
     * 阶段环境id
     */
    private Long stageEnvId;

    /**
     * 构建实例id
     */
    private Long buildInstanceId;

    /**
     * 测试负责人id
     */
    private Long directorId;

    /**
     * 提测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人id
     */
    private Long createBy;

    private Long updateBy;

    private Integer delFlag;

    /**
     * 环境测试状态:待测试=1,测试中=2,测试通过=3,测试失败=4
     */
    private Integer testStatus;

    /**
     * 测试环境（虚机）
     */
    private String testEnvVm;

    /**
     * 测试环境（容器）
     */
    private String testEnvContainer;

    /**
     * 测试描述
     */
    private String description;

    /**
     * 涉及sql更新标识：0-不更新，1-更新
     */
    private Integer sqlUpdateFlag;

    /**
     * 是否开启自动化测试:0-未开启,1-已开启
     */
    private Integer autotestFlag;

    /**
     * 测试结果：3-测试成功，4-测试失败
     */
    private Integer testResult;

    /**
     * 测试结果描述
     */
    private String testResultDescription;

    /**
     * 制品自动晋级：0-关闭，1-开启
     */
    private Integer promotionStatus;

    /**
     * 测试报告，多个测试报告用逗号分隔
     */
    private String testReportUrls;

    /**
     * 手动测试结果
     */
    private Integer manualResult;
    /**
     * 自动化测试结果
     */
    private Integer autotestResult;
    /**
     * 手动测试报告
     */
    private String manualReport;
    /**
     * 自动化测试报告
     */
    private String autotestReport;


    public List<Long> getManualReportIds(){
        if(manualReport == null || manualReport.length() == 0){
            return new ArrayList<>();
        }
        return   Splitter.on(",").splitToList(manualReport).stream().map(fid -> Long.parseLong(fid)).collect(Collectors.toList());
    }

    public List<Long> getAutotestReportIds(){
        if(autotestReport == null || autotestReport.length() == 0){
            return new ArrayList<>();
        }
        return   Splitter.on(",").splitToList(autotestReport).stream().map(fid -> Long.parseLong(fid)).collect(Collectors.toList());
    }


}
