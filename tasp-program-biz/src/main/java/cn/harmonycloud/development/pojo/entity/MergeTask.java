package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/24 11:36 上午
 **/
@TableName("scm_merge_task")
@Data
public class MergeTask implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -8131677077789397685L;
    private Long subSystemId;
    private String envCode;
    private Integer mergeType; // 1-分支合并器  2-合并请求
    private Long mergeTaskId;
    private LocalDateTime createTime;

}
