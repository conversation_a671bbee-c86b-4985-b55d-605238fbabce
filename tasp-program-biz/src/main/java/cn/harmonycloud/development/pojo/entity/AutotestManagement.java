package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 自动化测试表
 */
@Data
@TableName(value = "autotest_management")
public class AutotestManagement {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 自动化测试编号
     */
    private String code;

    /**
     * 测试单编号
     */
    private String testCodeIds;

    /**
     * 自动化测试描述
     */
    private String description;

    /**
     * 测试状态：1-待测试，2-测试中，5-测试结束
     */
    private Integer testStatus;

    /**
     * 测试负责人id
     */
    private Long directorId;

    /**
     * 提测时间
     */
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 创建人id
     */
    private Long createBy;

    private Long updateBy;

    private Integer delFlag;

}
