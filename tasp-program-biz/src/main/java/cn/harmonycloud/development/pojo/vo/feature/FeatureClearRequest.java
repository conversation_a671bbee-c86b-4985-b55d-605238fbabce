package cn.harmonycloud.development.pojo.vo.feature;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/14 1:43 下午
 **/
@Data
public class FeatureClearRequest implements Serializable {

    @NotEmpty
    @ApiModelProperty("功能分支id列表")
    private List<Long> ids;

    @ApiModelProperty("是否需要校验")
    private Boolean checkDelete = false;

    @ApiModelProperty("是否需要校验权限")
    private Boolean checkPermission = false;

}
