package cn.harmonycloud.development.pojo.vo.feature;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/9 1:48 下午
 **/
@Data
public class FeatureTaskQueryReq extends PageQuery {

    @ApiModelProperty("特性id")
    private Long featureId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("迭代")
    private Long sprintId;

    @ApiModelProperty("版本")
    private Long versionId;

    @ApiModelProperty("负责人id列表")
    private List<Long> principalIdList;

    @ApiModelProperty("工作项类型")
    private Long issuesTypeId;

    @ApiModelProperty("状态")
    private Long statusId;

    @ApiModelProperty("优先级")
    private Long priorityId;
}
