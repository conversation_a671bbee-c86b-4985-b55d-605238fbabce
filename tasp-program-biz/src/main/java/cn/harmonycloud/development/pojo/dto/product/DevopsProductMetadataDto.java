package cn.harmonycloud.development.pojo.dto.product;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/12 6:29 下午
 **/
@Data
public class DevopsProductMetadataDto {

    private Long id;

    /**
     * 制品名称
     */
    private String name;

    /**
     * 仓库id
     */
    private Long repoId;

    /**
     * 仓库名称
     */
    private String repoName;

    /**
     * 制品格式
     */
    private String format;

    /**
     * 路径
     */
    private String path;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 版本
     */
    private String version;
    /**
     * 流水线jobId
     */
    private Long jobId;
    /**
     * 构建id
     */
    private Long buildId;
    /**
     * 特性名称
     */
    private String featureName;
    /**
     * 特性分支
     */
    private String featureBranch;
    /**
     * 合并后分支
     */
    private String mergeBranch;
    /**
     * 流水线名称
     */
    private String pipelineName;
    private Integer source;
    private String testManagement;
    private String codeScan; // json类型
    private String commitId;
    private String systemId;
    private String subsystemId;
    private String downloadPath;
    private String productVersion;
    private String versionTimestamp;
    private Long createBy;
    private Long configId;

}
