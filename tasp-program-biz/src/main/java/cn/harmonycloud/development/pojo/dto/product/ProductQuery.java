package cn.harmonycloud.development.pojo.dto.product;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/21 9:11 上午
 **/
@Data
public class ProductQuery implements Serializable {

    private Long buildId;
    private List<Long> repoIds;
    private Long systemId;
    private Long subsystemId;
    private String version;
    private String format;
    private String productName;
    private int pageNo;
    private int pageSize;
}
