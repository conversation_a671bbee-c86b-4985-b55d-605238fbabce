package cn.harmonycloud.development.pojo.vo.repository;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/12 4:07 下午
 **/
@Data
@ApiModel("系统查询制品分页请求参数")
public class ProductTreeQuery implements Serializable {
    private static final long serialVersionUID = -6163373925698004561L;

    @NotNull(message = "缺少参数：系统id")
    private Long systemId;
    //@NotNull(message = "缺少参数：子系统id")
    private Long subsystemId;
    private Long envId;
    private Long repoId;
    private String version;
    private String format;
    private String productName;

}
