package cn.harmonycloud.development.pojo.dto.test;

import cn.harmonycloud.development.pojo.dto.web.WebFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/1 3:50 下午
 **/
@ApiModel("提测单保存")
@Data
public class TestModifyRequest implements Serializable {

    private static final long serialVersionUID = 3526918053458069783L;
    @ApiModelProperty("提测单id")
    @NotNull(message = "提测单id不能为空")
    private Long id;

    @ApiModelProperty("测试负责人id")
    @NotNull(message = "负责人id不能为空")
    private Long directorId;

    @ApiModelProperty("测试结果")
    @NotNull(message = "测试结果不能为空")
    private Integer testStatus;

    @ApiModelProperty("测试结果描述")
    private String testResultDescription;

    @ApiModelProperty("是否涉及sql更新")
    @NotNull(message = "sql更新状态为空")
    private Boolean sqlUpdateFlag;

    @ApiModelProperty("手动测试结果")
    private Integer manualResult;

    @ApiModelProperty("自动化测试结果")
    private Integer autotestResult;

    @ApiModelProperty("手动测试报告")
    private List<WebFile> manualReport;

    @ApiModelProperty("自动化测试报告")
    private List<WebFile>  autotestReport;

}
