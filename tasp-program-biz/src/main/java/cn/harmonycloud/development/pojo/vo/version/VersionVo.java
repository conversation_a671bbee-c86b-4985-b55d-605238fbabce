package cn.harmonycloud.development.pojo.vo.version;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/22
 */
@Data
public class VersionVo {
    private String id;

    private String versionNumberTotal;

    private String directorName;

    private LocalDateTime createTime;

    private Integer versionStatus;

    private Integer versionType;

    private String updateArea;

    private String updateType;

    private Integer switchStatus;

    private List<String> tarList;

    private List<String> yamlList;
}
