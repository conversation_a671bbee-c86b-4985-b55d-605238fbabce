package cn.harmonycloud.development.pojo.vo.version;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/11 4:08 下午
 **/
@Data
@ApiModel("版本查询实体")
public class VersionQuery extends PageQuery {

    @ApiModelProperty("子系统id")
    @NotNull(message = "缺少参数：subsystemId")
    private Long subsystemId;

    @ApiModelProperty("版本号")
    private String versionNumber;

    @ApiModelProperty("版本状态：0-未开始；1-开发中；2-测试中；3-已发布")
    private Integer versionStatus;

}
