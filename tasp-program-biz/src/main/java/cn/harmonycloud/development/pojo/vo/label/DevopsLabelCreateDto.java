package cn.harmonycloud.development.pojo.vo.label;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/19 5:28 下午
 **/
@Data
@ApiModel("标签创建请求实体")
public class DevopsLabelCreateDto implements Serializable {

    private static final long serialVersionUID = -226049648233090931L;

    @ApiModelProperty("类型编码")
    @NotEmpty(message = "缺少参数：类型编码")
    private String classificationCode;

    @ApiModelProperty("实例id")
    @NotNull(message = "缺少参数：实例id")
    private Long instanceId;

    @NotNull(message = "缺少参数：标签id")
    @ApiModelProperty("标签id列表")
    private List<Long> labelIds;

}
