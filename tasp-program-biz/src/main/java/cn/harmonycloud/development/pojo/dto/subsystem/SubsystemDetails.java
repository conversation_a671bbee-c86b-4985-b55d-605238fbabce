package cn.harmonycloud.development.pojo.dto.subsystem;

import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/19 5:32 下午
 **/
@Data
public class SubsystemDetails {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("子系统中文全称")
    private String fullNameCn;

    @ApiModelProperty("子系统编码")
    private String subCode;

    @ApiModelProperty("子系统描述")
    private String subDescCn;

    @ApiModelProperty("系统id")
    private Long systemId;

    @ApiModelProperty("负责人")
    private Long techDirectorId;

    @ApiModelProperty("语言")
    private String technology;

    @ApiModelProperty("代码库id")
    private Integer gitlabId;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("代码仓库")
    private GitProjectDto codeRepo;

    @ApiModelProperty("配置库")
    private Long configId;

    @ApiModelProperty("配置库名称")
    private Long configName;

}
