package cn.harmonycloud.development.pojo.dto.devopsstage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 用于创建环境实体
 * <AUTHOR>
 * @Date 2023/2/10 11:35 上午
 **/
@Data
@ApiModel("创建环境实体模型")
public class DevopsStageEnvCreate implements Serializable {

    private static final long serialVersionUID = -2343128101908286875L;

    @ApiModelProperty("子系统id")
    @NotNull(message = "子系统id不能为空")
    private Long subsystemId;

    @ApiModelProperty("阶段id")
    @NotNull(message = "阶段id不能为空")
    private Long stageId;

    @ApiModelProperty("环境名称")
    @NotEmpty(message = "环境名称不能为空")
    private String envName;

    @ApiModelProperty("阶段环境编码")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,20}$", message = "编码必须由1-20位英文字母或数字组成")
    private String stageEnvCode;

    @ApiModelProperty("环境类型：1-虚拟机；2-物理机；4-k8s集群")
    @NotNull(message = "环境类型不能为空")
    private Integer deployType;

    @ApiModelProperty("容器资源id")
    private Integer clusterId;

    @ApiModelProperty("容器资源命名空间")
    private String namespace;

    @ApiModelProperty("主机资源id")
    private List<Long> hostId;

    @ApiModelProperty("环境地址")
    @Length(max = 200, message = "环境地址超出限制")
    private String address;

}
