package cn.harmonycloud.development.pojo.vo.version;

import lombok.Data;

import jakarta.validation.constraints.Size;

@Data
public class CreateVersionRequest {
    private static final long serialVersionUID = -2630742124280487737L;

    private Long subSystemId;

    private Long directorId;

    private Integer versionType = 2;

    private String versionNumberTotal;

    @Size(max = 1000, message = "描述长度过长，最大长度为1000")
    private String description;

}
