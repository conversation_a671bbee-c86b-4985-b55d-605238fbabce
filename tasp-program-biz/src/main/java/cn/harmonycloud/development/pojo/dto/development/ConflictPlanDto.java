package cn.harmonycloud.development.pojo.dto.development;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/23 1:41 下午
 **/
@Data
public class ConflictPlanDto implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -7913934046105571625L;
    private Long id;
    private String targetBranch = "-";
    private String sourceBranch = "-";
}
