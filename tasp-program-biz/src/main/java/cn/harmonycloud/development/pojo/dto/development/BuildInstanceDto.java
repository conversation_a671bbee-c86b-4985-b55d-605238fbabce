package cn.harmonycloud.development.pojo.dto.development;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.google.common.base.Splitter;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/23 3:13 下午
 **/
@Data
public class BuildInstanceDto {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 子系统id
     */
    private Long subsystemId;
    /**
     * 阶段实例id
     */
    private Long stageEnvId;
    /**
     * 版本id
     */
    private Long majorVersionId;
    /**
     * 版本id
     */
    private Long versionId;
    /**
     * 流水线jobId
     */
    private Long jobId;
    /**
     * buildId
     */
    private Long buildId;
    /**
     * 分之合并器id
     */
    private Long mergeId;
    /**
     * 分之合并实例id
     */
    private Long mergeTaskId;
    /**
     * 特性id列表 以逗号隔开
     */
    private String features;
    /**
     * 构建分支
     */
    private String buildBranch;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public List<Long> getFeatureId(){
        if(features == null || features.length() == 0){
            return new ArrayList<>();
        }
        return Splitter.on(",").splitToList(features).stream().map(i -> Long.parseLong(i)).collect(Collectors.toList());
    }

}
