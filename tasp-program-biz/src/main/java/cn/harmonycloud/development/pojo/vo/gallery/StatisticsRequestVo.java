package cn.harmonycloud.development.pojo.vo.gallery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 子系统工作台统计面板接口请求参数
 * <AUTHOR>
 * @Date 2022/9/27 10:06 上午
 **/
@Data
@ApiModel("子系统概览统计接口请求参数")
public class StatisticsRequestVo implements Serializable {

    private static final long serialVersionUID = -7325103259254421912L;
    /**
     * 子系统id
     * 必填
     */
    @ApiModelProperty("子系统id")
    private Long subSystemId;

    /**
     * 统计时间
     * 必填
     * example: 0-今日、1-本周、2-本月
     */
    @ApiModelProperty("时间范围:0-今日、1-本周、2-本月")
    private Integer time;

}
