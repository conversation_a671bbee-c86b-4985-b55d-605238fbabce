package cn.harmonycloud.development.pojo.dto.development;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 11:16 上午
 **/
@ApiModel("环境特性关系实体")
@Data
public class StageEnvFeatureRequest implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -726256268223348374L;
    private Long stageEnvId;
    private Long versionId;
    private List<Long> featureIds;
}
