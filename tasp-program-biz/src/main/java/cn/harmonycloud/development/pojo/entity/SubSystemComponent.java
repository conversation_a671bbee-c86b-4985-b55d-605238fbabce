package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/23 4:51 下午
 **/
@Data
@TableName("sub_system_component")
public class SubSystemComponent {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long subSystemId;
    private Long systemId;
    private String component;
    private String componentKey;
    private String keyType;
    private Long createBy;
    private LocalDateTime createTime;
    private Integer delFlag;
}
