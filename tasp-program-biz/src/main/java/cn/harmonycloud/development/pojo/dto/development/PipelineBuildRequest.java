package cn.harmonycloud.development.pojo.dto.development;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 5:26 下午
 **/
@Data
@ApiModel("流水线构建")
public class PipelineBuildRequest implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 3212264995013792338L;
    @ApiModelProperty("阶段环境id")
    @NotNull(message = "环境id不能为空")
    private Long stageEnvId;
    @ApiModelProperty("jobid")
    @NotNull(message = "jobId不能为空")
    private Long jobId;
    @ApiModelProperty("版本id")
    private Long versionId;
    private String runDescribe;
    @ApiModelProperty("版本id")
    private List<JenkinsFileStartParameter> startParams;

}
