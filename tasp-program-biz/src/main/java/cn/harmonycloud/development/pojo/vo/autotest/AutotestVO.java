package cn.harmonycloud.development.pojo.vo.autotest;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("自动化测试放回信息实体类")
public class AutotestVO {

    private Long id;

    private String testCode;

    private String mainSystem;

    private String subSystem;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime testTime;

    private String leader;

    private String tester;

    private String status;

    private String description;

}
