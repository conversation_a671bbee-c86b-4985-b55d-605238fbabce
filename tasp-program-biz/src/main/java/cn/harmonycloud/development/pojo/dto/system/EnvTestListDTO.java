package cn.harmonycloud.development.pojo.dto.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/15
 **/
@Data
public class EnvTestListDTO {


    @ApiModelProperty("测试记录编号")
    private String testCode;

    @ApiModelProperty("环境测试状态:待测试=1,测试中=2,测试通过=3,测试失败=4")
    private Integer testStatus;

    private String projectName;
    private String systemName;
    private String subSystemName;

//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    private Integer pageNum = 1;
    private Integer pageSize = 10;
}
