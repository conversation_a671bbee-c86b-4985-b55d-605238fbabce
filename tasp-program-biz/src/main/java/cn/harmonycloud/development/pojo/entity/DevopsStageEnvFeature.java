package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description 环境集成分支关系表
 * <AUTHOR>
 * @Date 2023/2/10 3:55 下午
 **/
@TableName(value ="devops_stage_env_feature")
@Data
public class DevopsStageEnvFeature {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long stageEnvId;
    private Long versionId;
    private Long featureId;

}
