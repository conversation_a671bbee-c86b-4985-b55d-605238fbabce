package cn.harmonycloud.development.pojo.vo.pipeline;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/25 4:57 下午
 **/
@Data
public class PipelineVo implements Serializable {

    private static final long serialVersionUID = 882892327183758944L;
    @ApiModelProperty("子系统id")
    private Long subSystemId;
    @ApiModelProperty("流水线jobid")
    private Long pipelineJobId;
    @ApiModelProperty("流水线名称")
    private String jobName;
    @ApiModelProperty("子系统id")
    private String status;
    @ApiModelProperty("是否展示：0-不展示 1-展示")
    private boolean viewType; //0-不展示 1-展示
}
