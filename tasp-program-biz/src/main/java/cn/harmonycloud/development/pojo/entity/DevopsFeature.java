package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 特性实体
 * <AUTHOR>
 * @Date 2023/3/24 3:27 下午
 **/
@Data
@TableName("devops_feature")
public class DevopsFeature implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String featureName; // 特性名称
    private String featureDesc; // 特性简介
    private Integer featureType; // 特性类型：0-特性，缺陷
    private Integer featureStatus; // 特性状态：0-开发中；1-已完成；2-测试中；3-已发布；4-测试我那成；5-已清理
    private Long subSystemId;// 子系统id
    private Long systemId;// 系统id
    @TableField("feature_project_id")
    private Long projectId; // 项目id
    private String completeData; // 预计完成时间 yyyy-MM-dd格式
    private Long director; // 负责人；
    private Integer featureNumber; // 1.8特性编码 分支名称提增序号
    /**
     * 分支模型
     * @param cn.harmonycloud.enums.BranchModelEnum
     */
    private String branchModel; // 分支模型
    private String branchDesc; // 特性分支自定义
    private String featureCode; // 特性编码
    private Integer delFlag;
    private Long createBy;
    private LocalDateTime createTime;
    private Long updateBy;
    private LocalDateTime updateTime;
    private Integer clearFlag; // 清理标志位  0-不做清理，1-等待清理，2-已清理
    private LocalDateTime releaseTime; // 版本发布时间

}
