package cn.harmonycloud.development.pojo.dto.development;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/17 3:06 下午
 **/
@Data
@ApiModel("发起检修请求实体")
public class OnlineRequest implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -1073600470352572031L;
    @NotNull(message = "子系统id不能为空")
    private Long subsystemId;

    @NotNull(message = "环境id不能为空")
    private Long stageEnvId;

    @NotNull(message = "版本id")
    private Long versionId;

    @NotNull(message = "jobId不能为空")
    private Long jobId;

}
