package cn.harmonycloud.development.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "major_version")
@Data
public class MajorVersion {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 子系统id
     */
    private Long subSystemId;

    /**
     * 负责人id
     */
    private Long directorId;

    /**
     * 大版本号
     */
    private String versionNumber;

    /**
     * 版本状态
     */
    @ApiModelProperty("版本状态：0-未开始；1-开发中；2-测试中；3-已发布")
    private Integer versionStatus;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 子版本号用于版本变更
     */
    private String subNumber;

    /**
     * 删除状态
     */
    private Integer delFlag;

    /**
     * 描述
     */
    private String description;
}
