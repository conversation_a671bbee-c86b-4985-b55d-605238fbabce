package cn.harmonycloud.development.pojo.dto.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/1 11:20 上午
 **/
@Data
@ApiModel("文件展示实体类")
public class WebFile implements Serializable {

    private static final long serialVersionUID = -6225661387158150003L;
    @ApiModelProperty("唯一id")
    private String uid;
    @ApiModelProperty("文件名称")
    private String name;
    @ApiModelProperty("下载地址")
    private String url;

    @ApiModelProperty("状态")
    private String status = "done";
}
