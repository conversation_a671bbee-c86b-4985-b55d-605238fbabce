package cn.harmonycloud.development.pojo.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/14 3:33 下午
 **/
@Data
@ApiModel("制品树形结构")
public class ProductTreeDto {

    @ApiModelProperty("类型：true-文件，false-文件夹")
    private boolean leaf;// 文件夹  文件
    @ApiModelProperty("类型：1-系统编码   2-版本   3-制品")
    private Integer business;
    @ApiModelProperty("名称")
    private String title;
    @ApiModelProperty("制品名称（携带仓库）")
    private String name;
    @ApiModelProperty("主键")
    private String key;
    @ApiModelProperty("制品信息")
    private DevopsProductMetadataDto product;
    @ApiModelProperty("子节点")
    private List<ProductTreeDto> children = new ArrayList<>();
}
