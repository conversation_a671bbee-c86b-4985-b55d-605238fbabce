package cn.harmonycloud.development.pojo.dto.subsystem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/19 4:45 下午
 **/
@Data
public class SubsystemUpdateRequest {

    @NotNull(message = "id为空")
    private Long id;

    @NotEmpty(message = "应用名称不能为空")
    private String fullNameCn;

    @ApiModelProperty("子系统简介")
    private String subDescCn;

    @ApiModelProperty("技术栈")
    private String technology;

    @ApiModelProperty("负责人")
    private Long techDirectorId;

}
