package cn.harmonycloud.development.pojo.dto.system;

import cn.harmonycloud.enums.BranchModelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 5:00 下午
 **/
@Data
public class SystemUpdateRequest {

    @ApiModelProperty("系统名称")
    @NotNull(message = "系统id不能为空")
    private Long id;

    @ApiModelProperty("系统名称")
    @NotEmpty(message = "系统名称不能为空")
    private String subFullNameCn;

    @ApiModelProperty("分支模型")
    private String branchModel;

    @ApiModelProperty("负责人id")
    private Long projectDirectorId;

    @ApiModelProperty("系统简介")
    private String sysDescCn;

    @ApiModelProperty("关联项目")
    private List<Long> projectIds;

    @ApiModelProperty("标签")
    private List<Long> labels;
}
