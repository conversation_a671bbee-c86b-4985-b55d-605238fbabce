package cn.harmonycloud.development.pojo.vo.pipeline;

import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/11 3:21 下午
 **/
@Data
@ApiModel("制品环境")
public class ProductConfigDTO {

    private Long envId;

    private String configEnvName;

    private String configEnvCommit;


    private List<DevopsProductMetadataDto> products;

}
