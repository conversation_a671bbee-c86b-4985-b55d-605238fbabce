package cn.harmonycloud.development.pojo.vo.feature;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/21 8:58 上午
 **/
@Data
public class FeatureUpdateRequest {

    private Long id;

    @NotEmpty(message = "标题不能为空")
    @Size(max = 100 , message = "标题限制长度最大为100字符")
    private String featureName;

    private Integer featureStatus;

    private String featureDesc;

    private String completeData;

    private Long director;


}
