package cn.harmonycloud.development.pojo.dto.development;

import cn.harmonycloud.development.pojo.dto.feature.FeatureTaskDTO;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 3:01 下午
 **/
@Data
@ApiModel("检修单实体")
public class OnlineDetails implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = -7454505796350404402L;
    private Long id;
    private List<FeatureTaskDTO> features = new ArrayList<>();
    private List<CheckListDto> checkLists;

    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("关联子系统列表")
    private List<Long> subsystemIds = new ArrayList<>();

    @ApiModelProperty("子系统名称（检修对象）")
    private String subsystemName;

    @ApiModelProperty("构建实例id")
    private Long buildInstanceId;

    @ApiModelProperty("检修单名称")
    private String onlineName;

    @ApiModelProperty("检修开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("检修结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("影响范围")
    private String influenceScope;

    @ApiModelProperty("检修原因")
    private String description;

    @ApiModelProperty("申请人id")
    private Long createBy;

    @ApiModelProperty("申请人名称")
    private String createByName;
}
