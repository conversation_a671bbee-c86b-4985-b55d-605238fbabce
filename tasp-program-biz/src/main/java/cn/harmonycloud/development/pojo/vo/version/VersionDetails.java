package cn.harmonycloud.development.pojo.vo.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/11 7:08 下午
 **/
@Data
@ApiModel("版本详情")
public class VersionDetails {

    @ApiModelProperty("id主键")
    private Long id;

    @ApiModelProperty("子系统id")
    private Long subSystemId;

    @ApiModelProperty("负责人id")
    private Long directorId;

    @ApiModelProperty("负责人名称")
    private String directorName;

    @ApiModelProperty("大版本id")
    private Long majorVersionId;

    @ApiModelProperty("大版本号")
    private String versionNumber;

    @ApiModelProperty("子版本号")
    private String subVersionNumber;

    @ApiModelProperty("拼接版本号")
    private String totalVersionNumber;

    @ApiModelProperty("版本号类型 1-日期版本；2-自定义版本")
    private Integer versionType;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建人姓名")
    private String createByName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("更新人姓名")
    private String updateByName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("版本状态：0-未开始；1-开发中；2-测试中；3-已发布")
    private Integer versionStatus;

    private String description;


}
