package cn.harmonycloud.development.pojo.dto.development;

import cn.harmonycloud.development.pojo.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 6:37 下午
 **/
@Data
@ApiModel("上线分页条件实体")
public class OnlineQuery extends PageQuery implements Serializable {

    private static final long serialVersionUID = -390993688007883006L;
    @ApiModelProperty("子系统id")
    private Long subsystemId;

    @ApiModelProperty("申请人id")
    private Long applicant;

    @ApiModelProperty("申请状态")
    private Integer applyStatus;

    @ApiModelProperty("检修状态")
    private Integer onlineStatus;

    @ApiModelProperty("标题或者编码")
    private String search;
}
