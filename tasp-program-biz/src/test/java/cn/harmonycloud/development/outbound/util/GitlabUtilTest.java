package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.enums.ExceptionCode;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.ProjectApi;
import org.gitlab4j.api.models.Project;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * GitlabUtil单元测试类
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GitlabUtilTest {

    private GitlabUtil gitlabUtil;

    @Mock
    private GitLabApi mockGitLabApi;

    @Mock
    private ProjectApi mockProjectApi;

    @Mock
    private Project mockProject;

    @Before
    public void setUp() {
        gitlabUtil = new GitlabUtil();
        // 设置测试用的配置值
        ReflectionTestUtils.setField(gitlabUtil, "gitUrl", "https://gitlab.example.com");
        ReflectionTestUtils.setField(gitlabUtil, "gitToken", "test-token");
    }

    @Test
    public void testCreateGitlabRepository_Success() throws GitLabApiException {
        // Given
        String repoUrl = "test-repo";

        // 使用spy来部分mock GitlabUtil
        GitlabUtil spyGitlabUtil = spy(gitlabUtil);
        doReturn(mockGitLabApi).when(spyGitlabUtil).getGitLabApi();

        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenReturn(mockProject);

        // When
        Project result = spyGitlabUtil.createGitlabRepository(repoUrl);

        // Then
        assertNotNull(result);
        assertEquals(mockProject, result);
        verify(mockProjectApi).createProject(repoUrl);
    }

    @Test
    public void testCreateGitlabRepository_GitLabApiException() throws GitLabApiException {
        // Given
        String repoUrl = "test-repo";
        String errorMessage = "API调用失败";
        GitLabApiException gitLabApiException = new GitLabApiException(errorMessage);

        // 使用spy来部分mock GitlabUtil
        GitlabUtil spyGitlabUtil = spy(gitlabUtil);
        doReturn(mockGitLabApi).when(spyGitlabUtil).getGitLabApi();

        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenThrow(gitLabApiException);

        // When & Then
        try {
            spyGitlabUtil.createGitlabRepository(repoUrl);
            fail("应该抛出GitException");
        } catch (GitException exception) {
            // 验证异常信息
            assertEquals(ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            assertTrue(exception.getMessage().contains("仓库创建失败"));
            assertTrue(exception.getMessage().contains(errorMessage));
        }

        verify(mockProjectApi).createProject(repoUrl);
    }

    @Test
    public void testCreateGitlabRepository_NullRepoUrl() throws GitLabApiException {
        // Given
        String repoUrl = null;

        // 使用spy来部分mock GitlabUtil
        GitlabUtil spyGitlabUtil = spy(gitlabUtil);
        doReturn(mockGitLabApi).when(spyGitlabUtil).getGitLabApi();

        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenThrow(new GitLabApiException("Invalid repository URL"));

        // When & Then
        try {
            spyGitlabUtil.createGitlabRepository(repoUrl);
            fail("应该抛出GitException");
        } catch (GitException exception) {
            // 验证异常信息
            assertEquals(ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            assertTrue(exception.getMessage().contains("仓库创建失败"));
        }

        verify(mockProjectApi).createProject(repoUrl);
    }

    @Test
    public void testCreateGitlabRepository_ProjectApiReturnsNull() throws GitLabApiException {
        // Given
        String repoUrl = "test-repo";

        // 使用spy来部分mock GitlabUtil
        GitlabUtil spyGitlabUtil = spy(gitlabUtil);
        doReturn(mockGitLabApi).when(spyGitlabUtil).getGitLabApi();

        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenReturn(null);

        // When
        Project result = spyGitlabUtil.createGitlabRepository(repoUrl);

        // Then
        assertNull(result);
        verify(mockProjectApi).createProject(repoUrl);
    }

    @Test
    public void testGetGitLabApi() {
        // When
        GitLabApi result = gitlabUtil.getGitLabApi();

        // Then
        assertNotNull(result);
        // 注意：由于GitLabApi构造函数会创建新实例，我们主要验证方法能正常执行
        // 在实际项目中，可能需要进一步验证GitLabApi的配置是否正确
    }
}
