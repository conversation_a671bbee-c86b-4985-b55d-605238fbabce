@startuml
'https://plantuml.com/sequence-diagram

autonumber

web -> 研发协同: 成员分配，roleId、userId、systemId
研发协同 -> 应用管理: roleId，sourceCode、targetCode
研发协同 -> 研发协同: 查询系统的管理分组：groupId
应用管理 -> 研发协同: 返回分组的角色：codeRoleId
研发协同 --> 代码库: 关联信息不为空时，groupId、codeRoleId，分配分组角色
代码库 --> 应用管理: 分配分组角色
代码库 -> 研发协同: 成功
研发协同 -> 应用管理: roleId、userId，分配系统角色
研发协同 -> 应用管理: systemId
应用管理 -> 研发协同: 有权限的子系统列表
研发协同 -> 研发协同: 查询所有待赋权的流水线
研发协同 -> 流水线: 赋权流水线
研发协同 -> web: 成功
@enduml