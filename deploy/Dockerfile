#FROM ***********/library/java:11-prod-hc-sky
FROM ************:8443/library/eclipse-temurin:11-jdk-arthas-skywalking
#FROM openjdk:11-arthas
ENV JAR_FILE=/home/<USER>/springboot-dockerfile.jar
ADD ./devops-development-biz/target/*.jar /home/<USER>/springboot-dockerfile.jar
EXPOSE 8080
COPY start.sh ./start.sh
COPY start-hc.sh ./start.sh
# start the script to run your application. Take note that there are several other optional environment variables supported
# for details, please see the start.sh.
# for details, please see the start-hc.sh.
ENTRYPOINT ["/bin/bash", "start.sh"]
