<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.harmonycloud</groupId>
        <artifactId>tasp-program-svc</artifactId>
        <version>2.4.0-SNAPSHOT</version>
    </parent>
    <groupId>cn.harmonycloud</groupId>
    <artifactId>tasp-program-common</artifactId>
    <version>2.4.0-SNAPSHOT</version>
    <name>program-common</name>
    <description>program-common</description>
    <properties></properties>
    <packaging>pom</packaging>
<modules>
    <module>common-core</module>
    <module>datasource-starter</module>
    <module>mybatis-starter</module>
    <module>openapi-starter</module>
    <module>redis-starter</module>
    <module>sequence-starter</module>
    <module>tenant-starter</module>
    <module>web-starter</module>
</modules>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
