package cn.harmony.sequence.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Snowflake 发号器属性
 */
@Data
@Component
public class SequenceSnowflakeProperties extends BaseSequenceProperties {

    /**
     * 数据中心ID，值的范围在[0,31]之间，一般可以设置机房的IDC[必选]
     */
    @Value("${project.dataCenterId:0}")
    private long dataCenterId;
    /**
     * 工作机器ID，值的范围在[0,31]之间，一般可以设置机器编号[非选]
     */
    @Value("${project.workerId:0}")
    private long workerId;
}
