package cn.harmonycloud.common.core.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * @Usage: 实体基类，封装通用字段，逻辑删除
 */
@Getter
@Setter
@ApiModel(value = "BaseEntity", description = "基础实体")
public class BaseEntity implements Serializable {

    @TableId
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 是否删除  1：已删除  0：正常
     */
    @TableLogic
    @ApiModelProperty(value = "删除标记,1:已删除,0:正常")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @ApiModelProperty(value = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "修改用户")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 检查列名是否是公共
     *
     * @param columnName
     * @return
     */
    public static boolean checkCommonColumn(String columnName) {
        Set<String> commonColumns = Sets.newHashSetWithExpectedSize(6);
        commonColumns.add("ID");
        commonColumns.add("DEL_FLAG");
        commonColumns.add("CREATE_TIME");
        commonColumns.add("CREATE_BY");
        commonColumns.add("UPDATE_TIME");
        commonColumns.add("UPDATE_BY");
        return commonColumns.contains(columnName.toUpperCase());
    }
}
