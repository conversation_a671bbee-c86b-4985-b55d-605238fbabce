package cn.harmonycloud.mybatis.base;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数据操作方法放在Repository中
 *
 * @param <M> BaseMapper实现类
 * @param <T> 实体对象
 */
public class BaseRepositoryImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements BaseRepository<T> {
    @Autowired
    public M mapper;

    /**
     * 根据对象每个属性搜索
     *
     * @param entity
     * @return
     */
    @Override
    public T getByExample(T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<>(entity);
        return mapper.selectOne(queryWrapper);
    }

    /**
     * 动态查询list
     *
     * @param query
     * @return
     */
    @Override
    public List<T> getList(BaseQuery<T> query) {
        Wrapper<T> queryWrapper =
                initQueryWrapper(query);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public Page<T> getListByPage(BaseQuery<T> query) {
        Wrapper<T> queryWrapper = initQueryWrapper(query);
        return mapper.selectPage(query.toPage(), queryWrapper);
    }

    @Override
    public boolean batchDeleteLogic(@NotEmpty List<Serializable> ids) {
        return super.removeByIds(ids);
    }

    /**
     * 构造初始化wrapper，spage为通用查询格式
     *
     * @param spage
     * @return 返回查询条件的封装 todo 将查询条件装配部分拆分出去
     */
    public QueryWrapper<T> initQueryWrapper(BaseQuery<T> spage) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>(spage.getSearch());
        if (ArrayUtils.isNotEmpty(spage.getAsc())) {
            spage.getAsc()[0] = StringUtils.underscoreName(spage.getAsc()[0]);
        }
        if (ArrayUtils.isNotEmpty(spage.getDesc())) {
            spage.getDesc()[0] = StringUtils.underscoreName(spage.getDesc()[0]);
        }

        /**
         * 设置between查询
         */
        if (!CollectionUtils.isEmpty(spage.getBetween())) {
            List<BetweenEntity> betweenEntities = spage.getBetween();
            for (int i = 0; i < betweenEntities.size(); i++) {
                BetweenEntity betweenEntity = betweenEntities.get(i);
                queryWrapper.between(StringUtils.underscoreName(betweenEntity.getBetweenColumn()), betweenEntity.getStartValue(), betweenEntity.getEndValue());
            }
        }

        /**
         * 设置like查询
         */
        if (!CollectionUtils.isEmpty(spage.getLike())) {
            spage.getLike().entrySet().forEach(entry -> queryWrapper.like(StringUtils.underscoreName(entry.getKey()), entry.getValue()));
        }
        return queryWrapper;
    }
}