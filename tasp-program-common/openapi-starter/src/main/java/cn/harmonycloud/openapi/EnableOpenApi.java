package cn.harmonycloud.openapi;

import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;

import java.lang.annotation.*;

/**
 * 开启 Open API Swagger (SpringDoc OpenAPI)
 * SpringDoc OpenAPI 会自动配置，无需额外的配置类
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public @interface EnableOpenApi {
}
