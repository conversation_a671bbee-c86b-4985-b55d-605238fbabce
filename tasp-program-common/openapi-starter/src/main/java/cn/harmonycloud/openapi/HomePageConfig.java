package cn.harmonycloud.openapi;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @Usage:
 * @Author: by xiaoyx
 * @Date: 2020/9/17
 */
public class HomePageConfig implements WebMvcConfigurer {
    @Value("${project.openapi.homepage.path:/}")
    private String swaggerUiPath;
    @Value("${project.openapi.viewName:redirect:/swagger-ui.html}")
    private String viewName;

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController(swaggerUiPath).setViewName(viewName);
        registry.setOrder(Ordered.HIGHEST_PRECEDENCE);
    }
}
