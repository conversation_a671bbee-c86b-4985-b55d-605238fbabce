package cn.harmonycloud.common.datasource;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * @Usage: 开启读写分离
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class})
@Import(MasterSlaveDataSourceAutoConfiguration.class)
public @interface EnableMasterSlaveDataSource {
}
