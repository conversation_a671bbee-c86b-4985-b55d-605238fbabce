# Nacos Configuration Troubleshooting Guide

## Problem
Error: `[Nacos Config] config[dataId=config-center:tasp-development-svc.yml, group=DEFAULT_GROUP] is empty`

## Root Cause Analysis
1. **Incorrect config import format**: The original `config-center:${spring.application.name}.yml` format is not standard
2. **Missing configuration in Nacos server**: The configuration file might not exist in Nacos
3. **Namespace/Group mismatch**: Configuration might be in different namespace or group
4. **Connection issues**: Application might not be able to connect to Nacos server

## Solution Applied

### 1. Fixed application.yml Configuration
```yaml
spring:
  config:
    import: optional:nacos:${spring.application.name}.yml
  cloud:
    nacos:
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        file-extension: yml
        namespace: ${NACOS_NS:dev}
        group: DEFAULT_GROUP
        name: ${spring.application.name}
        logging:
          default-config-enabled: false
```

### 2. Key Changes Made
- Removed `config-center:` prefix from import statement
- Added explicit `group: DEFAULT_GROUP` configuration
- Added explicit `name: ${spring.application.name}` configuration
- Removed redundant `shared-configs` section

## Verification Steps

### Step 1: Check Nacos Server Configuration
1. Access Nacos console: `http://${NACOS_HOST}:${NACOS_PORT}/nacos`
2. Login with credentials: `nacos/LUOcheng@384`
3. Navigate to "Configuration Management" → "Configurations"
4. Check if configuration exists:
   - **Data ID**: `tasp-development-svc.yml`
   - **Group**: `DEFAULT_GROUP`
   - **Namespace**: `dev` (or your configured namespace)

### Step 2: Create Missing Configuration
If the configuration doesn't exist, create it:
1. Click "+" to create new configuration
2. Set:
   - **Data ID**: `tasp-development-svc.yml`
   - **Group**: `DEFAULT_GROUP`
   - **Configuration Format**: `YAML`
   - **Configuration Content**: Add your application-specific configuration

### Step 3: Test Connection
Add debug logging to verify connection:
```yaml
logging:
  level:
    com.alibaba.nacos.client.config: DEBUG
    com.alibaba.cloud.nacos.client.config: DEBUG
```

## Common Configuration Examples

### Basic Nacos Configuration Template
```yaml
# Database configuration
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password

# Application specific settings
your-app:
  feature:
    enabled: true
  timeout: 30000
```

### Environment-Specific Configuration
For different environments, you can create:
- `tasp-development-svc.yml` (default)
- `tasp-development-svc-dev.yml` (development)
- `tasp-development-svc-prod.yml` (production)

## Alternative Solutions

### Option 1: Use Bootstrap Configuration
If you prefer bootstrap approach, add dependency:
```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-bootstrap</artifactId>
</dependency>
```

Then create `bootstrap.yml`:
```yaml
spring:
  application:
    name: tasp-development-svc
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LUOcheng@384}
        namespace: ${NACOS_NS:dev}
        file-extension: yml
```

### Option 2: Disable Nacos Config (Temporary)
If you want to temporarily disable Nacos config:
```yaml
spring:
  cloud:
    nacos:
      config:
        enabled: false
```

## Troubleshooting Commands

### Check Nacos Server Status
```bash
curl -X GET "http://${NACOS_HOST}:${NACOS_PORT}/nacos/v1/ns/operator/servers"
```

### Check Configuration via API
```bash
curl -X GET "http://${NACOS_HOST}:${NACOS_PORT}/nacos/v1/cs/configs?dataId=tasp-development-svc.yml&group=DEFAULT_GROUP&tenant=${NACOS_NS}"
```

## Next Steps
1. Restart your application after making these changes
2. Check application logs for successful Nacos config loading
3. Verify that your application can access the configuration values
4. Create the missing configuration in Nacos console if needed
