# MyBatis + Nacos 配置最终解决方案

## 问题
错误：`Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required`

**背景**：数据源配置在 Nacos 中管理，使用 application.yml（不使用 bootstrap.yml）

## 根本原因分析
1. **自动配置排除问题**：错误地排除了 `DataSourceAutoConfiguration`，导致 DataSource Bean 无法创建
2. **版本兼容性问题**：MyBatis Plus 和相关依赖版本与 Spring Boot 3.x 不兼容
3. **配置加载顺序**：需要确保 Nacos 配置在 MyBatis 初始化前加载

## 最终解决方案

### 1. 修复自动配置排除问题
**文件**：`EnableMasterSlaveDataSource.java`
```java
// 移除了有问题的排除配置
@Import(MasterSlaveDataSourceAutoConfiguration.class)
public @interface EnableMasterSlaveDataSource {
}
```

**文件**：`Application.java`
```java
// 移除了 DataSourceAutoConfiguration 排除
@EnableMasterSlaveDataSource
@SpringBootApplication
public class Application {
```

### 2. 正确的 Nacos 配置（使用 application.yml）
**文件**：`application.yml`
```yaml
spring:
  config:
    import: optional:nacos:${spring.application.name}.yml
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LUOcheng@384}
        namespace: ${NACOS_NS:dev}
        logging:
          default-config-enabled: false
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        file-extension: yml
        namespace: ${NACOS_NS:dev}
        group: DEFAULT_GROUP
        name: ${spring.application.name}
        logging:
          default-config-enabled: false
```

### 3. 更新依赖版本以支持 Spring Boot 3.x
**文件**：`mybatis-starter/pom.xml`
```xml
<properties>
    <mybatis-plus.version>3.5.9</mybatis-plus.version>
    <mysql.connector.version>8.0.33</mysql.connector.version>
    <jsqlparser.version>4.9</jsqlparser.version>
</properties>

<!-- 更新 MySQL 连接器为 Spring Boot 3.x 标准 -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <version>${mysql.connector.version}</version>
</dependency>
```

**文件**：`datasource-starter/pom.xml`
```xml
<properties>
    <druid.version>1.2.23</druid.version>
    <dynamic-ds.version>4.3.1</dynamic-ds.version>
    <jasypt.version>3.0.5</jasypt.version>
</properties>
```

## 关键配置说明

### Nacos 配置导入
- 使用 `spring.config.import: optional:nacos:${spring.application.name}.yml`
- 这确保在应用启动时从 Nacos 加载配置
- `optional:` 前缀表示如果 Nacos 不可用，应用仍可启动

### 预期的 Nacos 配置格式
您在 Nacos 中的配置应该包含类似以下内容：
```yaml
spring:
  datasource:
    druid:
      url: ******************************************************************************************************************************************************
      username: your-username
      password: your-password
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
```

## 版本更新摘要

| 组件 | 旧版本 | 新版本 | 原因 |
|------|--------|--------|------|
| Druid | 1.2.8 | 1.2.23 | Spring Boot 3.x 兼容性 |
| Dynamic DataSource | 3.2.1 | 4.3.1 | Spring Boot 3.x 支持 |
| MyBatis Plus | 3.5.7 | 3.5.9 | Spring Boot 3.x 兼容性 |
| MySQL Connector | mysql-connector-java | mysql-connector-j | Spring Boot 3.x 标准 |
| JSQLParser | 4.3 | 4.9 | MyBatis Plus 3.5.9 要求 |
| Jasypt | 2.1.1 | 3.0.5 | Spring Boot 3.x 兼容性 |

## 验证步骤

### 步骤 1：验证 Nacos 配置
1. 访问 Nacos 控制台：`http://***********:8848/nacos`
2. 检查配置是否存在：
   - **Data ID**：`tasp-development-svc.yml`
   - **Group**：`DEFAULT_GROUP`
   - **Namespace**：`dev`
3. 确认数据源配置存在

### 步骤 2：清理并重新构建
```bash
mvn clean install -DskipTests
```

### 步骤 3：启动应用
```bash
mvn spring-boot:run
```

### 步骤 4：检查日志
查找成功的加载序列：
```
INFO  - Located property source: [BootstrapPropertySource {name='bootstrapProperties-tasp-development-svc.yml'}]
INFO  - HikariPool-1 - Starting...
INFO  - HikariPool-1 - Start completed.
INFO  - SqlSessionFactory configured successfully
```

## 故障排除

### 如果 Nacos 配置未加载
1. 检查 Nacos 服务器连接性
2. 验证命名空间和组设置
3. 确保配置在 Nacos 控制台中存在
4. 启用调试日志：`logging.level.com.alibaba.nacos.client.config: DEBUG`

### 如果仍然找不到 DataSource
1. 验证 Nacos 数据源配置格式
2. 检查配置是否在正确的命名空间/组中
3. 确认没有本地配置覆盖 Nacos 配置

### 如果 MyBatis 仍然失败
1. 确保 DataSource Bean 已创建（检查日志）
2. 验证 mapper 扫描路径
3. 检查循环依赖

## 调试配置
如需故障排除，添加到 application.yml：
```yaml
logging:
  level:
    com.alibaba.nacos.client.config: DEBUG
    com.alibaba.cloud.nacos.client.config: DEBUG
    com.baomidou.dynamic.datasource: DEBUG
    org.mybatis: DEBUG
```

## 最佳实践
1. **保持本地配置最小化** - 让 Nacos 管理大部分配置
2. **使用环境变量** 用于 Nacos 连接参数
3. **在不同环境中测试配置加载**
4. **监控生产环境中的 Nacos 连接性**
