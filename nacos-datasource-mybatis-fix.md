# Nacos DataSource + MyBatis Configuration Fix

## Problem
Error: `Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required`

**Context**: DataSource configuration is managed in Nacos, not local application.yml

## Root Cause Analysis
1. **Configuration Loading Order**: <PERSON><PERSON><PERSON><PERSON> tries to initialize before Nacos configuration is loaded
2. **Missing Bootstrap Configuration**: Without bootstrap.yml, Nacos config may not load early enough
3. **Auto-Configuration Conflicts**: Exclusions were preventing proper DataSource bean creation
4. **Version Compatibility**: MyBatis Plus and dependencies not compatible with Spring Boot 3.x

## Solution Applied

### 1. Added Bootstrap Configuration
**File**: `bootstrap.yml` (NEW)
```yaml
spring:
  application:
    name: tasp-development-svc
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LU<PERSON>cheng@384}
        namespace: ${NACOS_NS:dev}
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
        timeout: 3000
        enabled: true
```

### 2. Enabled Bootstrap Dependency
**File**: `pom.xml`
```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-bootstrap</artifactId>
</dependency>
```

### 3. Simplified Application Configuration
**File**: `application.yml`
- Removed local datasource configuration (using Nacos instead)
- Removed duplicate Nacos config settings
- Kept only service discovery configuration

### 4. Fixed Auto-Configuration Issues
**File**: `EnableMasterSlaveDataSource.java`
```java
// Removed problematic exclusions
@Import(MasterSlaveDataSourceAutoConfiguration.class)
public @interface EnableMasterSlaveDataSource {
}
```

**File**: `Application.java`
```java
// Removed DataSourceAutoConfiguration exclusion
@SpringBootApplication
public class Application {
```

### 5. Updated Dependencies for Spring Boot 3.x
**File**: `mybatis-starter/pom.xml`
```xml
<properties>
    <mybatis-plus.version>3.5.9</mybatis-plus.version>
    <mysql.connector.version>8.0.33</mysql.connector.version>
    <jsqlparser.version>4.9</jsqlparser.version>
</properties>

<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <version>${mysql.connector.version}</version>
</dependency>
```

## Configuration Loading Order

### With Bootstrap Configuration:
1. **Bootstrap Phase**: `bootstrap.yml` loads Nacos configuration
2. **Nacos Config Load**: DataSource configuration loaded from Nacos
3. **Application Phase**: `application.yml` loads with DataSource available
4. **MyBatis Init**: SqlSessionFactory created with available DataSource

### Expected Nacos Configuration
Your Nacos configuration should include something like:
```yaml
spring:
  datasource:
    druid:
      url: *****************************************
      username: your-username
      password: your-password
      driver-class-name: com.mysql.cj.jdbc.Driver
      # ... other druid settings
```

## Key Changes Summary

### Configuration Strategy
- **Bootstrap First**: Use bootstrap.yml for Nacos configuration loading
- **No Local DataSource**: Rely entirely on Nacos for datasource configuration
- **Proper Order**: Ensure configuration loads before auto-configuration

### Dependency Updates
| Component | Old Version | New Version | Reason |
|-----------|-------------|-------------|---------|
| MyBatis Plus | 3.5.7 | 3.5.9 | Spring Boot 3.x compatibility |
| MySQL Connector | mysql-connector-java | mysql-connector-j | Spring Boot 3.x standard |
| JSQLParser | 4.3 | 4.9 | MyBatis Plus 3.5.9 requirement |

### Auto-Configuration Fixes
- Removed `DataSourceAutoConfiguration.class` exclusions
- Enabled bootstrap configuration loading
- Simplified annotation configurations

## Verification Steps

### Step 1: Verify Nacos Configuration
1. Access Nacos console: `http://***********:8848/nacos`
2. Check configuration exists:
   - **Data ID**: `tasp-development-svc.yml`
   - **Group**: `DEFAULT_GROUP`
   - **Namespace**: `dev`
3. Verify datasource configuration is present

### Step 2: Clean and Rebuild
```bash
mvn clean install -DskipTests
```

### Step 3: Start Application
```bash
mvn spring-boot:run
```

### Step 4: Check Logs
Look for successful loading sequence:
```
INFO  - Located property source: [BootstrapPropertySource {name='bootstrapProperties-tasp-development-svc.yml'}]
INFO  - The following profiles are active: dev
INFO  - HikariPool-1 - Starting...
INFO  - HikariPool-1 - Start completed.
INFO  - SqlSessionFactory configured successfully
```

## Troubleshooting

### If Nacos Config Not Loading
1. Check Nacos server connectivity
2. Verify namespace and group settings
3. Ensure configuration exists in Nacos console
4. Check bootstrap.yml syntax

### If DataSource Still Not Found
1. Verify Nacos datasource configuration format
2. Check if configuration is in correct namespace/group
3. Enable debug logging: `logging.level.com.alibaba.nacos.client.config: DEBUG`

### If MyBatis Still Fails
1. Ensure DataSource bean is created (check logs)
2. Verify mapper scanning paths
3. Check for circular dependencies

## Debug Configuration
Add to application.yml for troubleshooting:
```yaml
logging:
  level:
    com.alibaba.nacos.client.config: DEBUG
    com.alibaba.cloud.nacos.client.config: DEBUG
    org.springframework.boot.autoconfigure: DEBUG
    com.baomidou.dynamic.datasource: DEBUG
```

## Best Practices
1. **Always use bootstrap.yml** for Nacos configuration when using config center
2. **Keep local application.yml minimal** - let Nacos manage most configuration
3. **Use environment variables** for Nacos connection parameters
4. **Test configuration loading** in different environments
5. **Monitor Nacos connectivity** in production
